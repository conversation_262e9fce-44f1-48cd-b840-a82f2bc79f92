package adhoc.base

import adhoc.helper.AdhocHelper
import adhoc.mock.EventMockFactory
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.slf4j.LoggerFactory
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Shared
import spock.lang.Specification

/**
 * Base class for adhoc integration tests that provides common setup and cleanup functionality.
 * This class contains shared methods for:
 * - S3 bucket management (clearing contents)
 * - DynamoDB table management (clearing contents)
 * - Web3j mock setup
 * - Event stream and pending event setup
 */
abstract class BaseAdhocITSpec extends Specification {

	// Common test constants - will be initialized based on environment
	static String TEST_BUCKET = "abijson-local-bucket"
	static String EVENTS_TABLE = "local-Events"
	static String BLOCK_HEIGHT_TABLE = "local-BlockHeight"

	// Common shared fields
	@Shared
	DynamoDbClient dynamoDbClient

	@Shared
	S3Client s3Client

	// Non-shared Web3j mock - each test gets its own instance
	Web3j web3j = Mock(Web3j)

	// Non-shared Web3jCaller mock - each test gets its own instance
	Web3j web3jCaller = Mock(Web3j)

	@Shared
	ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2)

	// Non-shared log appender - each test gets its own instance
	ListAppender<ILoggingEvent> logAppender

	@Shared
	def loggingServiceLogger = LoggerFactory.getLogger(LoggingService.class) as Logger

	@Shared
	def abiParserLogger = LoggerFactory.getLogger(AbiParser.class) as Logger

	@Shared
	def abiTypeConverterLogger = LoggerFactory.getLogger(AbiTypeConverter.class) as Logger

	/**
	 * Common dynamic property configuration for all adhoc integration tests
	 */
	@DynamicPropertySource
	static void configureProperties(DynamicPropertyRegistry registry) {
		registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
		registry.add("eagerStart", { "false" })
	}

	/**
	 * Common setupSpec implementation for creating AWS resources
	 */
	def setupSpecCommon() {
		// Initialize AWS clients
		def localStackPort = AdhocHelper.getLocalStackPort()
		def endpoint = "http://localhost:${localStackPort}"

		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create(endpoint))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create(endpoint))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables and bucket
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
	}

	/**
	 * Common cleanupSpec implementation for closing AWS clients
	 */
	def cleanupSpecCommon() {
		dynamoDbClient?.close()
		s3Client?.close()
	}

	/**
	 * Common setup implementation for test preparation
	 */
	def setupCommon() {
		// Clear all S3 bucket contents completely
		clearS3Bucket()
		// Clear all DynamoDB table contents
		clearDynamoDBTables()
		// Setup web3j mock
		setupWeb3jMock()
		//Setup web3jCaller
		setupWeb3jCallerMock()
		// Create a new log appender for this test to avoid log pollution
		logAppender = new ListAppender<>()
		logAppender.start()
		abiParserLogger.addAppender(logAppender)
		loggingServiceLogger.addAppender(logAppender)
		abiTypeConverterLogger.addAppender(logAppender)
		AdhocHelper.resetRunningFlag()
	}

	/**
	 * Common cleanup implementation for test cleanup
	 */
	def cleanupCommon() {
		// Remove log appender to prevent log pollution between tests
		if (logAppender != null) {
			// Detach from all loggers that might be using this appender
			loggingServiceLogger.detachAppender(logAppender)
			logAppender.stop()
			logAppender = null
		}
		// Clear S3 bucket for next test
		clearS3Bucket()
		// Shut down the scheduler to stop mock event generation
		scheduler.shutdown()
		scheduler.awaitTermination(5, TimeUnit.SECONDS)
		// Recreate scheduler for next test
		scheduler = Executors.newScheduledThreadPool(2)
	}

	/**
	 * Setup Web3j mock using reflection to inject into Web3jConfig
	 */
	protected void setupWeb3jMock() {
		def field = Web3jConfig.class.getDeclaredField("web3j")
		field.setAccessible(true)
		field.set(getWeb3jConfig(), web3j)
		println("Web3j mock setup completed")
	}

	/**
	 * Setup Web3jCaller mock using reflection to inject into Web3jConfig
	 */
	protected void setupWeb3jCallerMock() {
		def field = Web3jConfig.class.getDeclaredField("web3jCaller")
		field.setAccessible(true)
		field.set(getWeb3jConfig(), web3jCaller)
		println("Web3jCaller mock setup completed")
	}

	/**
	 * Abstract method to get Web3jConfig instance - must be implemented by subclasses
	 */
	abstract Web3jConfig getWeb3jConfig()

	/**
	 * Setup event stream for Web3j mock
	 */
	protected void setUpEventStream(List<NewHeadsNotification> blocks) {
		def index = new AtomicInteger(0)

		// Defer execution until Flowable is subscribed
		def flowable = Flowable.defer( {
			def processor = PublishProcessor.<NewHeadsNotification> create()

			scheduler.scheduleWithFixedDelay({
				int i = index.getAndIncrement()
				if (i < blocks.size()) {
					processor.onNext(blocks.get(i))
				}
			}, 1, 2, TimeUnit.SECONDS)

			return processor
		})

		// Stub the method to return deferred Flowable
		web3j.newHeadsNotifications() >> flowable
	}

	/**
	 * Setup pending event for Web3j mock
	 */
	protected void setUpPendingEvent(List<EthLog.LogResult> resultList) {
		def mockRequest = Mock(Request)
		def mockLog = Mock(EthLog)

		web3j.ethGetLogs(_ as EthFilter) >> mockRequest
		mockRequest.send() >> mockLog
		mockLog.getLogs() >> resultList
	}

	/**
	 * Clear all contents from the test S3 bucket
	 */
	protected void clearS3Bucket() {
		try {
			String continuationToken = null
			boolean hasMoreObjects = true

			while (hasMoreObjects) {
				def listRequest = ListObjectsV2Request.builder()
						.bucket(TEST_BUCKET)
						.continuationToken(continuationToken)
						.build()

				def listResponse = s3Client.listObjectsV2(listRequest as ListObjectsV2Request)

				listResponse.contents().each { obj ->
					s3Client.deleteObject(DeleteObjectRequest.builder()
							.bucket(TEST_BUCKET)
							.key(obj.key())
							.build() as DeleteObjectRequest)
				}

				continuationToken = listResponse.nextContinuationToken()
				hasMoreObjects = (continuationToken != null)
			}

			def finalCheck = s3Client.listObjectsV2 {
				it.bucket(TEST_BUCKET)
			}
			if (finalCheck.contents().isEmpty()) {
				println("S3 bucket ${TEST_BUCKET} successfully cleared")
			} else {
				println("Warning: S3 bucket ${TEST_BUCKET} still contains ${finalCheck.contents().size()} objects")
			}
		} catch (Exception e) {
			println("Error clearing S3 bucket: ${e.message}")
			e.printStackTrace()
		}
	}

	/**
	 * Clear all DynamoDB tables used in tests
	 */
	protected void clearDynamoDBTables() {
		try {
			// Clear events table
			clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

			// Clear block height table
			clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
		} catch (Exception e) {
			println("Error clearing DynamoDB tables: ${e.message}")
			e.printStackTrace()
		}
	}

	/**
	 * Clear a specific DynamoDB table
	 */
	protected void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
		try {
			// Scan the table to get all items
			def scanRequest = ScanRequest.builder()
					.tableName(tableName)
					.build()

			def scanResponse = dynamoDbClient.scan(scanRequest as ScanRequest)

			// Delete each item
			scanResponse.items().each { item ->
				def keyMap = [:]
				keyAttributes.each { keyAttr ->
					if (item.containsKey(keyAttr)) {
						keyMap[keyAttr] = item[keyAttr]
					}
				}

				if (!keyMap.isEmpty()) {
					def deleteRequest = DeleteItemRequest.builder()
							.tableName(tableName)
							.key(keyMap as Map<String, AttributeValue>)
							.build()
					dynamoDbClient.deleteItem(deleteRequest as DeleteItemRequest)
				}
			}
		} catch (Exception e) {
			println("Error clearing DynamoDB table ${tableName}: ${e.message}")
		}
	}

	/**
	 * Creates mock NewHeadsNotifications for testing with flexible parameters
	 * @param startBlockNumber The starting block number in decimal format
	 * @param numberOfNotifications The number of NewHeadsNotification objects to create
	 * @return List of mock NewHeadsNotification objects with sequential block numbers
	 */
	protected def createMockNewHeadsNotifications(long startBlockNumber, int numberOfNotifications) {
		def notifications = []

		for (int i = 0; i < numberOfNotifications; i++) {
			def currentBlockNumber = startBlockNumber + i
			def hexBlockNumber = "0x" + Long.toHexString(currentBlockNumber)

			def notification = Mock(NewHeadsNotification)
			def params = Mock(NotificationParams)
			def result = Mock(NewHead)
			result.getNumber() >> hexBlockNumber

			notification.getParams() >> params
			params.getResult() >> result
			notifications.add(notification)
		}

		return notifications
	}

	/**
	 * Setup mock Web3j to return blocks with transactions and events
	 * This method must be called from within a Spock test class context
	 * @param timestamp for the mock block
	 * @param mockLogConfigs List of maps containing log configurations. Each map should have:
	 *                       - logType: Type of log to create (e.g., 'addProviderRole', 'addTokenByProvider')
	 *                       - txHash: Transaction hash for this log
	 *                       - blockNumber: (optional) Block number, defaults to 1000
	 */
	protected def setupMockWeb3jWithEvents(List<Map> mockLogConfigs = null, Long timestamp = 1750606026L) {
		// Default configuration if none provided (backward compatibility)
		if (mockLogConfigs == null) {
			mockLogConfigs = [
				[logType: 'addProviderRole', txHash: '0xabc123'],
				[logType: 'addTokenByProvider', txHash: '0xdef456']
			]
		}

		// Group configs by block number
		def configsByBlock = mockLogConfigs.groupBy { it.blockNumber != null ? it.blockNumber : 1000L }

		def txHashCounter = 134
		def logIndexCounter = 0
		def mockRequestsByBlock = [:]

		// Create mock for each block
		configsByBlock.each { blockNumber, configs ->
			def mockRequest = Mock(Request)
			def mockEthBlock = Mock(EthBlock)
			def mockBlock = Mock(EthBlock.Block)
			def mockTxResults = []

			configs.each { config ->
				def txHashPrefix = config.txHash
				def txHash = null
				if (txHashPrefix != null && txHashPrefix != '') {
					txHash = "${txHashPrefix}${txHashCounter++}"
				}
				def logType = config.logType

				// Create transaction result that returns TransactionObject
				def mockTxObject = Mock(EthBlock.TransactionObject)
				mockTxObject.getHash() >> txHash

				def mockTxResult = Mock(EthBlock.TransactionResult)
				mockTxResult.get() >> mockTxObject
				mockTxResults.add(mockTxResult)

				// Create receipt mocks
				def mockReceiptRequest = Mock(Request)
				def mockReceiptResponse = Mock(EthGetTransactionReceipt)
				def mockReceipt = Mock(TransactionReceipt)

				// Create log based on type
				def log = createMockLogByType(logType, txHash, blockNumber)

				// Set logIndex with incrementing counter
				log.logIndex = BigInteger.valueOf(logIndexCounter++)

				mockReceipt.getLogs() >> [log]
				mockReceiptResponse.getTransactionReceipt() >> Optional.of(mockReceipt)
				mockReceiptRequest.send() >> mockReceiptResponse

				// Setup Web3j transaction receipt mock
				web3jCaller.ethGetTransactionReceipt(txHash) >> mockReceiptRequest
			}

			// Setup block with transactions for this specific block number
			mockBlock.getNumber() >> BigInteger.valueOf(blockNumber)
			mockBlock.getTimestamp() >> BigInteger.valueOf(timestamp)
			mockBlock.getTransactions() >> mockTxResults

			mockEthBlock.getBlock() >> mockBlock
			mockRequest.send() >> mockEthBlock

			// Store mock request for this block number
			mockRequestsByBlock[blockNumber] = mockRequest
		}

		// Setup Web3j block mock to return appropriate mock based on call order
		// Since we can't extract block number from lambda function due to security restrictions,
		// we'll use call order instead (block 1000, 1001, 1002)
		def callCount = 0
		def blockNumbers = mockLogConfigs.unique { it.blockNumber }.collect { it.blockNumber }.sort()

		web3jCaller.ethGetBlockByNumber(_, true) >> { blockParam, includeTransactions ->

			// Determine block number based on call order
			def blockNumber = null
			if (callCount < blockNumbers.size()) {
				blockNumber = blockNumbers[callCount]
				callCount++
			}

			def result = mockRequestsByBlock[blockNumber]

			// Return the appropriate mock request for this block number
			return result
		}

		// Also mock the DefaultBlockParameter overload for other uses (like getBlockTimestamp)
		web3jCaller.ethGetBlockByNumber(!null as DefaultBlockParameter, false) >> { blockParam, includeTransactions ->

			// This is for other uses like getBlockTimestamp
			def mockRequest = Mock(Request)
			def mockEthBlock = Mock(EthBlock)
			def mockBlock = Mock(EthBlock.Block)

			mockBlock.getTimestamp() >> BigInteger.valueOf(timestamp)
			mockEthBlock.getBlock() >> mockBlock
			mockRequest.send() >> mockEthBlock

			return mockRequest
		}
	}

	/**
	 * Create a mock log by type with specified transaction hash and block number
	 * @param logType Type of log to create
	 * @param txHash Transaction hash to assign
	 * @param blockNumber Block number to assign
	 * @return Mock log object
	 */
	protected def createMockLogByType(String logType, String txHash, Long blockNumber) {
		def log = null

		switch (logType) {
			case 'addProviderRole':
				log = EventMockFactory.createAddProviderRoleLog()
				break
			case 'addTokenByProvider':
				log = EventMockFactory.createAddTokenByProviderLog()
				break
			case 'roleAdminChanged':
				log = EventMockFactory.createRoleAdminChangedLog()
				break
			case 'roleGranted':
				log = EventMockFactory.createMockRoleGrantedLog()
				break
			case 'roleRevoked':
				log = EventMockFactory.createMockRoleRevokedLog()
				break
			case 'modProvider':
				log = EventMockFactory.createMockModProviderLog()
				break
			case 'modAccount':
				log = EventMockFactory.createMockModAccountLog()
				break
			case 'transfer':
				log = EventMockFactory.createMockTransferLog()
				break
			case 'addAccountLimit':
				log = EventMockFactory.createMockAddAccountLimitLog()
				break
			case 'forceBurn':
				log = EventMockFactory.createMockForceBurnLog()
				break
			case 'addAccount':
				log = EventMockFactory.createMockAddAccountLog()
				break
			case 'afterBalance':
				log = EventMockFactory.createMockAfterBalanceLog()
				break
			case 'generatedClientIdentifier':
				log = EventMockFactory.createMockGeneratedClientIdentifierLog()
				break
			case 'sendPacket':
				log = EventMockFactory.createMockSendPacketLog()
				break
			default:
				throw new IllegalArgumentException("Unknown mock log type: ${logType}")
		}

		if (log != null) {
			// Set transaction hash if provided
			if (txHash != null) {
				log.transactionHash = txHash
			}

			// Set block number if provided
			if (blockNumber != null) {
				log.blockNumber = BigInteger.valueOf(blockNumber)
			}
		}

		return log
	}

	/**
	 * Create mock pending event logs for testing with specific parameters
	 * @param mockLogs List of mock log types to include (e.g., ['addProviderRole', 'addTokenByProvider'])
	 * @param blockNumber Block number to assign to all logs
	 * @param txHashPrefix Prefix for transaction hashes (default: '0xabc')
	 * @param timestamp Block timestamp (default: 1750606026L)
	 * @return List of EthLog.LogResult objects containing mock event logs
	 */
	protected def createMockPendingEventLogs(List<String> mockLogs, Long blockNumber, String txHashPrefix = '0xabc', Long timestamp = 1750606026L) {
		def logResults = []
		def txHashCounter = 134
		def logIndexCounter = 0

		mockLogs.each { logType ->
			def log = null
			def txHash = null

			// Generate txHash only if prefix is provided
			if (txHashPrefix != null && txHashPrefix != '') {
				txHash = "${txHashPrefix}${txHashCounter++}"
			}

			switch (logType) {
				case 'addProviderRole':
					log = EventMockFactory.createAddProviderRoleLog()
					break
				case 'addTokenByProvider':
					log = EventMockFactory.createAddTokenByProviderLog()
					break
				case 'roleAdminChanged':
					log = EventMockFactory.createRoleAdminChangedLog()
					break
				case 'roleGranted':
					log = EventMockFactory.createMockRoleGrantedLog()
					break
				case 'roleRevoked':
					log = EventMockFactory.createMockRoleRevokedLog()
					break
				case 'modProvider':
					log = EventMockFactory.createMockModProviderLog()
					break
				case 'modAccount':
					log = EventMockFactory.createMockModAccountLog()
					break
				case 'transfer':
					log = EventMockFactory.createMockTransferLog()
					break
				case 'addAccountLimit':
					log = EventMockFactory.createMockAddAccountLimitLog()
					break
				default:
					throw new IllegalArgumentException("Unknown mock log type: ${logType}")
			}

			if (log != null) {
				// Set blockNumber if provided
				if (blockNumber != null) {
					log.blockNumber = BigInteger.valueOf(blockNumber)
				}

				// Set transactionHash if generated
				if (txHash != null) {
					log.transactionHash = txHash
				}

				// Set logIndex with incrementing counter
				log.logIndex = BigInteger.valueOf(logIndexCounter++)

				def logResult = Mock(EthLog.LogResult)
				logResult.get() >> log
				logResults.add(logResult)
			}
		}

		// Create mock block with timestamp
		setupMockBlockTimestamp(timestamp)

		return logResults
	}

	/**
	 * Setup mock block timestamp for Web3j
	 * @param timestamp Block timestamp to return
	 */
	private void setupMockBlockTimestamp(Long timestamp) {
		def mockRequest = Mock(Request)
		def ethBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		ethBlock.getBlock() >> mockBlock
		mockRequest.send() >> ethBlock
		mockBlock.getTimestamp() >> BigInteger.valueOf(timestamp)
		web3j.ethGetBlockByNumber(_ as DefaultBlockParameter, false) >> mockRequest
	}

	/**
	 * Helper method to setup mock Web3j with blocks containing transactions but no events
	 * @param blockNumbers List of block numbers to create
	 * @param timestamp Block timestamp (default: 1750606026L)
	 */
	def setupMockWeb3jWithEmptyEvents(List<Long> blockNumbers, Long timestamp = 1750606026L) {
		def mockRequestsByBlock = [:]
		def txHashCounter = 134

		// Create mock blocks with transactions but empty event logs
		blockNumbers.each { blockNumber ->
			def mockRequest = Mock(Request)
			def mockEthBlock = Mock(EthBlock)
			def mockBlock = Mock(EthBlock.Block)

			// Create mock transactions with empty logs
			def mockTxResults = []
			def mockTxHash = "0xempty${blockNumber}${txHashCounter++}"

			// Create transaction result that returns TransactionObject
			def mockTxObject = Mock(EthBlock.TransactionObject)
			mockTxObject.getHash() >> mockTxHash

			def mockTxResult = Mock(EthBlock.TransactionResult)
			mockTxResult.get() >> mockTxObject
			mockTxResults.add(mockTxResult)

			// Setup transaction receipt with empty logs
			def mockReceiptRequest = Mock(Request)
			def mockReceipt = Mock(EthGetTransactionReceipt)
			def mockReceiptResult = Mock(TransactionReceipt)

			mockReceiptResult.getLogs() >> [] // Empty logs - no events
			mockReceipt.getTransactionReceipt() >> Optional.of(mockReceiptResult)
			mockReceiptRequest.send() >> mockReceipt

			// Setup Web3j transaction receipt mock for this specific transaction hash
			web3jCaller.ethGetTransactionReceipt(mockTxHash) >> mockReceiptRequest

			// Setup block with transactions
			mockBlock.getNumber() >> BigInteger.valueOf(blockNumber)
			mockBlock.getTimestamp() >> BigInteger.valueOf(timestamp)
			mockBlock.getTransactions() >> mockTxResults

			mockEthBlock.getBlock() >> mockBlock
			mockRequest.send() >> mockEthBlock

			// Store mock request for this block number
			mockRequestsByBlock[blockNumber] = mockRequest
		}

		// Setup Web3j block mock to return appropriate mock based on call order
		def callCount = 0
		def sortedBlockNumbers = blockNumbers.sort()

		web3jCaller.ethGetBlockByNumber(_, true) >> { blockParam, includeTransactions ->
			def blockNumber = null
			if (callCount < sortedBlockNumbers.size()) {
				blockNumber = sortedBlockNumbers[callCount]
				callCount++
			}

			def result = mockRequestsByBlock[blockNumber]
			return result
		}

		// Also mock the DefaultBlockParameter overload for other uses (like getBlockTimestamp)
		web3jCaller.ethGetBlockByNumber(!null as DefaultBlockParameter, false) >> { blockParam, includeTransactions ->
			def mockRequest = Mock(Request)
			def mockEthBlock = Mock(EthBlock)
			def mockBlock = Mock(EthBlock.Block)

			mockBlock.getTimestamp() >> BigInteger.valueOf(timestamp)
			mockEthBlock.getBlock() >> mockBlock
			mockRequest.send() >> mockEthBlock

			return mockRequest
		}
	}
}
