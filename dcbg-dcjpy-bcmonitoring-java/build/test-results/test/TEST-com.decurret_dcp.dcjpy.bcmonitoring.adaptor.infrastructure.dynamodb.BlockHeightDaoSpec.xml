<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:28.085Z" hostname="tungvt.local" time="1.296">
  <properties/>
  <testcase name="should successfully get block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="1.117"/>
  <testcase name="should return 0 when no block heights found" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.007"/>
  <testcase name="should throw DataAccessException when get fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.019"/>
  <testcase name="should successfully save block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.039"/>
  <testcase name="should handle exception when saving block height fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.101"/>
  <testcase name="should return true when saving null block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.002"/>
  <testcase name="should handle generic Exception during get operation" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.006"/>
  <testcase name="should handle generic Exception during save operation" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec" time="0.005"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
