envName: dev-biz

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/dev-biz-core
    tag: "9d16809"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/dev-biz-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/dev-biz-tokyo-core-alb-tg/0157fe88ae19d73a

configMap:
  DB_BASE: "dev-biz-tokyo-rds-cluster.cluster-cwv5otaof6ma.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_LmF3xvvIZ/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_LmF3xvvIZ"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "business_zone"
