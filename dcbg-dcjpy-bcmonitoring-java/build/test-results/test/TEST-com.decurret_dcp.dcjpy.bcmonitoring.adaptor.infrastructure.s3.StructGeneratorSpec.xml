<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" tests="17" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:39.150Z" hostname="tungvt.local" time="0.021">
  <properties/>
  <testcase name="generateStructClass should create StaticStruct for static types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.003"/>
  <testcase name="generateStructClass should create DynamicStruct for dynamic types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should create DynamicStruct when DynamicBytes is present" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should create DynamicStruct when DynamicArray is present" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should create DynamicStruct when DynamicStruct is present" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should handle empty field types list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should handle single field type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="generateStructClass should create unique class names" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.003"/>
  <testcase name="isDynamic should return true for DynamicBytes" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.0"/>
  <testcase name="isDynamic should return true for Utf8String" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.0"/>
  <testcase name="isDynamic should return true for DynamicArray" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <testcase name="isDynamic should return true for DynamicStruct" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.0"/>
  <testcase name="isDynamic should return false for static types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.0"/>
  <testcase name="isDynamic should handle null parameter" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.0"/>
  <testcase name="generateStructClass should handle mixed static and dynamic types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.002"/>
  <testcase name="generateStructClass should create constructor with correct parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.005"/>
  <testcase name="constructor should be callable for completeness" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
