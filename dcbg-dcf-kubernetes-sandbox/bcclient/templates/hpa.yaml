apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bcclient
  namespace: dcbg-core
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bcclient
  minReplicas: {{ .Values.minReplicas }}
  maxReplicas: {{ .Values.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 50
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bcclient-read-only
  namespace: dcbg-core
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bcclient-read-only
  minReplicas: {{ .Values.minReplicas }}
  maxReplicas: {{ .Values.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 50