<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="DYNAMO" family-id="DYNAMO" format-version="4.53">
    <root id="1">
      <ServerVersion>1.0</ServerVersion>
    </root>
    <schema id="2" parent="1" name="schema"/>
    <table id="3" parent="2" name="local-BlockHeight">
      <ReadThroughput>5</ReadThroughput>
      <WriteThroughput>5</WriteThroughput>
    </table>
    <table id="4" parent="2" name="local-Events">
      <ReadThroughput>5</ReadThroughput>
      <WriteThroughput>5</WriteThroughput>
    </table>
    <column id="5" parent="3" name="id">
      <Position>1</Position>
      <StoredType>N|0s</StoredType>
    </column>
    <key id="6" parent="3" name="primary key">
      <PartitionColumnName>id</PartitionColumnName>
    </key>
    <column id="7" parent="4" name="transactionHash">
      <Position>1</Position>
      <StoredType>S|0s</StoredType>
    </column>
    <column id="8" parent="4" name="logIndex">
      <Position>2</Position>
      <StoredType>N|0s</StoredType>
    </column>
    <key id="9" parent="4" name="primary key">
      <PartitionColumnName>transactionHash</PartitionColumnName>
      <SortColumnName>logIndex</SortColumnName>
    </key>
  </database-model>
</dataSource>