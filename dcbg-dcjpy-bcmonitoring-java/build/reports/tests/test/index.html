<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">417</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">11.799s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a>
</td>
<td>26</td>
<td>0</td>
<td>0</td>
<td>1.418s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</a>
</td>
<td>96</td>
<td>0</td>
<td>0</td>
<td>9.082s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a>
</td>
<td>216</td>
<td>0</td>
<td>0</td>
<td>0.425s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.html">com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.025s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.html">com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event</a>
</td>
<td>67</td>
<td>0</td>
<td>0</td>
<td>0.849s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDaoSpec</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>1.296s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.064s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.058s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec</a>
</td>
<td>92</td>
<td>0</td>
<td>0</td>
<td>9.064s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.018s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParserContentSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParserContentSpec</a>
</td>
<td>43</td>
<td>0</td>
<td>0</td>
<td>0.261s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec</a>
</td>
<td>123</td>
<td>0</td>
<td>0</td>
<td>0.036s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.085s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>0.021s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.025s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec.html">com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec</a>
</td>
<td>67</td>
<td>0</td>
<td>0</td>
<td>0.849s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
