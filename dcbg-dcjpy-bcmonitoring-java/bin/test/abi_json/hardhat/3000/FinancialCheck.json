{"address": "0xa6d4d967074cf7732f7D7045397ADF34f1E22b73", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "checkExchange", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint16", "name": "toZoneId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkFinAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkSyncAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransaction", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "bytes32", "name": "fromAccountIssuerId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountLimit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "dischargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple", "name": "cumulativeTransactionLimits", "components": [{"type": "uint256", "name": "cumulativeMintLimit"}, {"type": "uint256", "name": "cumulativeMintAmount"}, {"type": "uint256", "name": "cumulativeBurnLimit"}, {"type": "uint256", "name": "cumulativeBurnAmount"}, {"type": "uint256", "name": "cumulativeChargeLimit"}, {"type": "uint256", "name": "cumulativeChargeAmount"}, {"type": "uint256", "name": "cumulativeDischargeLimit"}, {"type": "uint256", "name": "cumulativeDischargeAmount"}, {"type": "uint256", "name": "cumulativeTransferLimit"}, {"type": "uint256", "name": "cumulativeTransferAmount"}]}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getBizZoneAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getIssuerWithZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}], "outputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x407bba0a7751b714384e1f80b9c590c59ad62d4877b6dd0bdfb08b5bd63036ab", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0xa6d4d967074cf7732f7D7045397ADF34f1E22b73", "transactionIndex": 0, "gasUsed": "4312491", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x9b79d3c543a8fde7b5ca0965cab7d241659f34a1c489ed3344d260476c0425c3", "blockNumber": 224, "cumulativeGasUsed": "4312491", "status": 1}}