<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventLogRepositoryImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</a> &gt; <span class="el_source">EventLogRepositoryImpl.java</span></div><h1>EventLogRepositoryImpl.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.BlockchainException;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import org.springframework.stereotype.Repository;

/** Repository implementation for Ethereum event logs */
@Repository
public class EventLogRepositoryImpl implements EventLogRepository {
  private final LoggingService logger;
  private final EthEventLogDao eventLogDao;

<span class="fc" id="L16">  public EventLogRepositoryImpl(LoggingService logger, EthEventLogDao eventLogDao) {</span>
<span class="fc" id="L17">    this.logger = logger;</span>
<span class="fc" id="L18">    this.eventLogDao = eventLogDao;</span>
<span class="fc" id="L19">  }</span>

  /**
   * Subscribe to blockchain events
   *
   * @return Queue of transactions containing events
   * @throws BlockchainException if there is an error subscribing to events
   */
  @Override
  public BlockingQueue&lt;Transaction&gt; subscribe() {
    try {
<span class="fc" id="L30">      return eventLogDao.subscribeAll();</span>
<span class="fc" id="L31">    } catch (Exception e) {</span>
<span class="fc" id="L32">      String errorMessage = &quot;Error subscribing to blockchain events&quot;;</span>
<span class="fc" id="L33">      logger.error(errorMessage, e);</span>
<span class="fc" id="L34">      throw new BlockchainException(errorMessage, e);</span>
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   * @throws BlockchainException if there is an error getting filter logs
   */
  @Override
  public List&lt;Transaction&gt; getFilterLogs(long blockHeight) {
    try {
<span class="fc" id="L48">      return eventLogDao.getPendingTransactions(blockHeight);</span>
<span class="fc" id="L49">    } catch (Exception e) {</span>
<span class="fc" id="L50">      String errorMessage = &quot;Error getting filter logs from block height: &quot; + blockHeight;</span>
<span class="fc" id="L51">      logger.error(errorMessage, e);</span>
<span class="fc" id="L52">      throw new BlockchainException(errorMessage, e);</span>
    }
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>