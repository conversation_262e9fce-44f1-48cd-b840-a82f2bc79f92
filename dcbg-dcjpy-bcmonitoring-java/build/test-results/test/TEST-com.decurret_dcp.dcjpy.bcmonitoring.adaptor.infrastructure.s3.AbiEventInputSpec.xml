<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" tests="13" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:38.644Z" hostname="tungvt.local" time="0.011">
  <properties/>
  <testcase name="constructor with 3 parameters should set fields correctly" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.001"/>
  <testcase name="constructor with 4 parameters should set fields correctly" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.003"/>
  <testcase name="constructor should handle null components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.001"/>
  <testcase name="constructor should handle empty components list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <testcase name="isTuple should return true for tuple types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.001"/>
  <testcase name="isTuple should return false for non-tuple types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <testcase name="isTuple should handle null type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.001"/>
  <testcase name="isTuple should handle empty type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <testcase name="getters should return correct values" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.001"/>
  <testcase name="should handle null name" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <testcase name="should handle null type in constructor" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <testcase name="components should be immutable copy" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.002"/>
  <testcase name="should handle complex nested components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
