Index: src/test/groovy/adhoc/startup/StartupServiceSpec.groovy
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package adhoc.startup\n\nimport adhoc.helper.AdhocHelper\nimport ch.qos.logback.classic.Logger\nimport ch.qos.logback.classic.spi.ILoggingEvent\nimport ch.qos.logback.core.read.ListAppender\nimport com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository\nimport com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService\nimport com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRetryListener\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig\nimport com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService\nimport io.reactivex.Flowable\nimport io.reactivex.processors.PublishProcessor\nimport java.util.concurrent.Executors\nimport java.util.concurrent.TimeUnit\nimport java.util.concurrent.atomic.AtomicBoolean\nimport java.util.concurrent.atomic.AtomicInteger\nimport org.slf4j.LoggerFactory\nimport org.springframework.beans.factory.annotation.Autowired\nimport org.springframework.boot.CommandLineRunner\nimport org.springframework.boot.test.context.SpringBootTest\nimport org.springframework.context.ApplicationContext\nimport org.springframework.retry.support.RetryTemplate\nimport org.springframework.test.context.DynamicPropertyRegistry\nimport org.springframework.test.context.DynamicPropertySource\nimport org.springframework.test.context.bean.override.mockito.MockitoSpyBean\nimport org.web3j.protocol.Web3j\nimport org.web3j.protocol.core.Request\nimport org.web3j.protocol.core.methods.request.EthFilter\nimport org.web3j.protocol.core.methods.response.EthBlock\nimport org.web3j.protocol.core.methods.response.EthLog\nimport software.amazon.awssdk.auth.credentials.AwsBasicCredentials\nimport software.amazon.awssdk.auth.credentials.StaticCredentialsProvider\nimport software.amazon.awssdk.core.sync.RequestBody\nimport software.amazon.awssdk.regions.Region\nimport software.amazon.awssdk.services.dynamodb.DynamoDbClient\nimport software.amazon.awssdk.services.dynamodb.model.AttributeValue\nimport software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest\nimport software.amazon.awssdk.services.dynamodb.model.ScanRequest\nimport software.amazon.awssdk.services.s3.S3Client\nimport software.amazon.awssdk.services.s3.model.DeleteObjectRequest\nimport software.amazon.awssdk.services.s3.model.ListObjectsV2Request\nimport software.amazon.awssdk.services.s3.model.PutObjectRequest\nimport spock.lang.Shared\nimport spock.lang.Specification\n\n@SpringBootTest(\nclasses = [BcmonitoringApplication.class],\nwebEnvironment = SpringBootTest.WebEnvironment.NONE\n)\nclass StartupServiceSpec extends Specification {\n\n\t@Shared\n\tDynamoDbClient dynamoDbClient\n\n\t@Shared\n\tS3Client s3Client\n\n\t@Shared\n\tdef logger = LoggerFactory.getLogger(LoggingService.class) as Logger\n\n\t@Autowired\n\tLoggingService loggingService\n\n\t@Autowired\n\tMonitoringRetryListener retryListener\n\n\t@Autowired\n\tDownloadAbiService downloadAbiService\n\n\t@Autowired\n\tMonitorEventService monitorEventService\n\n\t@Autowired\n\tS3AbiRepository s3AbiRepository\n\n\t@Autowired\n\tApplicationContext applicationContext\n\n\t@Autowired\n\tRetryTemplate retryTemplate\n\n\t@MockitoSpyBean\n\tWeb3jConfig web3jConfig\n\n\tstatic final String TEST_BUCKET = \"abijson-local-bucket\"  // Same as default in application.properties\n\tstatic final String EVENTS_TABLE = \"local-Events\"        // Same as default in application.properties\n\tstatic final String BLOCK_HEIGHT_TABLE = \"local-BlockHeight\"  // Same as default in application.properties\n\n\tdef logAppender = new ListAppender<ILoggingEvent>()\n\tdef web3j = Mock(Web3j)\n\tdef scheduler = Executors.newScheduledThreadPool(1)\n\n\n\t@DynamicPropertySource\n\tstatic void configureProperties(DynamicPropertyRegistry registry) {\n\t\tregistry.add(\"local-stack.end-point\", { \"http://localhost:\" + AdhocHelper.getLocalStackPort() })\n\t\tregistry.add(\"eagerStart\", { \"false\" })\n\t\tregistry.add(\"aws.dynamodb.table-prefix\", { \"\" })\n\t}\n\n\tdef setupSpec() {\n\t\t// Create DynamoDB client for LocalStack\n\t\tdynamoDbClient = DynamoDbClient.builder()\n\t\t\t\t.endpointOverride(URI.create(\"http://localhost:\" + AdhocHelper.getLocalStackPort()))\n\t\t\t\t.credentialsProvider(StaticCredentialsProvider.create(\n\t\t\t\tAwsBasicCredentials.create(\"access123\", \"secret123\")))\n\t\t\t\t.region(Region.AP_NORTHEAST_1)\n\t\t\t\t.build()\n\n\t\t// Create S3 client for LocalStack\n\t\ts3Client = S3Client.builder()\n\t\t\t\t.endpointOverride(URI.create(\"http://localhost:\" + AdhocHelper.getLocalStackPort()))\n\t\t\t\t.credentialsProvider(StaticCredentialsProvider.create(\n\t\t\t\tAwsBasicCredentials.create(\"access123\", \"secret123\")))\n\t\t\t\t.region(Region.AP_NORTHEAST_1)\n\t\t\t\t.forcePathStyle(true)\n\t\t\t\t.build()\n\n\t\t// Create tables and bucket\n\t\tAdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)\n\t\tAdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)\n\t\tAdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)\n\t}\n\n\tdef cleanupSpec() {\n\t\tdynamoDbClient?.close()\n\t\ts3Client?.close()\n\t}\n\n\tdef setup() {\n\t\t// Clear all S3 bucket contents completely\n\t\tclearS3BucketCompletely()\n\t\t// Upload real ABI files to\n\t\tAdhocHelper.uploadRealAbiFiles(s3Client, TEST_BUCKET, \"3000\", [\n\t\t\t\"Token\",\n\t\t\t\"Account\",\n\t\t\t\"Provider\"\n\t\t])\n\t\t// Clear all DynamoDB table contents\n\t\tclearDynamoDBTables()\n\t\t// Setup web3j mock\n\t\tsetupWeb3jMock()\n\t\t// Start log appender to capture logs\n\t\tlogAppender.start()\n\t\tlogger.addAppender(logAppender)\n\t}\n\n\tprivate void setupWeb3jMock() {\n\n\t\tdef field = Web3jConfig.class.getDeclaredField(\"web3j\")\n\t\tfield.setAccessible(true)\n\t\tfield.set(web3jConfig, web3j)\n\t\tprintln(\"Web3j mock setup completed\")\n\t}\n\n\tprivate void setUpEventStream(List<EthBlock> blocks) {\n\t\tdef processor = PublishProcessor.<EthBlock> create()\n\t\tdef index = new AtomicInteger(0)\n\t\tscheduler.scheduleAtFixedRate({\n\t\t\tint i = index.getAndIncrement()\n\t\t\tif (i < blocks.size()) {\n\t\t\t\tprocessor.onNext(blocks.get(i))\n\t\t\t} else {\n\t\t\t\tprocessor.onComplete()\n\t\t\t}\n\t\t}, 0, 2, TimeUnit.SECONDS)\n\n\t\tweb3j.blockFlowable(false) >> Flowable.fromPublisher(processor)\n\t}\n\n\tprivate void setUpPendingEvent(List<EthLog.LogResult> resultList) {\n\t\tdef mockRequest = Mock(Request)\n\t\tdef mockLog = Mock(EthLog)\n\n\t\tweb3j.ethGetLogs(_ as EthFilter) >> mockRequest\n\t\tmockRequest.send() >> mockLog\n\t\tmockLog.getLogs() >> resultList\n\t}\n\n\tdef cleanup() {\n\t\t// Clear S3 bucket for next test\n\t\tclearS3Bucket()\n\t\t// Shut down the scheduler to stop mock event generation\n\t\tscheduler.shutdown()\n\t\tscheduler.awaitTermination(5, TimeUnit.SECONDS)\n\t}\n\n\tprivate void clearS3Bucket() {\n\t\tclearS3BucketCompletely()\n\t}\n\n\tprivate void clearS3BucketCompletely() {\n\t\ttry {\n\t\t\tString continuationToken = null\n\t\t\tboolean hasMoreObjects = true\n\n\t\t\twhile (hasMoreObjects) {\n\t\t\t\tdef listRequest = ListObjectsV2Request.builder()\n\t\t\t\t\t\t.bucket(TEST_BUCKET)\n\t\t\t\t\t\t.continuationToken(continuationToken)\n\t\t\t\t\t\t.build()\n\n\t\t\t\tdef listResponse = s3Client.listObjectsV2(listRequest as ListObjectsV2Request)\n\n\t\t\t\tlistResponse.contents().each { obj ->\n\t\t\t\t\ts3Client.deleteObject(DeleteObjectRequest.builder()\n\t\t\t\t\t\t\t.bucket(TEST_BUCKET)\n\t\t\t\t\t\t\t.key(obj.key())\n\t\t\t\t\t\t\t.build() as DeleteObjectRequest)\n\t\t\t\t}\n\n\t\t\t\tcontinuationToken = listResponse.nextContinuationToken()\n\t\t\t\thasMoreObjects = (continuationToken != null)\n\t\t\t}\n\n\t\t\tdef finalCheck = s3Client.listObjectsV2 {\n\t\t\t\tit.bucket(TEST_BUCKET)\n\t\t\t}\n\t\t\tif (finalCheck.contents().isEmpty()) {\n\t\t\t\tprintln(\"S3 bucket ${TEST_BUCKET} successfully cleared\")\n\t\t\t} else {\n\t\t\t\tprintln(\"Warning: S3 bucket ${TEST_BUCKET} still contains ${finalCheck.contents().size()} objects\")\n\t\t\t}\n\t\t} catch (Exception e) {\n\t\t\tprintln(\"Error clearing S3 bucket: ${e.message}\")\n\t\t\te.printStackTrace()\n\t\t}\n\t}\n\n\tprivate void clearDynamoDBTables() {\n\t\ttry {\n\t\t\t// Clear events table\n\t\t\tclearDynamoDBTable(EVENTS_TABLE, [\"transactionHash\", \"logIndex\"])\n\n\t\t\t// Clear block height table\n\t\t\tclearDynamoDBTable(BLOCK_HEIGHT_TABLE, [\"id\"])\n\t\t} catch (Exception e) {\n\t\t\tprintln(\"Error clearing DynamoDB tables: ${e.message}\")\n\t\t\te.printStackTrace()\n\t\t}\n\t}\n\n\tprivate void clearDynamoDBTable(String tableName, List<String> keyAttributes) {\n\t\ttry {\n\t\t\t// Scan the table to get all items\n\t\t\tdef scanRequest = ScanRequest.builder()\n\t\t\t\t\t.tableName(tableName)\n\t\t\t\t\t.build()\n\n\t\t\tdef scanResponse = dynamoDbClient.scan(scanRequest as ScanRequest)\n\n\t\t\t// Delete each item\n\t\t\tscanResponse.items().each { item ->\n\t\t\t\tdef keyMap = [:]\n\t\t\t\tkeyAttributes.each { keyAttr ->\n\t\t\t\t\tif (item.containsKey(keyAttr)) {\n\t\t\t\t\t\tkeyMap[keyAttr] = item[keyAttr]\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (!keyMap.isEmpty()) {\n\t\t\t\t\tdef deleteRequest = DeleteItemRequest.builder()\n\t\t\t\t\t\t\t.tableName(tableName)\n\t\t\t\t\t\t\t.key(keyMap as Map<String, AttributeValue>)\n\t\t\t\t\t\t\t.build()\n\t\t\t\t\tdynamoDbClient.deleteItem(deleteRequest as DeleteItemRequest)\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (Exception e) {\n\t\t\tprintln(\"Error clearing DynamoDB table ${tableName}: ${e.message}\")\n\t\t}\n\t}\n\n\tprivate void createAbiFile(String key, String content) {\n\t\tprintln(\"Creating ABI file: ${key}\")\n\t\ts3Client.putObject(PutObjectRequest.builder()\n\t\t\t\t.bucket(TEST_BUCKET)\n\t\t\t\t.key(key)\n\t\t\t\t.build() as PutObjectRequest,\n\t\t\t\tRequestBody.fromString(content))\n\t}\n\n\t/**\n\t * Successful Service Startup\n\t * Verifies service starts successfully with all dependencies available\n\t * Expected: Service logs \"Starting bc monitoring\" and \"Started bc monitoring\"\n\t */\n\tdef \"Should start successfully with all dependencies available\"() {\n\t\tgiven: \"Valid environment with accessible dependencies\"\n\t\tsetUpEventStream(Collections.emptyList())\n\t\tsetUpPendingEvent(Collections.emptyList())\n\t\twhen: \"Testing real service startup with CommandLineRunner\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner.class)\n\n\t\tscheduler.schedule({\n\t\t\tstopBCMonitoring()\n\t\t}, 10, TimeUnit.SECONDS)\n\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"Service should start successfully and log the required messages\"\n\t\tnoExceptionThrown()\n\t\tlogAppender.list*.formattedMessage.contains(\"Started bc monitoring\")\n\t\tand: \"Real services should be available\"\n\t\tdownloadAbiService != null\n\t\tmonitorEventService != null\n\t\tloggingService != null\n\n\t\tand: \"Infrastructure should be accessible\"\n\t\ts3Client != null\n\t\tdynamoDbClient != null\n\t}\n\n\t/**\n\t * Service Restart After WebSocket Error\n\t * Verifies service automatically restarts monitoring after WebSocket handshake error\n\t * Expected: Service retries 5 times with WebSocketHandshakeException, logs \"restart bc monitoring\" 5 times\n\t */\n\tdef \"Should automatically restart monitoring after WebSocket handshake error\"() {\n\t\t//todo\n\t}\n\n\t/**\n\t * Service Startup with Empty ABI Bucket\n\t * Verifies service starts successfully when S3 bucket exists but contains no ABI files\n\t * Expected: Service starts with no contract addresses loaded, application continues running and logs \"Started bc monitoring\"\n\t */\n\tdef \"Should start successfully with empty ABI bucket\"() {\n\t\tgiven: \"An empty S3 bucket and all dependencies available\"\n\t\tclearS3Bucket()\n\t\tassert s3Client.listObjectsV2 { it.bucket(TEST_BUCKET) }.contents().isEmpty()\n\n\t\twhen: \"The service starts\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner.class)\n\n\t\tscheduler.schedule({\n\t\t\tstopBCMonitoring()\n\t\t}, 10, TimeUnit.SECONDS)\n\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"No exception is thrown\"\n\t\tnoExceptionThrown()\n\n\t\tand: \"Critical services are initialized\"\n\t\tassert downloadAbiService\n\t\tassert monitorEventService\n\t\tassert loggingService\n\t\tassert dynamoDbClient\n\t\tassert s3Client\n\n\t\tand: \"Log should indicate successful startup\"\n\t\tlogAppender.list*.formattedMessage.contains(\"Started bc monitoring\")\n\t}\n\n\tprivate void stopBCMonitoring() {\n\t\tdef field = MonitorEventService.class.getDeclaredField(\"running\")\n\t\tfield.setAccessible(true)\n\t\tAtomicBoolean running = field.get(monitorEventService) as AtomicBoolean\n\t\trunning.set(false)\n\t}\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy b/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy
--- a/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy	(revision 25f0bd929a8291f37c2dcda0fae62e0c3db7c560)
+++ b/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy	(date 1750395474480)
@@ -131,6 +131,8 @@
 	}
 
 	def setup() {
+		// Reset running flag to ensure clean state for each test
+		resetRunningFlag()
 		// Clear all S3 bucket contents completely
 		clearS3BucketCompletely()
 		// Upload real ABI files to
@@ -186,6 +188,8 @@
 		// Shut down the scheduler to stop mock event generation
 		scheduler.shutdown()
 		scheduler.awaitTermination(5, TimeUnit.SECONDS)
+		// Reset the running flag for next test
+		resetRunningFlag()
 	}
 
 	private void clearS3Bucket() {
@@ -317,7 +321,7 @@
 	/**
 	 * Service Restart After WebSocket Error
 	 * Verifies service automatically restarts monitoring after WebSocket handshake error
-	 * Expected: Service retries 5 times with WebSocketHandshakeException, logs "restart bc monitoring" 5 times
+	 * Expected: Service reinitialized 5 times with WebSocketHandshakeException, logs "restart bc monitoring"
 	 */
 	def "Should automatically restart monitoring after WebSocket handshake error"() {
 		//todo
@@ -356,10 +360,23 @@
 		logAppender.list*.formattedMessage.contains("Started bc monitoring")
 	}
 
+	private void resetRunningFlag() {
+		def field = MonitorEventService.class.getDeclaredField("running")
+		field.setAccessible(true)
+		AtomicBoolean running = field.get(monitorEventService) as AtomicBoolean
+		running.set(true)
+		println("Reset running flag to true")
+	}
+
 	private void stopBCMonitoring() {
-		def field = MonitorEventService.class.getDeclaredField("running")
-		field.setAccessible(true)
-		AtomicBoolean running = field.get(monitorEventService) as AtomicBoolean
-		running.set(false)
+		try {
+			def field = MonitorEventService.class.getDeclaredField("running")
+			field.setAccessible(true)
+			AtomicBoolean running = field.get(monitorEventService) as AtomicBoolean
+			running.set(false)
+			println("Set running flag to false to stop monitoring")
+		} catch (Exception e) {
+			println("Error stopping BC monitoring: ${e.message}")
+		}
 	}
 }
