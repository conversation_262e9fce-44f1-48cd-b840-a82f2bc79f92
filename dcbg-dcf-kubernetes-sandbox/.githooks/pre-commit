#!/bin/bash
if [[ $(which ggrep) =~ ggrep$ ]]; then
  : # check ok
else
  echo "ERROR : please install ggrep"
  exit 1
fi

RESULT1=$(ggrep -rn -P '[\x{1000}-\x{E0000}]' ./ --include='README.md' --exclude-dir={.git,.github,.gitignore})
if [ -n "$RESULT1" ]; then
  : # ggrepでASCII文字以外を検出出来ることを確認するためにREADME.mdを引っ掛ける
else
  echo "ERROR : use a different git client"
  exit 1
fi

RESULT2=$(git diff --name-only --diff-filter=AM | grep '.yaml$' | xargs ggrep -rn -P '[\x{1000}-\x{E0000}]')
if [ -n "$RESULT2" ]; then
  echo "$RESULT2"
  echo "ERROR : non-ascii character found"
  exit 1
fi


