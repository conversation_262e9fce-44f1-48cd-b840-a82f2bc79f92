<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="58eef0f7-6fc0-48dc-a5e0-081fb8801951" name="Changes" comment="Fix tuple array decoding in EthEventLogDao and update related tests">
      <change afterPath="$PROJECT_DIR$/async_processing_design.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/basic_design_outline.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/corrected_sequence_diagram.puml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/groovy/adhoc/README_ADHOC_TESTS.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/基本設計書_BCMonitoring_新規ブロックsubscribe機能.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/基本設計書_BCMonitoring_新規ブロック処理機能.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/基本設計書_Web3j統合.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/基本設計書_選択中Listenerノード接続管理.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/LoggingService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/LoggingService.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <task path="$PROJECT_DIR$/src/test/groovy/adhoc">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="dcbg-dcjpy-bcmonitoring" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="dcbg-dcjpy-bcmonitoring" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="dcbg-dcjpy-bcmonitoring" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="other" type="c8890929:TasksNode$1" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="dcbg-dcjpy-bcmonitoring" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="verification" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="khactainguyen &lt;<EMAIL>&gt;" />
        <option value="dinhns &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/DCPF-50714-fix-decode-nested-tuple-traceId" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="SWAP_SIDES_IN_COMPARE_BRANCHES" value="true" />
  </component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="DCPF-47702: test case 5,6 - configuration management" />
        <option name="rewordedMessage" value="DCPF-47682: implement test case 4, 5, 6" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="ecf2a7dffb87c9bf908fb4c77d36b820fe936960" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.5.18/c18967160fa1b855dd90a43d69e7bad0328cf19c/logback-classic-1.5.18-sources.jar!/ch/qos/logback/classic/Logger.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.spockframework/spock-core/2.4-M6-groovy-4.0/89dad1d1958a24d51e048ed3f4550abae8cc1f9e/spock-core-2.4-M6-groovy-4.0-sources.jar!/org/spockframework/runtime/ConditionNotSatisfiedError.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.spockframework/spock-core/2.4-M6-groovy-4.0/89dad1d1958a24d51e048ed3f4550abae8cc1f9e/spock-core-2.4-M6-groovy-4.0-sources.jar!/org/spockframework/runtime/MasterRunSupervisor.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.spockframework/spock-core/2.4-M6-groovy-4.0/89dad1d1958a24d51e048ed3f4550abae8cc1f9e/spock-core-2.4-M6-groovy-4.0-sources.jar!/org/spockframework/runtime/model/ErrorInfo.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/abi/4.14.0/705dc373ee09aa68910555cb2fae643334030090/abi-4.14.0-sources.jar!/org/web3j/abi/DefaultFunctionReturnDecoder.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/abi/4.14.0/705dc373ee09aa68910555cb2fae643334030090/abi-4.14.0-sources.jar!/org/web3j/abi/FunctionReturnDecoder.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/abi/4.14.0/705dc373ee09aa68910555cb2fae643334030090/abi-4.14.0-sources.jar!/org/web3j/abi/TypeDecoder.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.web3j/core/4.14.0/9f1389b3a4540a784476ecbcd44ce78a186f0368/core-4.14.0-sources.jar!/org/web3j/tx/Contract.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/software.amazon.awssdk/utils/2.31.50/c9c9ec8c3a5aa40d0ab6e226dcae766b6759e158/utils-2.31.50-sources.jar!/software/amazon/awssdk/utils/Logger.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yiQMDJlYRzg12oPkuWq2JKIcyb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle./Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc [test --tests \&quot;adhoc.event_monitoring.EventMonitoringITSpec\&quot;].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.AbiParserContentSpec.AbiEventInput should store and return all properties correctly.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.AbiParserContentSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.AbiParserContentSpec.parseAbi should handle events with null inputs.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.AbiParserTupleTest.testParseAbiWithMixedTypes.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.AbiParserTupleTest.testParseAbi_WithTupleArrayTypes_Success.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.ApplicationPropertiesProdITSpec.Should load all configuration properties correctly when env is 'prod'.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should handle invalid subscription check interval value gracefully.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should load all configuration properties correctly when env is 'test'.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should load all configuration properties correctly.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should load configuration based on test environment profile.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should not use LocalStack endpoint when env is not 'local'.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ConfigurationServiceITSpec.Should use LocalStack endpoint when env is 'local'.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should connection pool (max 10 connections) managed correctly, operations complete successfully.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should connection pool managed correctly, operations complete successfully.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should events successfully stores parsed events to DynamoDB Events table.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handle DynamoDB 400KB item size limit error without mocking.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handle DynamoDB operations gracefully under stress conditions.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handle connection pool exhaustion gracefully when EventDao save fails.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handle real DynamoDB 400KB item size limit error without mocking.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handles multiple events from same block correctly when processing new blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handles multiple events from same transaction correctly when processing pending transactions.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should handles transactions with empty events when processing new blocks.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should log error and return false when EventDao save fail of pending transaction using MockitoSpyBean.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should log error and return false when EventDao save fails using MockitoSpyBean.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should log error when saving event fails due to DynamoDbException.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should save events and block height when processing new blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should save events but don't save block height when processing pending transactions with same block number.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should updates block height correctly when processing new blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.Should updates block height correctly when processing pending transactions.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DataPersistenceITSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Download Sources.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceITSpec.Should fails to start when S3 bucket is inaccessible.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceITSpec.Should service start up successfully with command line runner and process ABI files.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceITSpec.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should correctly parses ABI files based on truffle environment variable.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should fails to start when S3 bucket is inaccessible.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should skip deeply nested files and only process direct child objects.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should skip objects that are not direct children of the current prefix.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should start fails when ABI file lacks required abi section.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should start fails when parsing malformed JSON.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.Should start fails when s3 connect timeout.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.DownloadAbiServiceIntegrationSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ErrorHandlingAndRecoveryITSpec.Should restart when handle invalid subscription check interval value invalid.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ErrorHandlingAndRecoveryITSpec.Should restart when websocket disconnect.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.ErrorHandlingAndRecoveryITSpec.Should retry when new transaction with transaction hash is null.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EthEventLogDaoSpec.convBlock2EventEntities should handle missing transaction receipts.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EthEventLogDaoSpec.decodeTupleArray should handle regular array inside tuple array - line 407 test.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EthEventLogDaoSpec.executor&quot;: &quot;Coverage&quot;,
    &quot;Gradle.EthEventLogDaoSpec.subscribeAll should call convBlock2EventEntities when block has transactions - line 122.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EthEventLogDaoSpec.subscribeAll should cover remaining async callback paths with forced execution.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EthEventLogDaoSpec.subscribeAll should execute lambda function in ethGetBlockByNumber call.executor&quot;: &quot;Coverage&quot;,
    &quot;Gradle.EthEventLogDaoSpec.subscribeAll should handle error callback with full coverage including shutdown and queue operations.executor&quot;: &quot;Coverage&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should detects and processes events from new blockchain blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should empty traceId in non-indexed values.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should event data correctly parsed into indexed and non-indexed values.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should event that don't contain traceId are processed successfully and no log in MDC.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should extracts traceId from event non-indexed values when present.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles blocks with zero block number.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles event with empty transaction hash.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles event with missing transaction hash.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles events that don't match any loaded ABI definitions.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles websocket connection failure at startup.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles websocket connection failure during monitoring.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should handles websocket disconnect during active subscription.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should process pending transactions from specified block height.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should processes events with dynamic array types correctly.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should processes events with nested tuple types correctly.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should processes events with tuple array types correctly.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should processes events with tuple types correctly.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should rejects blocks with zero block number.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should rejects events with empty transaction hash in new blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should rejects events with empty transaction hash.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should rejects events with missing transaction hash in new blocks.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should rejects events with missing transaction hash.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should run normally when timestamp against allowable block timestamp different seconds.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.Should run normally when timestamp is equal or less than allowable block timestamp different seconds.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.EventMonitoringITSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ForceBurnEventDecodeTest.should test raw log data structure (1).executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.ForceBurnEventDecodeTest.should test raw log data structure.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Service behavior when logging system fails.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should LoggingService PostConstruct method fail when environment configuration is invalid.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should appropriate log levels used for different scenarios.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should demonstrate LoggingService PostConstruct failure with invalid configuration.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should log include relevant context fields in StructuredLoggingContext.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should service fail to start when logging system initialization fails.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.Should service start fail when logging service fails to initialize.executor&quot;: &quot;Debug&quot;,
    &quot;Gradle.LoggingAndMonitoringITSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingServiceFailureITSpec.Should service fail to start when logging system initialization fails.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingServiceFailureITSpec.Should service fail when LoggingService throws exception during info logging.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LoggingServiceFailureITSpec.Should service run successfully with current configuration.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.MonitorEventServiceSpec.executor&quot;: &quot;Coverage&quot;,
    &quot;Gradle.StartupServiceITSpec.Should restart if DynamoDB connection Error.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceITSpec.Should start successfully with all dependencies available.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceITSpec.Should start successfully with empty ABI bucket (1).executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceITSpec.Should start successfully with empty ABI bucket.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceITSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceSpec.Should restart if DynamoDB connection Error.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceSpec.Should start successfully with empty ABI bucket.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceSpec.Should start successfully with empty DynamoDB BlockHeight table.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.StartupServiceSpec.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.dcbg-dcjpy-bcmonitoring-java [:test --tests \&quot;adhoc.startup.StartupServiceSpec\&quot;] (2).executor&quot;: &quot;Run&quot;,
    &quot;Gradle.dcbg-dcjpy-bcmonitoring-java [:test --tests \&quot;adhoc.startup.StartupServiceSpec\&quot;].executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;CSV-Groovy.csv.groovy&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feature/DCPF-50724-fix-wrong-BlockHeight-after-process-pending-transaction&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;dynamo&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/test/resources" />
      <recent name="$PROJECT_DIR$/src/test/groovy/adhoc/resources/abi_json/invalid_file" />
      <recent name="$PROJECT_DIR$/src/test/groovy/adhoc/resources/abi_json/truffle_type/3000" />
      <recent name="$PROJECT_DIR$/src/test/groovy/adhoc/resources/abi_json/hardhat_type" />
      <recent name="$PROJECT_DIR$/src/test/groovy/adhoc/resources/abi_json" />
    </key>
  </component>
  <component name="RubyModuleManagerSettings">
    <option name="blackListedRootsPaths">
      <list>
        <option value="$PROJECT_DIR$/src/test" />
        <option value="$PROJECT_DIR$" />
        <option value="$PROJECT_DIR$/src/main" />
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Gradle.EventMonitoringITSpec.Should processes events with dynamic array types correctly">
    <configuration name="DataPersistenceITSpec.Should connection pool managed correctly, operations complete successfully" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.data_persistence.DataPersistenceITSpec.Should connection pool managed correctly, operations complete successfully&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ErrorHandlingAndRecoveryITSpec.Should restart when websocket disconnect" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.error_handling.ErrorHandlingAndRecoveryITSpec.Should restart when websocket disconnect&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="EventMonitoringITSpec.Should processes events with dynamic array types correctly" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.event_monitoring.EventMonitoringITSpec.Should processes events with dynamic array types correctly&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="EventMonitoringITSpec.Should processes events with nested tuple types correctly" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.event_monitoring.EventMonitoringITSpec.Should processes events with nested tuple types correctly&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="EventMonitoringITSpec.Should processes events with tuple types correctly" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.event_monitoring.EventMonitoringITSpec.Should processes events with tuple types correctly&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="StartupServiceITSpec.Should start successfully with empty ABI bucket" type="GradleRunConfiguration" factoryName="Gradle">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":testAdhoc" />
            <option value="--tests" />
            <option value="&quot;adhoc.event_monitoring.EventMonitoringITSpec.*Should event data correctly parsed into indexed and non-indexed values*&quot;" />
            <option value="--tests" />
            <option value="&quot;adhoc.startup.StartupServiceITSpec.*Should start successfully with empty ABI bucket*&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="BcmonitoringApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="dcbg-dcjpy-bcmonitoring.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="dcbg-dcjpy-bcmonitoring-java" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Gradle.StartupServiceITSpec.Should start successfully with empty ABI bucket" />
      <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with dynamic array types correctly" />
      <item itemvalue="Gradle.DataPersistenceITSpec.Should connection pool managed correctly, operations complete successfully" />
      <item itemvalue="Gradle.ErrorHandlingAndRecoveryITSpec.Should restart when websocket disconnect" />
      <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with nested tuple types correctly" />
      <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with tuple types correctly" />
      <item itemvalue="Spring Boot.BcmonitoringApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with dynamic array types correctly" />
        <item itemvalue="Gradle.DataPersistenceITSpec.Should connection pool managed correctly, operations complete successfully" />
        <item itemvalue="Gradle.ErrorHandlingAndRecoveryITSpec.Should restart when websocket disconnect" />
        <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with tuple types correctly" />
        <item itemvalue="Gradle.EventMonitoringITSpec.Should processes events with nested tuple types correctly" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="58eef0f7-6fc0-48dc-a5e0-081fb8801951" name="Changes" comment="" />
      <created>1750314863911</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750314863911</updated>
      <workItem from="1752201498428" duration="92000" />
      <workItem from="1752201633964" duration="155000" />
      <workItem from="1752201837600" duration="234000" />
      <workItem from="1752202083686" duration="22934000" />
      <workItem from="1752231112025" duration="37873000" />
      <workItem from="1752576844562" duration="914000" />
      <workItem from="1752577778266" duration="26315000" />
      <workItem from="1752654563051" duration="11740000" />
      <workItem from="1752734331791" duration="21993000" />
      <workItem from="1753179103322" duration="627000" />
      <workItem from="1753179743541" duration="15161000" />
      <workItem from="1753241232120" duration="24199000" />
      <workItem from="1753726683043" duration="597000" />
      <workItem from="1753757587073" duration="4899000" />
      <workItem from="1753791515097" duration="599000" />
      <workItem from="1753792364224" duration="206000" />
      <workItem from="1753792587465" duration="95000" />
      <workItem from="1753792705831" duration="114000" />
      <workItem from="1753843341294" duration="16783000" />
      <workItem from="1754642281526" duration="14857000" />
      <workItem from="1755144412464" duration="35000" />
      <workItem from="1755144472239" duration="16961000" />
      <workItem from="1755225844034" duration="9463000" />
      <workItem from="1755237975896" duration="30714000" />
      <workItem from="1755849119376" duration="788000" />
      <workItem from="1755855662914" duration="8162000" />
      <workItem from="1756119975322" duration="620000" />
    </task>
    <task id="LOCAL-00002" summary="DCPF-44953: fix MoniterEventService UT to make 100% coverage">
      <option name="closed" value="true" />
      <created>1750418894159</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750418894159</updated>
    </task>
    <task id="LOCAL-00003" summary="DCPF-44953: delete some unused code">
      <option name="closed" value="true" />
      <created>1750498434359</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750498434359</updated>
    </task>
    <task id="LOCAL-00004" summary="DCPF-44955: update UT reflect to handling new subscribe flow">
      <option name="closed" value="true" />
      <created>1750528313693</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750528313693</updated>
    </task>
    <task id="LOCAL-00005" summary="DCPF-47673: refactor adhoc tests for ABI JSON upload flow">
      <option name="closed" value="true" />
      <created>1750562262548</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750562262548</updated>
    </task>
    <task id="LOCAL-00006" summary="DCPF-47673: refactor - create BaseAdhocIntegrationSpec to eliminate test code duplication&#10;            &#10;- Extract common S3/DynamoDB cleanup methods&#10;- Extract common Web3j mock setup&#10;- Isolate log appenders between test cases&#10;- Reduce code duplication by ~70 lines per test class">
      <option name="closed" value="true" />
      <created>1750566414635</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1750566414635</updated>
    </task>
    <task id="LOCAL-00007" summary="DCPF-47674: rebase and refactor to improve S3 timeout test case with proper timeout configuration">
      <option name="closed" value="true" />
      <created>1750573367292</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750573367292</updated>
    </task>
    <task id="LOCAL-00008" summary="DCPF-47674: delete some unused abi json files">
      <option name="closed" value="true" />
      <created>1750573922204</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750573922204</updated>
    </task>
    <task id="LOCAL-00009" summary="DCPF-47675: Fix Vo’s feedback - refactor ABI JSON folder structure and improve maintainability">
      <option name="closed" value="true" />
      <created>1750613280902</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1750613280902</updated>
    </task>
    <task id="LOCAL-00010" summary="DCPF-47675: Update UT and refactor web3jmock structure">
      <option name="closed" value="true" />
      <created>1750686724774</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1750686724774</updated>
    </task>
    <task id="LOCAL-00011" summary="DCPF-47675: Add more testcase for event monitoring IT and refactor some code">
      <option name="closed" value="true" />
      <created>1750701011205</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1750701011205</updated>
    </task>
    <task id="LOCAL-00012" summary="DCPF-47675: rebase latest source">
      <option name="closed" value="true" />
      <created>1750702110985</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1750702110985</updated>
    </task>
    <task id="LOCAL-00013" summary="DCPF-47702: rebase from DCPF-47701">
      <option name="closed" value="true" />
      <created>1750810095500</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1750810095500</updated>
    </task>
    <task id="LOCAL-00014" summary="DCPF-47702: add comment">
      <option name="closed" value="true" />
      <created>1750820581169</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1750820581169</updated>
    </task>
    <task id="LOCAL-00015" summary="DCPF-47702: refactor to application read properties from application properties">
      <option name="closed" value="true" />
      <created>1750833107439</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1750833107439</updated>
    </task>
    <task id="LOCAL-00016" summary="DCPF-47687: update todo">
      <option name="closed" value="true" />
      <created>1750841913743</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750841913743</updated>
    </task>
    <task id="LOCAL-00017" summary="DCPF-47687: fix block height table name">
      <option name="closed" value="true" />
      <created>1750845004201</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750845004201</updated>
    </task>
    <task id="LOCAL-00018" summary="DCPF-47687: update testcase correctly">
      <option name="closed" value="true" />
      <created>1750850206892</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750850206892</updated>
    </task>
    <task id="LOCAL-00019" summary="DCPF-47687: update testcase correctly">
      <option name="closed" value="true" />
      <created>1750851356562</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1750851356562</updated>
    </task>
    <task id="LOCAL-00020" summary="DCPF-47687: update testcase correctly">
      <option name="closed" value="true" />
      <created>1750851809711</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1750851809711</updated>
    </task>
    <task id="LOCAL-00021" summary="DCPF-47687: update testcase correctly">
      <option name="closed" value="true" />
      <created>1750851875346</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750851875346</updated>
    </task>
    <task id="LOCAL-00022" summary="DCPF-47687: update testcase correctly">
      <option name="closed" value="true" />
      <created>1750851939209</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1750851939209</updated>
    </task>
    <task id="LOCAL-00023" summary="DCPF-47687: fix block height table name">
      <option name="closed" value="true" />
      <created>1750861732625</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1750861732625</updated>
    </task>
    <task id="LOCAL-00024" summary="DCPF-47687: fix block height table name">
      <option name="closed" value="true" />
      <created>1750861859865</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1750861859865</updated>
    </task>
    <task id="LOCAL-00025" summary="DCPF-47687: fix block height table name">
      <option name="closed" value="true" />
      <created>1750861867831</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1750861867831</updated>
    </task>
    <task id="LOCAL-00026" summary="DCPF-47687: fix block height table name">
      <option name="closed" value="true" />
      <created>1750862073443</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1750862073443</updated>
    </task>
    <task id="LOCAL-00027" summary="DCPF-47683: refactor test case 6 for simplicity">
      <option name="closed" value="true" />
      <created>1750868302172</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1750868302172</updated>
    </task>
    <task id="LOCAL-00028" summary="DCPF-47683: refactor test case 6 for simplicity">
      <option name="closed" value="true" />
      <created>1750869423221</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1750869423221</updated>
    </task>
    <task id="LOCAL-00029" summary="DCPF-47684: refactor setup mock for pending transaction and new transaction">
      <option name="closed" value="true" />
      <created>1750961316694</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1750961316695</updated>
    </task>
    <task id="LOCAL-00030" summary="DCPF-47684: fix bug related to new transaction flow can't restarting when txhash is empty or missing">
      <option name="closed" value="true" />
      <created>1751007857664</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1751007857664</updated>
    </task>
    <task id="LOCAL-00031" summary="DCPF-47685: refactor setupMockWeb3jWithEvents to return block data by block number correctly">
      <option name="closed" value="true" />
      <created>1751176804463</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1751176804463</updated>
    </task>
    <task id="LOCAL-00032" summary="DCPF-47685: implement testcase 1, 2, 3 of data persistence">
      <option name="closed" value="true" />
      <created>1751181114912</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1751181114912</updated>
    </task>
    <task id="LOCAL-00033" summary="DCPF-47686: add DynamoDB connection pool with max 10 connections&#10;            &#10;- Implement connection pool with semaphore-based limiting&#10;- Update DAOs to use pooled connections&#10;- Add comprehensive connection pool testing&#10;- Ensure Java/Go behavior consistency">
      <option name="closed" value="true" />
      <created>1751212691441</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1751212691441</updated>
    </task>
    <task id="LOCAL-00034" summary="DCPF-47686: handle empty events consistently with Go implementation&#10;            &#10;- Create Transaction even with empty events list in EthEventLogDao&#10;- Add test coverage for empty events scenario&#10;- Move test helper to base class for reusability">
      <option name="closed" value="true" />
      <created>1751215996585</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1751215996585</updated>
    </task>
    <task id="LOCAL-00035" summary="DCPF-47686: handle empty events consistently with Go implementation&#10;            &#10;- Create Transaction even with empty events list in EthEventLogDao&#10;- Add test coverage for empty events scenario&#10;- Move test helper to base class for reusability">
      <option name="closed" value="true" />
      <created>1751244919934</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1751244919934</updated>
    </task>
    <task id="LOCAL-00036" summary="DCPF-48551 local it logging and monitoring implement">
      <option name="closed" value="true" />
      <created>1751808259918</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1751808259918</updated>
    </task>
    <task id="LOCAL-00037" summary="DCPF-48551 local it logging and monitoring implement">
      <option name="closed" value="true" />
      <created>1751809924284</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1751809924284</updated>
    </task>
    <task id="LOCAL-00038" summary="DCPF-48556 Correct the behaviors of AbiParser and EthEventLogDao">
      <option name="closed" value="true" />
      <created>1751811281550</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1751811281550</updated>
    </task>
    <task id="LOCAL-00039" summary="DCPF-48556 Correct the behaviors of AbiParser and EthEventLogDao">
      <option name="closed" value="true" />
      <created>1751812616071</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1751812616071</updated>
    </task>
    <task id="LOCAL-00040" summary="DCPF-48556 Correct the behaviors of AbiParser and EthEventLogDao">
      <option name="closed" value="true" />
      <created>1751817262954</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1751817262954</updated>
    </task>
    <task id="LOCAL-00041" summary="DCPF-48556 Correct the behaviors of AbiParser and EthEventLogDao">
      <option name="closed" value="true" />
      <created>1751826684507</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1751826684507</updated>
    </task>
    <task id="LOCAL-00042" summary="DCPF-48556: Refactor AbiParser and EthEventLogDao to improve event handling and parsing logic">
      <option name="closed" value="true" />
      <created>1751884651504</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1751884651504</updated>
    </task>
    <task id="LOCAL-00043" summary="DCPF-48556: Update EventMockFactory and MonitorEventService to use test identifiers and improve JSON handling">
      <option name="closed" value="true" />
      <created>1751887127538</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1751887127538</updated>
    </task>
    <task id="LOCAL-00044" summary="Refactor ABI parser to streamline tuple handling and remove unused methods">
      <option name="closed" value="true" />
      <created>1752193825020</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1752193825020</updated>
    </task>
    <task id="LOCAL-00045" summary="Refactor ABI parser and type converter for improved tuple handling and code clarity">
      <option name="closed" value="true" />
      <created>1752231169361</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1752231169361</updated>
    </task>
    <task id="LOCAL-00046" summary="DCPF-49663: add UT to handle tuple and dynamic types in ABI parsing">
      <option name="closed" value="true" />
      <created>1752546604595</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1752546604595</updated>
    </task>
    <task id="LOCAL-00047" summary="DCPF-49663: Update EthEventLogDao to handle DynamicArray in tuple decoding and enhance unit tests">
      <option name="closed" value="true" />
      <created>1752576337154</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1752576337154</updated>
    </task>
    <task id="LOCAL-00048" summary="DCPF-49663: Update EthEventLogDao and tests to handle transaction objects and log empty transactions">
      <option name="closed" value="true" />
      <created>1752604221695</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1752604221695</updated>
    </task>
    <task id="LOCAL-00049" summary="DCPF-50468: Add Web3j caller instance for RPC API calls in EthEventLogDao">
      <option name="closed" value="true" />
      <created>1752724916375</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1752724916375</updated>
    </task>
    <task id="LOCAL-00050" summary="Fix tuple array decoding in EthEventLogDao and update related tests">
      <option name="closed" value="true" />
      <created>1753255218779</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1753255218779</updated>
    </task>
    <option name="localTasksCounter" value="51" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature/DCPF-47683-local-implementation-blockchain-event-monitoring-3" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="DCPF-47687: update" />
    <MESSAGE value="DCPF-47682: implement test case 4, 5, 6" />
    <MESSAGE value="DCPF-47687: update testcase correctly" />
    <MESSAGE value="DCPF-47687: fix block height table name" />
    <MESSAGE value="DCPF-47683: implement test case 4,5,6 - blockchain event monitoring" />
    <MESSAGE value="DCPF-47683: implement test case 4,5,6 - blockchain event monitoring" />
    <MESSAGE value="DCPF-47683: refactor test case 6 for simplicity" />
    <MESSAGE value="DCPF-47684: refactor setup mock for pending transaction and new transaction" />
    <MESSAGE value="DCPF-47684: fix bug related to new transaction flow can't restarting when txhash is empty or missing" />
    <MESSAGE value="DCPF-47685: refactor setupMockWeb3jWithEvents to return block data by block number correctly" />
    <MESSAGE value="DCPF-47685: implement testcase 1, 2, 3 of data persistence" />
    <MESSAGE value="DCPF-47686: add DynamoDB connection pool with max 10 connections&#10;            &#10;- Implement connection pool with semaphore-based limiting&#10;- Update DAOs to use pooled connections&#10;- Add comprehensive connection pool testing&#10;- Ensure Java/Go behavior consistency" />
    <MESSAGE value="DCPF-47686: handle empty events consistently with Go implementation&#10;            &#10;- Create Transaction even with empty events list in EthEventLogDao&#10;- Add test coverage for empty events scenario&#10;- Move test helper to base class for reusability" />
    <MESSAGE value="DCPF-48551 local it logging and monitoring implement" />
    <MESSAGE value="DCPF-48556 Correct the behaviors of AbiParser and EthEventLogDao" />
    <MESSAGE value="DCPF-48556: Refactor AbiParser and EthEventLogDao to improve event handling and parsing logic" />
    <MESSAGE value="DCPF-48556: Update EventMockFactory and MonitorEventService to use test identifiers and improve JSON handling" />
    <MESSAGE value="Refactor ABI parser to streamline tuple handling and remove unused methods" />
    <MESSAGE value="Refactor ABI parser and type converter for improved tuple handling and code clarity" />
    <MESSAGE value="refactor: update ABI parsing to handle tuple and dynamic types" />
    <MESSAGE value="DCPF-49663: add UT to handle tuple and dynamic types in ABI parsing" />
    <MESSAGE value="DCPF-49663: Update EthEventLogDao to handle DynamicArray in tuple decoding and enhance unit tests" />
    <MESSAGE value="DCPF-49663: Update EthEventLogDao and tests to handle transaction objects and log empty transactions" />
    <MESSAGE value="DCPF-50468: Add Web3j caller instance for RPC API calls in EthEventLogDao" />
    <MESSAGE value="Fix tuple array decoding in EthEventLogDao and update related tests" />
    <option name="LAST_COMMIT_MESSAGE" value="Fix tuple array decoding in EthEventLogDao and update related tests" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/MonitoringRunnerConfig.java</url>
          <line>19</line>
          <option name="timeStamp" value="148" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///opt/homebrew/Cellar/openjdk@21/21.0.7/libexec/openjdk.jdk/Contents/Home/lib/src.zip!/java.base/java/lang/reflect/Constructor.java</url>
          <line>481</line>
          <option name="timeStamp" value="215" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home/lib/src.zip!/java.base/jdk/internal/reflect/BootstrapConstructorAccessorImpl.java</url>
          <line>44</line>
          <option name="timeStamp" value="218" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home/lib/src.zip!/java.base/jdk/internal/reflect/DelegatingConstructorAccessorImpl.java</url>
          <line>44</line>
          <option name="timeStamp" value="219" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home/lib/src.zip!/java.base/jdk/internal/reflect/InstantiationExceptionConstructorAccessorImpl.java</url>
          <line>46</line>
          <option name="timeStamp" value="220" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home/lib/src.zip!/java.base/jdk/internal/reflect/NativeConstructorAccessorImpl.java</url>
          <line>57</line>
          <option name="timeStamp" value="221" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar:///Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home/lib/src.zip!/java.base/java/lang/reflect/AccessibleObject.java</url>
          <line>672</line>
          <option name="timeStamp" value="222" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/groovy/adhoc/error_handling/ErrorHandlingAndRecoveryITSpec.groovy</url>
          <line>128</line>
          <option name="timeStamp" value="241" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/groovy/adhoc/error_handling/ErrorHandlingAndRecoveryITSpec.groovy</url>
          <line>132</line>
          <option name="timeStamp" value="242" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/groovy/adhoc/event_monitoring/EventMonitoringITSpec.groovy</url>
          <line>1717</line>
          <option name="timeStamp" value="244" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="org.web3j.abi.datatypes.Event" memberName="parameters" />
        <PinnedItemInfo parentTag="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$1" memberName="type" />
        <PinnedItemInfo parentTag="software.amazon.awssdk.core.client.config.SdkClientConfiguration" memberName="attributes" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$EthEventLogDaoSpec_subscribeAll_should_cover_remaining_async_callback_paths_with_forced_execution.ic" NAME="EthEventLogDaoSpec.subscribeAll should cover remaining async callback paths with forced execution Coverage Results" MODIFIED="1750590104375" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$EthEventLogDaoSpec_subscribeAll_should_handle_error_callback_with_full_coverage_including_shutdown_and_queue_operations.ic" NAME="EthEventLogDaoSpec.subscribeAll should handle error callback with full coverage including shutdown and queue operations Coverage Results" MODIFIED="1750728958583" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$MonitorEventServiceSpec.ic" NAME="MonitorEventServiceSpec Coverage Results" MODIFIED="1750417284329" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$DownloadAbiServiceIntegrationSpec_Should_correctly_parses_ABI_files_based_on_truffle_environment_variable.ic" NAME="DownloadAbiServiceIntegrationSpec.Should correctly parses ABI files based on truffle environment variable Coverage Results" MODIFIED="1750413790139" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$EventMonitoringITSpec_Should_detects_and_processes_events_from_new_blockchain_blocks.ic" NAME="EventMonitoringITSpec.Should detects and processes events from new blockchain blocks Coverage Results" MODIFIED="1750728210459" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$EthEventLogDaoSpec_subscribeAll_should_execute_lambda_function_in_ethGetBlockByNumber_call.ic" NAME="EthEventLogDaoSpec.subscribeAll should execute lambda function in ethGetBlockByNumber call Coverage Results" MODIFIED="1750729119982" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/dcbg_dcjpy_bcmonitoring$EthEventLogDaoSpec.ic" NAME="EthEventLogDaoSpec Coverage Results" MODIFIED="1751884204407" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
  </component>
</project>