<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">72</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">18m16.36s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/adhoc.abi.html">adhoc.abi</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>1m21.50s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.application_configuration.html">adhoc.application_configuration</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>33.703s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.data_persistence.html">adhoc.data_persistence</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>5m28.74s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.error_handling.html">adhoc.error_handling</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>3m2.73s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.event_monitoring.html">adhoc.event_monitoring</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>6m7.62s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.logging_monitoring.html">adhoc.logging_monitoring</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>41.887s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/adhoc.startup.html">adhoc.startup</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>1m0.18s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/adhoc.abi.DownloadAbiServiceITSpec.html">adhoc.abi.DownloadAbiServiceITSpec</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>1m21.50s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.application_configuration.ApplicationConfigurationDefaultITSpec.html">adhoc.application_configuration.ApplicationConfigurationDefaultITSpec</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>10.403s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.application_configuration.ApplicationConfigurationITSpec.html">adhoc.application_configuration.ApplicationConfigurationITSpec</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>22.834s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.application_configuration.ApplicationConfigurationProdITSpec.html">adhoc.application_configuration.ApplicationConfigurationProdITSpec</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.466s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.data_persistence.DataPersistenceITSpec.html">adhoc.data_persistence.DataPersistenceITSpec</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>5m28.74s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.error_handling.ErrorHandlingAndRecoveryITSpec.html">adhoc.error_handling.ErrorHandlingAndRecoveryITSpec</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>3m2.73s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.event_monitoring.EventMonitoringITSpec.html">adhoc.event_monitoring.EventMonitoringITSpec</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>6m7.62s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.logging_monitoring.LoggingAndMonitoringITSpec.html">adhoc.logging_monitoring.LoggingAndMonitoringITSpec</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>41.887s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/adhoc.startup.StartupServiceITSpec.html">adhoc.startup.StartupServiceITSpec</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>1m0.18s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:58:20</p>
</div>
</div>
</body>
</html>
