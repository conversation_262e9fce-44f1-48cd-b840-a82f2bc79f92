<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" tests="10" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:38.982Z" hostname="tungvt.local" time="0.013">
  <properties/>
  <testcase name="constructor should set rawType and typeArguments" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.007"/>
  <testcase name="constructor should handle single type argument" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.002"/>
  <testcase name="constructor should handle no type arguments" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.001"/>
  <testcase name="getActualTypeArguments should return type arguments array" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.0"/>
  <testcase name="getRawType should return raw type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.0"/>
  <testcase name="getOwnerType should always return null" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.001"/>
  <testcase name="should handle null raw type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.0"/>
  <testcase name="should handle null type arguments" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.001"/>
  <testcase name="should work with complex generic types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.0"/>
  <testcase name="should be usable as ParameterizedType interface" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
