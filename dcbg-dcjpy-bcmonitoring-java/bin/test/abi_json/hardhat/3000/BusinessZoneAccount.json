{"address": "0x853810cf1c0f005BFb3239341d390e78d916B82D", "abi": [{"type": "event", "anonymous": false, "name": "BalanceUpdateByIssueVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "BalanceUpdateByRedeemVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetActiveBusinessAccountWithZone", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncBusinessZoneBalance", "inputs": [{"type": "tuple", "name": "transferData", "indexed": false, "components": [{"type": "bytes32", "name": "transferType"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "fromValidatorId"}, {"type": "bytes32", "name": "toValidatorId"}, {"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}, {"type": "uint256", "name": "businessZoneBalance"}, {"type": "uint16", "name": "bizZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncBusinessZoneStatus", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "accountStatus", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "accountIdExistenceByZoneId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}]}, {"type": "function", "name": "addBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "balanceUpdateByIssueVoucher", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "balanceUpdateByRedeemVoucher", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "forceBurnAllBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint256", "name": "burnedAmount"}, {"type": "tuple[]", "name": "forceDischarge", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "dischargeAmount"}]}]}, {"type": "function", "name": "getBizAccountsAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}]}, {"type": "function", "name": "getBusinessZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}]}, {"type": "function", "name": "hasAccountByZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isActivatedByZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "setActiveBusinessAccountWithZone", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setBizAccountsAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setBizZoneTerminated", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": []}, {"type": "function", "name": "subtractBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "syncBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "params", "components": [{"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}]}], "outputs": []}, {"type": "function", "name": "syncBusinessZoneStatus", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xf2b3dbf6f97030e0f074a3f35a986aa867dcdc809a3918a618328e1b37ca5e99", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0x853810cf1c0f005BFb3239341d390e78d916B82D", "transactionIndex": 0, "gasUsed": "3969020", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x82f3b7e6f7f0b369e82d5c0e31e965262e38e3b45cfb3de25b7432a4c9fdb861", "blockNumber": 199, "cumulativeGasUsed": "3969020", "status": 1}}