name: "Update Manifest"

on:
  repository_dispatch:
    types: [update-manifest]

env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

jobs:
  update-manifest:
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.env }}
    timeout-minutes: 300

    steps:
      - uses: actions/checkout@v3

      - name: Update image tag for ${{ github.event.client_payload.apl }}
        run: |
          #!/bin/bash
          env="${{ github.event.client_payload.env }}"
          env_prefix=$(echo ${{ github.event.client_payload.env }} | sed -E 's/(^[a-z0-9]+)-.*$/\1/')
          dir="./${{ github.event.client_payload.apl }}/$env_prefix"
          files=($(ls "$dir"))
          for file in "${files[@]}"; do
              if [[ "$file" == *"${env}"* ]]; then
                sed -E 's/tag: "[0-9a-zA-Z]+"$/tag: "${{ github.event.client_payload.sha }}"/' ./$dir/${file} > $dir/${file}_tmp
                mv $dir/${file}_tmp $dir/${file}
              fi
          done

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v4
        with:
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}
          commit-message: 'Update manifest'
          title: Update manifest ${{ github.event.client_payload.apl }} ${{ github.event.client_payload.sha }}
          body: ""
          labels: ""
          branch: update-manifest-${{ github.event.client_payload.env }}-${{ github.event.client_payload.apl }}-${{ github.event.client_payload.sha }}

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.client_payload.env }}][${{ github.event.client_payload.apl }}] update-manifest job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: Successfully executed the update-manifest job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.client_payload.env }}][${{ github.event.client_payload.apl }}] update-manifest job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: The update-manifest job failed to execute.
