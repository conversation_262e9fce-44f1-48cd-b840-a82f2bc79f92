<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BlockHeightDao.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a> &gt; <span class="el_source">BlockHeightDao.java</span></div><h1>BlockHeightDao.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.DataAccessException;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

@Component
public class BlockHeightDao implements BlockHeightRepository {
  private final PooledDynamoDbService pooledDynamoDbService;
  private final BcmonitoringConfigurationProperties properties;
  private final LoggingService logger;

  public BlockHeightDao(
      PooledDynamoDbService pooledDynamoDbService,
      BcmonitoringConfigurationProperties properties,
<span class="fc" id="L28">      LoggingService logger) {</span>
<span class="fc" id="L29">    this.pooledDynamoDbService = pooledDynamoDbService;</span>
<span class="fc" id="L30">    this.logger = logger;</span>
<span class="fc" id="L31">    this.properties = properties;</span>
<span class="fc" id="L32">  }</span>

  /**
   * Retrieves a block height from the DynamoDB table using connection pool.
   *
   * @return The block height number.
   * @throws DataAccessException if there was an error during the operation.
   */
  public long get() {
    try {
<span class="fc" id="L42">      BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();</span>

      // Use pooled connection service for query operation
<span class="fc" id="L45">      return pooledDynamoDbService.executeWithConnection(</span>
          client -&gt; {
            // Create expression attribute values
<span class="fc" id="L48">            Map&lt;String, AttributeValue&gt; expressionValues = new HashMap&lt;&gt;();</span>
<span class="fc" id="L49">            expressionValues.put(&quot;:id&quot;, AttributeValue.builder().n(&quot;1&quot;).build());</span>

            // Create query request
            QueryRequest queryRequest =
<span class="fc" id="L53">                QueryRequest.builder()</span>
<span class="fc" id="L54">                    .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getBlockHeightTableName()))</span>
<span class="fc" id="L55">                    .keyConditionExpression(&quot;id = :id&quot;)</span>
<span class="fc" id="L56">                    .expressionAttributeValues(expressionValues)</span>
<span class="fc" id="L57">                    .build();</span>

            // Execute query
<span class="fc" id="L60">            QueryResponse response = client.query(queryRequest);</span>

<span class="fc" id="L62">            List&lt;BlockHeight&gt; blockHeights = buildBlockHeightsFromMapValue(response);</span>

            // Check if the response is empty
<span class="fc bfc" id="L65" title="All 2 branches covered.">            if (blockHeights.isEmpty()) {</span>
<span class="fc" id="L66">              return 0L; // Return 0 when no block heights found</span>
            }
<span class="fc" id="L68">            return blockHeights.getFirst().blockNumber; // Return the first block height's number</span>
          });

<span class="fc" id="L71">    } catch (DynamoDbException e) {</span>
<span class="fc" id="L72">      String errorMessage = &quot;Error retrieving block height from DynamoDB&quot;;</span>
<span class="fc" id="L73">      logger.error(errorMessage, e);</span>
<span class="fc" id="L74">      throw new DataAccessException(errorMessage, e);</span>
<span class="fc" id="L75">    } catch (Exception e) {</span>
<span class="fc" id="L76">      String errorMessage = &quot;Unexpected error retrieving block height&quot;;</span>
<span class="fc" id="L77">      logger.error(errorMessage, e);</span>
<span class="fc" id="L78">      throw new DataAccessException(errorMessage, e);</span>
    }
  }

  /**
   * * Builds a list of BlockHeight objects from a map of attribute values.
   *
   * @param response The response containing the block height data.
   * @return A list of BlockHeight objects.
   */
  @NotNull private static List&lt;BlockHeight&gt; buildBlockHeightsFromMapValue(QueryResponse response) {
<span class="fc" id="L89">    List&lt;BlockHeight&gt; blockHeights = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L90" title="All 2 branches covered.">    for (Map&lt;String, AttributeValue&gt; item : response.items()) {</span>
      BlockHeight blockHeight =
<span class="fc" id="L92">          BlockHeight.builder()</span>
<span class="fc" id="L93">              .id(Long.parseLong(item.get(&quot;id&quot;).n()))</span>
<span class="fc" id="L94">              .blockNumber(Long.parseLong(item.get(&quot;blockNumber&quot;).n()))</span>
<span class="fc" id="L95">              .build();</span>
<span class="fc" id="L96">      blockHeights.add(blockHeight);</span>
<span class="fc" id="L97">    }</span>
<span class="fc" id="L98">    return blockHeights;</span>
  }

  /**
   * Saves a block height to the DynamoDB table using connection pool.
   *
   * @param blockHeight The block height to save.
   * @return true if the operation was successful, false otherwise.
   */
  @Override
  public boolean save(BlockHeight blockHeight) {
<span class="fc" id="L109">    BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();</span>

    try {
      // Use pooled connection service for save operation
<span class="fc" id="L113">      pooledDynamoDbService.executeWithConnection(</span>
          client -&gt; {
<span class="fc" id="L115">            client.putItem(</span>
<span class="fc" id="L116">                PutItemRequest.builder()</span>
<span class="fc" id="L117">                    .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getBlockHeightTableName()))</span>
<span class="fc" id="L118">                    .item(blockHeight.toAttributeMap())</span>
<span class="fc" id="L119">                    .build());</span>
<span class="fc" id="L120">            return null;</span>
          });
<span class="fc" id="L122">      return true;</span>
<span class="fc" id="L123">    } catch (DynamoDbException dbException) {</span>
<span class="fc" id="L124">      String errorMessage = &quot;Failed to save block height: &quot; + blockHeight.blockNumber;</span>
<span class="fc" id="L125">      logger.error(errorMessage, dbException);</span>
      // We're returning false instead of throwing an exception to maintain backward compatibility
      // with existing code that expects a boolean return value
<span class="fc" id="L128">      return false;</span>
<span class="fc" id="L129">    } catch (Exception e) {</span>
<span class="fc" id="L130">      String errorMessage = &quot;Unexpected error saving block height: &quot; + blockHeight.blockNumber;</span>
<span class="fc" id="L131">      logger.error(errorMessage, e);</span>
<span class="fc" id="L132">      return false;</span>
    }
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>