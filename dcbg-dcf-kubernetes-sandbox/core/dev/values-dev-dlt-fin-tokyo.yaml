envName: dev-dlt-fin

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/dev-dlt-fin-core
    tag: "4ff3d60"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/dev-dlt-fin-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/dev-dlt-fin-tokyo-core-alb-tg/7c89790e890f15cf

configMap:
  DB_BASE: "dev-dlt-fin-tokyo-rds-cluster.cluster-c7cq2a4ksxf8.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_rzNkYPWxz/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_rzNkYPWxz"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"
