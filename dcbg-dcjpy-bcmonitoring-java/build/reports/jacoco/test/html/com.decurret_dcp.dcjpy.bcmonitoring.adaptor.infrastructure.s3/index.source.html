<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <span class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</span></div><h1>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 1,614</td><td class="ctr2">100%</td><td class="bar">1 of 86</td><td class="ctr2">98%</td><td class="ctr1">1</td><td class="ctr2">87</td><td class="ctr1">0</td><td class="ctr2">364</td><td class="ctr1">0</td><td class="ctr2">44</td><td class="ctr1">0</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a1"><a href="AbiTypeConverter.java.html" class="el_source">AbiTypeConverter.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="765" alt="765"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="57" height="10" title="24" alt="24"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">23</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">158</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a0"><a href="AbiParser.java.html" class="el_source">AbiParser.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="95" height="10" title="607" alt="607"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="50" alt="50"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g0">47</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">144</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">22</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">3</td></tr><tr><td id="a3"><a href="S3ClientAdaptor.java.html" class="el_source">S3ClientAdaptor.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="118" alt="118"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i3">27</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="StructGenerator.java.html" class="el_source">StructGenerator.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="107" alt="107"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">91%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">28</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a2"><a href="ParameterizedTypeImpl.java.html" class="el_source">ParameterizedTypeImpl.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="17" alt="17"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>