<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:39.172Z" hostname="tungvt.local" time="0.025">
  <properties/>
  <testcase name="should throw ConfigurationException when S3 bucket name is empty" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.009"/>
  <testcase name="should throw ConfigurationException when S3 bucket name is null" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.002"/>
  <testcase name="should process multiple prefixes, objects and download json files" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.004"/>
  <testcase name="should process single prefix, objects and download json files" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.002"/>
  <testcase name="should skip non-json files" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should handle empty object list" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should throw S3Exception when listing prefixes fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should throw S3Exception when listing objects fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should only process direct child objects" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should throw S3Exception when object retrieval fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should throw IOException when parsing fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <testcase name="should process single prefix, name start with dot" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi.DownloadAbiInteractorSpec" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
