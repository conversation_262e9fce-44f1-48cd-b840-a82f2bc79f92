# Development Environment Configuration
spring.application.name=Bcmonitoring

server.port=2345

# AWS Configuration for Development Environment
aws.region=${DEV_AWS_REGION:ap-northeast-1}
aws.access-key-id=${DEV_AWS_ACCESS_KEY:dev-access-key}
aws.secret-access-key=${DEV_AWS_SECRET_KEY:dev-secret-key}
aws.dynamodb.region=${DEV_DYNAMODB_REGION:ap-northeast-1}
aws.dynamodb.table-prefix=${DEV_DYNAMODB_TABLE_PREFIX:dev}
aws.dynamodb.endpoint=${DEV_DYNAMODB_ENDPOINT:https://dynamodb.ap-northeast-1.amazonaws.com}
aws.s3.bucket-name=${DEV_S3_BUCKET_NAME:abijson-dev-bucket}
aws.s3.region=${DEV_S3_REGION:ap-northeast-1}
aws.dynamodb.events-table-name=${DEV_EVENTS_TABLE_NAME:Events}
aws.dynamodb.block-height-table-name=${DEV_BLOCK_HEIGHT_TABLE_NAME:BlockHeight}

# WebSocket Configuration for Development
websocket.uri.host=${DEV_WEBSOCKET_URI_HOST:dev-websocket.example.com}
websocket.uri.port=${DEV_WEBSOCKET_URI_PORT:8545}

# Subscription Configuration for Development
subscription.check-interval=${DEV_SUBSCRIPTION_CHECK_INTERVAL:3000}
subscription.allowable-block-timestamp-diff-sec=${DEV_ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC:2}

# Environment Configuration
env=dev
abi-format=${DEV_ABI_FORMAT:hardhat}
eager-start=${DEV_EAGER_START:true}

# LocalStack Configuration (not used in dev, but kept for compatibility)
local-stack.end-point=${DEV_LOCALSTACK_ENDPOINT:http://localhost:4566}
local-stack.region=${DEV_LOCALSTACK_REGION:ap-northeast-1}
local-stack.access-key=${DEV_LOCALSTACK_ACCESS_KEY:access123}
local-stack.secret-key=${DEV_LOCALSTACK_SECRET_KEY:secret123}
