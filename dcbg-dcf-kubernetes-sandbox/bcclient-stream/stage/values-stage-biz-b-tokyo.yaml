envName: stage-biz-b

# minReplicas: 3 最小構成のためデフォルトを使用
# maxReplicas: 5 最小構成のためデフォルトを使用

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-biz-b-bcclient-stream
    tag: "aaaaaa"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-biz-b-tokyo-eks-pod-bcclient-stream

configMap:
  WEBSOCKET_URI_HOST: "stage-biz-b-tokyo-besu-lb-743da07f7c75175b.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "stage-biz-b-tokyo-fin-besu-lb-d402adfc62c702e5.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "stage-biz-b-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "stage-biz-b-tokyo-abijson"