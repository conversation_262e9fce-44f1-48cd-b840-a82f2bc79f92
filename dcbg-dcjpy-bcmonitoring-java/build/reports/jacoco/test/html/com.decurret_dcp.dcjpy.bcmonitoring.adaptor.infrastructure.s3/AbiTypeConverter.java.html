<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AbiTypeConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; <span class="el_source">AbiTypeConverter.java</span></div><h1>AbiTypeConverter.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.UnsupportedTypeException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.*;
import org.web3j.abi.datatypes.primitive.Byte;
import org.web3j.abi.datatypes.primitive.Char;
import org.web3j.abi.datatypes.primitive.Double;
import org.web3j.abi.datatypes.primitive.Float;
import org.web3j.abi.datatypes.primitive.Long;
import org.web3j.abi.datatypes.primitive.Short;
import org.web3j.protocol.core.methods.response.AbiDefinition;

<span class="fc" id="L23">public class AbiTypeConverter {</span>
<span class="fc" id="L24">  private static final Logger log = LoggerFactory.getLogger(AbiTypeConverter.class);</span>
<span class="fc" id="L25">  private static final Map&lt;String, Class&lt;? extends Type&lt;?&gt;&gt;&gt; web3TypeMap = new HashMap&lt;&gt;();</span>
  public static final String TUPLE_TYPE = &quot;tuple&quot;;
  public static final String TUPLE_ARRAY_TYPE = &quot;tuple[]&quot;;

  static {
    // Web3j boolean type
<span class="fc" id="L31">    web3TypeMap.put(DCFConst.BOOL, Bool.class);</span>
<span class="fc" id="L32">    web3TypeMap.put(DCFConst.BOOLEAN, Bool.class);</span>

    // Web3j string type
<span class="fc" id="L35">    web3TypeMap.put(DCFConst.STRING, Utf8String.class);</span>

    // Web3j address type
<span class="fc" id="L38">    web3TypeMap.put(DCFConst.ADDRESS, Address.class);</span>

    // Web3j primitive type
<span class="fc" id="L41">    web3TypeMap.put(DCFConst.DOUBLE, Double.class);</span>
<span class="fc" id="L42">    web3TypeMap.put(DCFConst.FLOAT, Float.class);</span>
<span class="fc" id="L43">    web3TypeMap.put(DCFConst.LONG, Long.class);</span>
<span class="fc" id="L44">    web3TypeMap.put(DCFConst.SHORT, Short.class);</span>
<span class="fc" id="L45">    web3TypeMap.put(DCFConst.CHAR, Char.class);</span>
<span class="fc" id="L46">    web3TypeMap.put(DCFConst.BYTE, Byte.class);</span>

    // Web3j uint types
<span class="fc" id="L49">    web3TypeMap.put(DCFConst.UINT, Uint.class);</span>
<span class="fc" id="L50">    web3TypeMap.put(DCFConst.UINT_8, Uint8.class);</span>
<span class="fc" id="L51">    web3TypeMap.put(DCFConst.UINT_16, Uint16.class);</span>
<span class="fc" id="L52">    web3TypeMap.put(DCFConst.UINT_24, Uint24.class);</span>
<span class="fc" id="L53">    web3TypeMap.put(DCFConst.UINT_32, Uint32.class);</span>
<span class="fc" id="L54">    web3TypeMap.put(DCFConst.UINT_40, Uint40.class);</span>
<span class="fc" id="L55">    web3TypeMap.put(DCFConst.UINT_48, Uint48.class);</span>
<span class="fc" id="L56">    web3TypeMap.put(DCFConst.UINT_56, Uint56.class);</span>
<span class="fc" id="L57">    web3TypeMap.put(DCFConst.UINT_64, Uint64.class);</span>
<span class="fc" id="L58">    web3TypeMap.put(DCFConst.UINT_72, Uint72.class);</span>
<span class="fc" id="L59">    web3TypeMap.put(DCFConst.UINT_80, Uint80.class);</span>
<span class="fc" id="L60">    web3TypeMap.put(DCFConst.UINT_88, Uint88.class);</span>
<span class="fc" id="L61">    web3TypeMap.put(DCFConst.UINT_96, Uint96.class);</span>
<span class="fc" id="L62">    web3TypeMap.put(DCFConst.UINT_104, Uint104.class);</span>
<span class="fc" id="L63">    web3TypeMap.put(DCFConst.UINT_112, Uint112.class);</span>
<span class="fc" id="L64">    web3TypeMap.put(DCFConst.UINT_120, Uint120.class);</span>
<span class="fc" id="L65">    web3TypeMap.put(DCFConst.UINT_128, Uint128.class);</span>
<span class="fc" id="L66">    web3TypeMap.put(DCFConst.UINT_136, Uint136.class);</span>
<span class="fc" id="L67">    web3TypeMap.put(DCFConst.UINT_144, Uint144.class);</span>
<span class="fc" id="L68">    web3TypeMap.put(DCFConst.UINT_152, Uint152.class);</span>
<span class="fc" id="L69">    web3TypeMap.put(DCFConst.UINT_160, Uint160.class);</span>
<span class="fc" id="L70">    web3TypeMap.put(DCFConst.UINT_168, Uint168.class);</span>
<span class="fc" id="L71">    web3TypeMap.put(DCFConst.UINT_176, Uint176.class);</span>
<span class="fc" id="L72">    web3TypeMap.put(DCFConst.UINT_184, Uint184.class);</span>
<span class="fc" id="L73">    web3TypeMap.put(DCFConst.UINT_192, Uint192.class);</span>
<span class="fc" id="L74">    web3TypeMap.put(DCFConst.UINT_200, Uint200.class);</span>
<span class="fc" id="L75">    web3TypeMap.put(DCFConst.UINT_208, Uint208.class);</span>
<span class="fc" id="L76">    web3TypeMap.put(DCFConst.UINT_216, Uint216.class);</span>
<span class="fc" id="L77">    web3TypeMap.put(DCFConst.UINT_224, Uint224.class);</span>
<span class="fc" id="L78">    web3TypeMap.put(DCFConst.UINT_232, Uint232.class);</span>
<span class="fc" id="L79">    web3TypeMap.put(DCFConst.UINT_240, Uint240.class);</span>
<span class="fc" id="L80">    web3TypeMap.put(DCFConst.UINT_248, Uint248.class);</span>
<span class="fc" id="L81">    web3TypeMap.put(DCFConst.UINT_256, Uint256.class);</span>

    // Web3j int types
<span class="fc" id="L84">    web3TypeMap.put(DCFConst.INT, Int.class);</span>
<span class="fc" id="L85">    web3TypeMap.put(DCFConst.INT_8, Int8.class);</span>
<span class="fc" id="L86">    web3TypeMap.put(DCFConst.INT_16, Int16.class);</span>
<span class="fc" id="L87">    web3TypeMap.put(DCFConst.INT_24, Int24.class);</span>
<span class="fc" id="L88">    web3TypeMap.put(DCFConst.INT_32, Int32.class);</span>
<span class="fc" id="L89">    web3TypeMap.put(DCFConst.INT_40, Int40.class);</span>
<span class="fc" id="L90">    web3TypeMap.put(DCFConst.INT_48, Int48.class);</span>
<span class="fc" id="L91">    web3TypeMap.put(DCFConst.INT_56, Int56.class);</span>
<span class="fc" id="L92">    web3TypeMap.put(DCFConst.INT_64, Int64.class);</span>
<span class="fc" id="L93">    web3TypeMap.put(DCFConst.INT_72, Int72.class);</span>
<span class="fc" id="L94">    web3TypeMap.put(DCFConst.INT_80, Int80.class);</span>
<span class="fc" id="L95">    web3TypeMap.put(DCFConst.INT_88, Int88.class);</span>
<span class="fc" id="L96">    web3TypeMap.put(DCFConst.INT_96, Int96.class);</span>
<span class="fc" id="L97">    web3TypeMap.put(DCFConst.INT_104, Int104.class);</span>
<span class="fc" id="L98">    web3TypeMap.put(DCFConst.INT_112, Int112.class);</span>
<span class="fc" id="L99">    web3TypeMap.put(DCFConst.INT_120, Int120.class);</span>
<span class="fc" id="L100">    web3TypeMap.put(DCFConst.INT_128, Int128.class);</span>
<span class="fc" id="L101">    web3TypeMap.put(DCFConst.INT_136, Int136.class);</span>
<span class="fc" id="L102">    web3TypeMap.put(DCFConst.INT_144, Int144.class);</span>
<span class="fc" id="L103">    web3TypeMap.put(DCFConst.INT_152, Int152.class);</span>
<span class="fc" id="L104">    web3TypeMap.put(DCFConst.INT_160, Int160.class);</span>
<span class="fc" id="L105">    web3TypeMap.put(DCFConst.INT_168, Int168.class);</span>
<span class="fc" id="L106">    web3TypeMap.put(DCFConst.INT_176, Int176.class);</span>
<span class="fc" id="L107">    web3TypeMap.put(DCFConst.INT_184, Int184.class);</span>
<span class="fc" id="L108">    web3TypeMap.put(DCFConst.INT_192, Int192.class);</span>
<span class="fc" id="L109">    web3TypeMap.put(DCFConst.INT_200, Int200.class);</span>
<span class="fc" id="L110">    web3TypeMap.put(DCFConst.INT_208, Int208.class);</span>
<span class="fc" id="L111">    web3TypeMap.put(DCFConst.INT_216, Int216.class);</span>
<span class="fc" id="L112">    web3TypeMap.put(DCFConst.INT_224, Int224.class);</span>
<span class="fc" id="L113">    web3TypeMap.put(DCFConst.INT_232, Int232.class);</span>
<span class="fc" id="L114">    web3TypeMap.put(DCFConst.INT_240, Int240.class);</span>
<span class="fc" id="L115">    web3TypeMap.put(DCFConst.INT_248, Int248.class);</span>
<span class="fc" id="L116">    web3TypeMap.put(DCFConst.INT_256, Int256.class);</span>

    // Web3j dynamic bytes type
<span class="fc" id="L119">    web3TypeMap.put(DCFConst.BYTES, DynamicBytes.class);</span>

    // Web3j bytes types
<span class="fc" id="L122">    web3TypeMap.put(DCFConst.BYTES_1, Bytes1.class);</span>
<span class="fc" id="L123">    web3TypeMap.put(DCFConst.BYTES_2, Bytes2.class);</span>
<span class="fc" id="L124">    web3TypeMap.put(DCFConst.BYTES_3, Bytes3.class);</span>
<span class="fc" id="L125">    web3TypeMap.put(DCFConst.BYTES_4, Bytes4.class);</span>
<span class="fc" id="L126">    web3TypeMap.put(DCFConst.BYTES_5, Bytes5.class);</span>
<span class="fc" id="L127">    web3TypeMap.put(DCFConst.BYTES_6, Bytes6.class);</span>
<span class="fc" id="L128">    web3TypeMap.put(DCFConst.BYTES_7, Bytes7.class);</span>
<span class="fc" id="L129">    web3TypeMap.put(DCFConst.BYTES_8, Bytes8.class);</span>
<span class="fc" id="L130">    web3TypeMap.put(DCFConst.BYTES_9, Bytes9.class);</span>
<span class="fc" id="L131">    web3TypeMap.put(DCFConst.BYTES_10, Bytes10.class);</span>
<span class="fc" id="L132">    web3TypeMap.put(DCFConst.BYTES_11, Bytes11.class);</span>
<span class="fc" id="L133">    web3TypeMap.put(DCFConst.BYTES_12, Bytes12.class);</span>
<span class="fc" id="L134">    web3TypeMap.put(DCFConst.BYTES_13, Bytes13.class);</span>
<span class="fc" id="L135">    web3TypeMap.put(DCFConst.BYTES_14, Bytes14.class);</span>
<span class="fc" id="L136">    web3TypeMap.put(DCFConst.BYTES_15, Bytes15.class);</span>
<span class="fc" id="L137">    web3TypeMap.put(DCFConst.BYTES_16, Bytes16.class);</span>
<span class="fc" id="L138">    web3TypeMap.put(DCFConst.BYTES_17, Bytes17.class);</span>
<span class="fc" id="L139">    web3TypeMap.put(DCFConst.BYTES_18, Bytes18.class);</span>
<span class="fc" id="L140">    web3TypeMap.put(DCFConst.BYTES_19, Bytes19.class);</span>
<span class="fc" id="L141">    web3TypeMap.put(DCFConst.BYTES_20, Bytes20.class);</span>
<span class="fc" id="L142">    web3TypeMap.put(DCFConst.BYTES_21, Bytes21.class);</span>
<span class="fc" id="L143">    web3TypeMap.put(DCFConst.BYTES_22, Bytes22.class);</span>
<span class="fc" id="L144">    web3TypeMap.put(DCFConst.BYTES_23, Bytes23.class);</span>
<span class="fc" id="L145">    web3TypeMap.put(DCFConst.BYTES_24, Bytes24.class);</span>
<span class="fc" id="L146">    web3TypeMap.put(DCFConst.BYTES_25, Bytes25.class);</span>
<span class="fc" id="L147">    web3TypeMap.put(DCFConst.BYTES_26, Bytes26.class);</span>
<span class="fc" id="L148">    web3TypeMap.put(DCFConst.BYTES_27, Bytes27.class);</span>
<span class="fc" id="L149">    web3TypeMap.put(DCFConst.BYTES_28, Bytes28.class);</span>
<span class="fc" id="L150">    web3TypeMap.put(DCFConst.BYTES_29, Bytes29.class);</span>
<span class="fc" id="L151">    web3TypeMap.put(DCFConst.BYTES_30, Bytes30.class);</span>
<span class="fc" id="L152">    web3TypeMap.put(DCFConst.BYTES_31, Bytes31.class);</span>
<span class="fc" id="L153">    web3TypeMap.put(DCFConst.BYTES_32, Bytes32.class);</span>
<span class="fc" id="L154">  }</span>

  /**
   * Converts a Solidity type string into a corresponding {@link TypeReference} instance used by
   * Web3j.
   *
   * &lt;p&gt;This method looks up the given Solidity type in a predefined type mapping, and creates a
   * {@code TypeReference&lt;?&gt;} for use in encoding/decoding smart contract data.
   *
   * @param solidityType the Solidity type as a string (e.g., {@code &quot;uint256&quot;}, {@code &quot;string&quot;},
   *     {@code &quot;bytes32&quot;})
   * @param isIndexed whether the parameter is indexed (applicable for event parameters)
   * @return a {@code TypeReference&lt;?&gt;} corresponding to the given Solidity type
   */
  public static TypeReference&lt;?&gt; convertType(
      String solidityType, boolean isIndexed, List&lt;AbiDefinition.NamedType&gt; components) {
<span class="fc bfc" id="L170" title="All 2 branches covered.">    if (web3TypeMap.containsKey(solidityType)) {</span>
<span class="fc" id="L171">      Class&lt;? extends Type&lt;?&gt;&gt; clazz = web3TypeMap.get(solidityType);</span>
<span class="fc" id="L172">      return createTypeReference(clazz, isIndexed);</span>
    } else {
      try {
<span class="fc" id="L175">        return createStructType(solidityType, components, isIndexed);</span>
<span class="fc" id="L176">      } catch (Exception e) {</span>
<span class="fc" id="L177">        log.error(&quot;Error creating dynamic struct type: {}&quot;, e.getMessage());</span>
<span class="fc" id="L178">        throw new UnsupportedTypeException(&quot;Error creating dynamic struct&quot;);</span>
      }
    }
  }

  /**
   * Creates a {@link TypeReference} instance for the given Web3j ABI type class.
   *
   * &lt;p&gt;This method allows dynamic creation of {@code TypeReference&lt;T&gt;} without the need to manually
   * declare each specific type (e.g., {@code Uint256}, {@code Address}, etc.).
   *
   * @param clazz the class representing a Web3j ABI type (e.g., {@code Uint8.class}, {@code
   *     Utf8String.class})
   * @param isIndexed true if the parameter should be marked as indexed (used in event logs)
   * @param &lt;T&gt; the Web3j ABI type extending {@code Type&lt;?&gt;}
   * @return a {@code TypeReference&lt;T&gt;} instance corresponding to the given type
   */
  private static &lt;T extends Type&lt;?&gt;&gt; TypeReference&lt;T&gt; createTypeReference(
      Class&lt;T&gt; clazz, boolean isIndexed) {
<span class="fc" id="L197">    return new TypeReference&lt;&gt;(isIndexed) {</span>
      @Override
      public java.lang.reflect.Type getType() {
<span class="fc" id="L200">        return clazz;</span>
      }
    };
  }

  /**
   * Creates a {@link TypeReference} instance for the given Web3j ABI type class.
   *
   * &lt;p&gt;This method allows dynamic creation of {@code TypeReference&lt;T&gt;} without the need to manually
   * declare each specific type (e.g., {@code Uint256}, {@code Address}, etc.).
   *
   * @param type Solidity type
   * @param components AbiDefinition.NamedType List
   * @param indexed true if the parameter should be marked as indexed (used in event logs)
   * @return a {@code TypeReference&lt;T&gt;} instance corresponding to the given type
   * @throws Exception if any type cannot convert to Type
   */
  private static TypeReference&lt;?&gt; createStructType(
      String type, List&lt;AbiDefinition.NamedType&gt; components, boolean indexed) throws Exception {
<span class="fc bfc" id="L219" title="All 2 branches covered.">    if (type.contains(TUPLE_TYPE)) {</span>
      // Create Type list from components, handling nested tuples recursively
<span class="fc" id="L221">      List&lt;Class&lt;? extends Type&lt;?&gt;&gt;&gt; fieldTypes = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L222" title="All 2 branches covered.">      for (AbiDefinition.NamedType component : components) {</span>
<span class="fc" id="L223">        String solidityType = component.getType();</span>
<span class="fc" id="L224">        Class&lt;? extends Type&lt;?&gt;&gt; fieldType =</span>
<span class="fc" id="L225">            resolveComponentType(solidityType, component.getComponents());</span>
<span class="fc" id="L226">        fieldTypes.add(fieldType);</span>
<span class="fc" id="L227">      }</span>

      // Use ByteBuddy for create Dynamic Struct
<span class="fc" id="L230">      Class&lt;? extends Type&lt;?&gt;&gt; structClass = StructGenerator.generateStructClass(fieldTypes);</span>
<span class="fc bfc" id="L231" title="All 2 branches covered.">      if (type.equals(TUPLE_ARRAY_TYPE)) {</span>
        // Handle array of tuples
<span class="fc" id="L233">        return extractTypeReferenceDynamicArray(structClass);</span>
      }

      // Return TypeReference
<span class="fc" id="L237">      return TypeReference.create(structClass, indexed);</span>
    }

    // Handle type dynamic array as: uint256[], bytes32[], etc.
<span class="fc bfc" id="L241" title="All 2 branches covered.">    if (type.endsWith(&quot;[]&quot;)) {</span>
<span class="fc" id="L242">      String elementTypeStr = type.replace(&quot;[]&quot;, &quot;&quot;);</span>
<span class="fc" id="L243">      Class&lt;? extends org.web3j.abi.datatypes.Type&lt;?&gt;&gt; elementType =</span>
<span class="fc" id="L244">          web3TypeMap.get(elementTypeStr);</span>

<span class="fc bfc" id="L246" title="All 2 branches covered.">      if (elementType == null) {</span>
<span class="fc" id="L247">        throw new UnsupportedTypeException(&quot;Unsupported array element type: &quot; + elementTypeStr);</span>
      }

<span class="fc" id="L250">      return extractTypeReferenceDynamicArray(elementType);</span>
    }

    // Throw unsupported type if not detected type
<span class="fc" id="L254">    throw new UnsupportedTypeException(&quot;Unsupported type: &quot; + type);</span>
  }

  /**
   * Extract TypeReference for dynamic array
   *
   * @param structClass the struct class
   * @return the TypeReference for dynamic array
   */
  @NotNull private static TypeReference&lt;?&gt; extractTypeReferenceDynamicArray(
      Class&lt;? extends Type&lt;?&gt;&gt; structClass) {
<span class="fc" id="L265">    java.lang.reflect.Type dynamicArrayType =</span>
        new ParameterizedTypeImpl(DynamicArray.class, structClass);

<span class="fc" id="L268">    return new TypeReference&lt;DynamicArray&lt;? extends Type&gt;&gt;() {</span>
      @Override
      public java.lang.reflect.Type getType() {
<span class="fc" id="L271">        return dynamicArrayType;</span>
      }
    };
  }

  /**
   * Resolves the component type, handling both simple types and nested tuples recursively.
   *
   * @param solidityType the Solidity type string (e.g., &quot;uint256&quot;, &quot;tuple&quot;)
   * @param components the components list for tuple types (null for simple types)
   * @return the resolved Class representing the Web3j type
   * @throws Exception if the type cannot be resolved
   */
  private static Class&lt;? extends Type&lt;?&gt;&gt; resolveComponentType(
      String solidityType, List&lt;AbiDefinition.NamedType&gt; components) throws Exception {

    // Handle simple types first
<span class="fc" id="L288">    Class&lt;? extends Type&lt;?&gt;&gt; simpleType = web3TypeMap.get(solidityType);</span>
<span class="fc bfc" id="L289" title="All 2 branches covered.">    if (simpleType != null) {</span>
<span class="fc" id="L290">      return simpleType;</span>
    }

    // Handle tuple types recursively
<span class="fc bfc" id="L294" title="All 6 branches covered.">    if (solidityType.contains(TUPLE_TYPE) &amp;&amp; components != null &amp;&amp; !components.isEmpty()) {</span>
<span class="fc" id="L295">      log.info(&quot;Processing nested tuple with {} components&quot;, components.size());</span>
      // Recursively resolve nested tuple components
<span class="fc" id="L297">      List&lt;Class&lt;? extends Type&lt;?&gt;&gt;&gt; nestedFieldTypes = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L298" title="All 2 branches covered.">      for (AbiDefinition.NamedType nestedComponent : components) {</span>
<span class="fc" id="L299">        nestedFieldTypes.add(</span>
<span class="fc" id="L300">            resolveComponentType(nestedComponent.getType(), nestedComponent.getComponents()));</span>
<span class="fc" id="L301">      }</span>
      // Generate struct class for the nested tuple
<span class="fc" id="L303">      return StructGenerator.generateStructClass(nestedFieldTypes);</span>
    }

<span class="fc" id="L306">    throw new UnsupportedTypeException(</span>
        &quot;Unsupported type: &quot;
            + solidityType
<span class="fc bfc" id="L309" title="All 2 branches covered.">            + (components != null ? &quot; with &quot; + components.size() + &quot; components&quot; : &quot;&quot;));</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>