#!/bin/bash -e

# EKS Cluster https://github.com/decurret-lab/dcbg-dcf-terraform-sandbox/blob/b94293e19ba04ee81d4f29c0fe5a7bde035acf09/Modules/EKS/main.tf#L85
CLUSTER_NAME=$1
ENV=$2
ALBC_VERSION=$3
REGION=$4
REGION_NAME=$5


if [[ -z "${CLUSTER_NAME}" ]]; then
  echo "error: please set cluster name (e.g. prod-fin-a-tokyo-eks-cluster)" 1>&2
  exit 1
fi

if [[ -z "${ENV}" ]]; then
  echo "error: please set environment name (e.g. dev)" 1>&2
  exit 1
fi

if [[ -z "${ALBC_VERSION}" ]]; then
  echo "error: please set albc version (e.g. v2.2.0)" 1>&2
  exit 1
fi

if [[ -z "${REGION}" ]]; then
  echo "error: please set region (e.g. ap-northeast-1 or ap-northeast-3)" 1>&2
  exit 1
fi

if [[ -z "${REGION_NAME}" ]]; then
  echo "error: please set region (e.g. tokyo or osaka)" 1>&2
  exit 1
fi

if [ ! -e "$(which helm)" ]; then
  echo "error: helm is not installed or not in the PATH" 1>&2
  exit 1
fi

VPC_ID=$(aws eks describe-cluster \
  --name "${CLUSTER_NAME}" \
  --region "${REGION}" \
  --output text \
  --query 'cluster.resourcesVpcConfig.vpcId')
# ALBC Role https://github.com/decurret-lab/dcbg-dcf-terraform-sandbox/blob/b94293e19ba04ee81d4f29c0fe5a7bde035acf09/Modules/EKS/main.tf#L301
ROLE_ARN=$(aws iam get-role \
  --role-name  "${ENV}-${REGION_NAME}-albc-role" \
  --region "${REGION}" \
  --output text \
  --query 'Role.Arn')

# chart install
helm repo add eks https://aws.github.io/eks-charts
helm repo update eks

# chart apply
aws eks update-kubeconfig --name "${CLUSTER_NAME}" --region ${REGION}
kubectl apply -k "github.com/aws/eks-charts/stable/aws-load-balancer-controller/crds?ref=master"

echo -e "\nCLUSTER_NAME:\t${CLUSTER_NAME}"
echo -e "VPC_ID:\t\t${VPC_ID}"
echo -e "ROLE_ARN:\t${ROLE_ARN}"
echo -e "ALBC_VERSION:\t${ALBC_VERSION}\n"

# check the variable to determine 'install' or 'upgrade'
HELM_COMMAND="install"
if [[ ${ALBC_UPDATE} -eq 1 ]]; then
  HELM_COMMAND="upgrade"
fi

# image.repository: ap-northeast-1 https://docs.aws.amazon.com/ja_jp/eks/latest/userguide/add-ons-images.html
COMMAND=$(cat<<-EOF
  helm ${HELM_COMMAND} aws-load-balancer-controller eks/aws-load-balancer-controller \
    --set clusterName="${CLUSTER_NAME}" \
    --set serviceAccount.create=true \
    --set image.repository=************.dkr.ecr.${REGION}.amazonaws.com/amazon/aws-load-balancer-controller \
    --set image.tag="${ALBC_VERSION}" \
    --set region=${REGION} \
    --set vpcId="${VPC_ID}" \
    --set serviceAccount.name=aws-load-balancer-controller \
    --set serviceAccount.annotations."eks\.amazonaws\.com/role-arn"="${ROLE_ARN}" \
    --set clusterSecretsPermissions.allowAllSecrets=true \
    -n kube-system
EOF)

eval ${COMMAND}
