kind: ConfigMap
apiVersion: v1
metadata:
  name: aws-logging
  namespace: aws-observability
data:
  parsers.conf: |
    [PARSER]
        Name crio
        Format Regex
        Regex ^(?<time>[^ ]+) (?<stream>stdout|stderr) (?<logtag>P|F) (?<log>.*)$
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z
        Decode_Field_As json log

  filters.conf: |
    [FILTER]
        Name parser
        Match *
        Key_name log
        Parser crio

    [FILTER]
        Name kubernetes
        Match kube.*
        Merge_Log On
        Buffer_Size 0
        Kube_Meta_Cache_TTL 300s

  output.conf: |
    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-ibc_relayer*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/relayer
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-ibc_newrelic-infra-agent*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/newrelic-infra-agent
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-ibc_prometheus*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/prometheus
        log_stream_prefix from-fluent-bit-
        auto_create_group false
