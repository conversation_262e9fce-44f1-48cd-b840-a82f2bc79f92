<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventDao.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a> &gt; <span class="el_source">EventDao.java</span></div><h1>EventDao.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.DataAccessException;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;

@Component
public class EventDao implements EventRepository {
  private final PooledDynamoDbService pooledDynamoDbService;
  private final BcmonitoringConfigurationProperties properties;
  private final LoggingService logger;

  public EventDao(
      PooledDynamoDbService pooledDynamoDbService,
      BcmonitoringConfigurationProperties properties,
<span class="fc" id="L21">      LoggingService logger) {</span>
<span class="fc" id="L22">    this.pooledDynamoDbService = pooledDynamoDbService;</span>
<span class="fc" id="L23">    this.properties = properties;</span>
<span class="fc" id="L24">    this.logger = logger;</span>
<span class="fc" id="L25">  }</span>

  /**
   * Saves an event to the DynamoDB table using connection pool.
   *
   * @param event The event to save.
   * @return true if the operation was successful, false otherwise.
   * @throws DataAccessException if there was an error during the operation.
   */
  @Override
  public boolean save(@NotNull Event event) {
<span class="fc" id="L36">    BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();</span>
    try {
      // Use pooled connection service (similar to Go's newConnection/releaseConnection pattern)
<span class="fc" id="L39">      pooledDynamoDbService.executeWithConnection(</span>
          client -&gt; {
<span class="fc" id="L41">            client.putItem(</span>
<span class="fc" id="L42">                PutItemRequest.builder()</span>
<span class="fc" id="L43">                    .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getEventsTableName()))</span>
<span class="fc" id="L44">                    .item(event.toAttributeMap())</span>
<span class="fc" id="L45">                    .build());</span>
<span class="fc" id="L46">            return null;</span>
          });
<span class="fc" id="L48">      return true;</span>
<span class="fc" id="L49">    } catch (DynamoDbException dynamoDbException) {</span>
<span class="fc" id="L50">      String errorMessage =</span>
          &quot;Failed to save event: &quot; + event.transactionHash + &quot;, logIndex: &quot; + event.logIndex;
<span class="fc" id="L52">      logger.error(errorMessage, dynamoDbException);</span>
<span class="fc" id="L53">      return false;</span>
<span class="fc" id="L54">    } catch (Exception e) {</span>
<span class="fc" id="L55">      String errorMessage =</span>
          &quot;Unexpected error saving event: &quot;
              + event.transactionHash
              + &quot;, logIndex: &quot;
              + event.logIndex;
<span class="fc" id="L60">      logger.error(errorMessage, e);</span>
<span class="fc" id="L61">      return false;</span>
    }
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>