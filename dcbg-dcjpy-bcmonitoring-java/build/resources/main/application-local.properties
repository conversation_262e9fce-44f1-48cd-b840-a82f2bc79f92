# Local Environment Configuration
spring.application.name=Bcmonitoring

server.port=2345

# AWS Configuration for Local Environment
aws.region=ap-northeast-1
aws.access-key-id=dummy
aws.secret-access-key=dummy
aws.dynamodb.region=ap-northeast-1
aws.dynamodb.table-prefix=local
aws.dynamodb.endpoint=http://localstack:4566
aws.s3.bucket-name=abijson-local-bucket
aws.s3.region=ap-northeast-1
aws.dynamodb.events-table-name=Events
aws.dynamodb.block-height-table-name=BlockHeight

# WebSocket Configuration for Local
websocket.uri.host=localhost
websocket.uri.port=18541

# Subscription Configuration for Local
subscription.check-interval=3000
subscription.allowable-block-timestamp-diff-sec=2

# Environment Configuration
env=local
abi-format=hardhat
eager-start=true

# LocalStack Configuration
local-stack.end-point=http://localstack:4566
local-stack.region=ap-northeast-1
local-stack.access-key=access123
local-stack.secret-key=secret123
