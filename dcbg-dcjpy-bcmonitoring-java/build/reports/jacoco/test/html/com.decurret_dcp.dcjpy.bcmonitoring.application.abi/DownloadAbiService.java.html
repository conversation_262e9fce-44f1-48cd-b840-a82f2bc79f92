<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DownloadAbiService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.application.abi</a> &gt; <span class="el_source">DownloadAbiService.java</span></div><h1>DownloadAbiService.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.application.abi;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ConfigurationException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.model.CommonPrefix;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;

@Service
public class DownloadAbiService {
  private final LoggingService logger;
  private final S3AbiRepository s3AbiRepository;
<span class="fc" id="L26">  private final ObjectMapper mapper = new ObjectMapper();</span>
  private final AbiParser abiParser;
  private final BcmonitoringConfigurationProperties properties;

  public DownloadAbiService(
      LoggingService logger,
      S3AbiRepository s3AbiRepository,
      AbiParser ethereumService,
<span class="fc" id="L34">      BcmonitoringConfigurationProperties properties) {</span>
<span class="fc" id="L35">    this.logger = logger;</span>
<span class="fc" id="L36">    this.s3AbiRepository = s3AbiRepository;</span>
<span class="fc" id="L37">    this.abiParser = ethereumService;</span>
<span class="fc" id="L38">    this.properties = properties;</span>
<span class="fc" id="L39">  }</span>

  /**
   * Execute the ABI download and parsing process.
   *
   * @throws S3Exception if an error occurs during S3 operations
   * @throws ConfigurationException if there is an error in the configuration
   * @throws IOException if there is an error parsing ABI content
   */
  public void execute() throws S3Exception, ConfigurationException, IOException {
    // Retrieve the S3 bucket name from configuration properties
<span class="fc" id="L50">    String bucketName = properties.getAws().getS3().getBucketName();</span>
<span class="fc" id="L51">    logger.info(&quot;downloading abi files... bucket_name={}&quot;, bucketName);</span>

    // Check if bucket name is configured
<span class="fc bfc" id="L54" title="All 4 branches covered.">    if (bucketName == null || bucketName.isEmpty()) {</span>
<span class="fc" id="L55">      String errorMessage = &quot;S3 bucket name is not configured&quot;;</span>
<span class="fc" id="L56">      logger.error(errorMessage);</span>
<span class="fc" id="L57">      throw new ConfigurationException(errorMessage);</span>
    }

    // List all common prefixes (folders) in the S3 bucket
<span class="fc" id="L61">    List&lt;CommonPrefix&gt; commonPrefixes =</span>
<span class="fc" id="L62">        s3AbiRepository.listCommonPrefixesObjects(bucketName, DCFConst.SLASH);</span>
<span class="fc bfc" id="L63" title="All 2 branches covered.">    if (commonPrefixes == null) {</span>
<span class="fc" id="L64">      String errorMessage = &quot;Failed to list S3 CommonPrefixes objects&quot;;</span>
<span class="fc" id="L65">      logger.error(errorMessage);</span>
<span class="fc" id="L66">      throw new S3Exception(errorMessage);</span>
    }

    // Iterate through each common prefix (folder)
<span class="fc bfc" id="L70" title="All 2 branches covered.">    for (CommonPrefix prefix : commonPrefixes) {</span>
      // Remove the trailing slash from the prefix
<span class="fc" id="L72">      String prefixWithoutTrailingSlash =</span>
<span class="fc" id="L73">          prefix.prefix().substring(0, prefix.prefix().length() - 1);</span>

      // List all objects within the current prefix
<span class="fc" id="L76">      ListObjectsV2Response listOutput =</span>
<span class="fc" id="L77">          s3AbiRepository.listObjects(bucketName, prefixWithoutTrailingSlash);</span>
<span class="fc bfc" id="L78" title="All 2 branches covered.">      if (listOutput == null) {</span>
<span class="fc" id="L79">        String errorMessage =</span>
            &quot;Failed to list S3 objects with prefix: &quot; + prefixWithoutTrailingSlash;
<span class="fc" id="L81">        logger.error(errorMessage);</span>
<span class="fc" id="L82">        throw new S3Exception(errorMessage);</span>
      }

      // Iterate through each object in the current prefix
<span class="fc bfc" id="L86" title="All 2 branches covered.">      for (S3Object obj : listOutput.contents()) {</span>
<span class="fc" id="L87">        String objKey = obj.key();</span>

        // Skip objects that are not direct children of the current prefix
<span class="fc bfc" id="L90" title="All 4 branches covered.">        if (objKey.chars().filter(ch -&gt; ch == '/').count() &gt; 1) {</span>
<span class="fc" id="L91">          continue;</span>
        }

        // Get the file extension of the object
<span class="fc" id="L95">        Path path = FileSystems.getDefault().getPath(objKey);</span>
<span class="fc" id="L96">        String extension = getFileExtension(path.getFileName().toString());</span>

        // Skip objects that do not have a .json extension
<span class="fc bfc" id="L99" title="All 2 branches covered.">        if (!&quot;.json&quot;.equals(extension)) {</span>
<span class="fc" id="L100">          logger.info(&quot;This object will be skipped because the extension is not .json: {}&quot;, objKey);</span>
<span class="fc" id="L101">          continue;</span>
        }

        // Retrieve the object from S3
<span class="fc" id="L105">        logger.info(&quot;getting s3 abi object. bucketName={}, objKey={}&quot;, bucketName, objKey);</span>
<span class="fc" id="L106">        InputStream inputStream = s3AbiRepository.getObject(bucketName, objKey);</span>
<span class="fc bfc" id="L107" title="All 2 branches covered.">        if (inputStream == null) {</span>
<span class="fc" id="L108">          String errorMessage = &quot;Failed to get S3 abi object: &quot; + objKey;</span>
<span class="fc" id="L109">          logger.error(errorMessage);</span>
<span class="fc" id="L110">          throw new S3Exception(errorMessage);</span>
        }

        // Parse the ABI content of the object
        try {
<span class="fc" id="L115">          abiParser.parseAbiContent(inputStream, objKey, Date.from(obj.lastModified()));</span>
<span class="fc" id="L116">        } catch (IOException e) {</span>
<span class="fc" id="L117">          String errorMessage = &quot;Failed to parse S3 abi object: &quot; + objKey;</span>
<span class="fc" id="L118">          logger.error(errorMessage, e);</span>
<span class="fc" id="L119">          throw new IOException(errorMessage, e);</span>
<span class="fc" id="L120">        }</span>
<span class="fc" id="L121">      }</span>
<span class="fc" id="L122">    }</span>
<span class="fc" id="L123">  }</span>

  private String getFileExtension(String filename) {
<span class="fc" id="L126">    int lastDotIndex = filename.lastIndexOf(DCFConst.DOT);</span>
<span class="fc bfc" id="L127" title="All 2 branches covered.">    return lastDotIndex &gt; 0 ? filename.substring(lastDotIndex) : DCFConst.EMPTY;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>