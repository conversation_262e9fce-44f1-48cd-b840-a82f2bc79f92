<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - AbiTypeConverterSpec</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>AbiTypeConverterSpec</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; AbiTypeConverterSpec</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">123</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.036s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">constructor should be callable for completeness</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should call getType method in dynamic array TypeReference</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'address' with indexed=false to class org.web3j.abi.datatypes.Address</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bool' with indexed=true to class org.web3j.abi.datatypes.Bool</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'boolean' with indexed=true to class org.web3j.abi.datatypes.Bool</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'byte' with indexed=true to class org.web3j.abi.datatypes.primitive.Byte</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes' with indexed=false to class org.web3j.abi.datatypes.DynamicBytes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes1' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes1</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes10' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes10</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes11' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes11</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes12' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes12</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes13' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes13</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes14' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes14</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes15' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes15</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes16' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes16</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes17' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes17</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes18' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes18</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes19' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes19</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes2' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes2</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes20' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes20</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes21' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes21</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes22' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes22</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes23' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes23</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes24' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes24</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes25' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes25</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes26' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes26</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes27' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes27</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes28' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes28</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes29' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes29</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes3' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes3</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes30' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes30</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes31' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes31</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes32' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes32</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes4' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes4</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes5' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes5</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes6' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes6</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes7' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes7</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes8' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes8</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'bytes9' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes9</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'char' with indexed=false to class org.web3j.abi.datatypes.primitive.Char</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'double' with indexed=false to class org.web3j.abi.datatypes.primitive.Double</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'float' with indexed=false to class org.web3j.abi.datatypes.primitive.Float</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int104' with indexed=false to class org.web3j.abi.datatypes.generated.Int104</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int112' with indexed=false to class org.web3j.abi.datatypes.generated.Int112</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int120' with indexed=false to class org.web3j.abi.datatypes.generated.Int120</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int128' with indexed=false to class org.web3j.abi.datatypes.generated.Int128</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int136' with indexed=false to class org.web3j.abi.datatypes.generated.Int136</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int144' with indexed=false to class org.web3j.abi.datatypes.generated.Int144</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int152' with indexed=false to class org.web3j.abi.datatypes.generated.Int152</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int16' with indexed=false to class org.web3j.abi.datatypes.generated.Int16</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int160' with indexed=false to class org.web3j.abi.datatypes.generated.Int160</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int168' with indexed=false to class org.web3j.abi.datatypes.generated.Int168</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int176' with indexed=false to class org.web3j.abi.datatypes.generated.Int176</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int184' with indexed=false to class org.web3j.abi.datatypes.generated.Int184</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int192' with indexed=false to class org.web3j.abi.datatypes.generated.Int192</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int200' with indexed=false to class org.web3j.abi.datatypes.generated.Int200</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int208' with indexed=false to class org.web3j.abi.datatypes.generated.Int208</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int216' with indexed=false to class org.web3j.abi.datatypes.generated.Int216</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int224' with indexed=false to class org.web3j.abi.datatypes.generated.Int224</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int232' with indexed=false to class org.web3j.abi.datatypes.generated.Int232</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int24' with indexed=false to class org.web3j.abi.datatypes.generated.Int24</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int240' with indexed=false to class org.web3j.abi.datatypes.generated.Int240</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int248' with indexed=false to class org.web3j.abi.datatypes.generated.Int248</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int256' with indexed=false to class org.web3j.abi.datatypes.generated.Int256</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int32' with indexed=false to class org.web3j.abi.datatypes.generated.Int32</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int40' with indexed=false to class org.web3j.abi.datatypes.generated.Int40</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int48' with indexed=false to class org.web3j.abi.datatypes.generated.Int48</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int56' with indexed=false to class org.web3j.abi.datatypes.generated.Int56</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int64' with indexed=false to class org.web3j.abi.datatypes.generated.Int64</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int72' with indexed=false to class org.web3j.abi.datatypes.generated.Int72</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int8' with indexed=false to class org.web3j.abi.datatypes.generated.Int8</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int80' with indexed=false to class org.web3j.abi.datatypes.generated.Int80</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int88' with indexed=false to class org.web3j.abi.datatypes.generated.Int88</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'int96' with indexed=false to class org.web3j.abi.datatypes.generated.Int96</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'long' with indexed=false to class org.web3j.abi.datatypes.primitive.Long</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'short' with indexed=false to class org.web3j.abi.datatypes.primitive.Short</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'string' with indexed=false to class org.web3j.abi.datatypes.Utf8String</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint' with indexed=false to class org.web3j.abi.datatypes.Uint</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint104' with indexed=false to class org.web3j.abi.datatypes.generated.Uint104</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint112' with indexed=false to class org.web3j.abi.datatypes.generated.Uint112</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint120' with indexed=false to class org.web3j.abi.datatypes.generated.Uint120</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint128' with indexed=false to class org.web3j.abi.datatypes.generated.Uint128</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint136' with indexed=false to class org.web3j.abi.datatypes.generated.Uint136</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint144' with indexed=false to class org.web3j.abi.datatypes.generated.Uint144</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint152' with indexed=false to class org.web3j.abi.datatypes.generated.Uint152</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint16' with indexed=false to class org.web3j.abi.datatypes.generated.Uint16</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint160' with indexed=false to class org.web3j.abi.datatypes.generated.Uint160</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint168' with indexed=false to class org.web3j.abi.datatypes.generated.Uint168</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint176' with indexed=false to class org.web3j.abi.datatypes.generated.Uint176</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint184' with indexed=false to class org.web3j.abi.datatypes.generated.Uint184</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint192' with indexed=false to class org.web3j.abi.datatypes.generated.Uint192</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint200' with indexed=false to class org.web3j.abi.datatypes.generated.Uint200</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint208' with indexed=false to class org.web3j.abi.datatypes.generated.Uint208</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint216' with indexed=false to class org.web3j.abi.datatypes.generated.Uint216</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint224' with indexed=false to class org.web3j.abi.datatypes.generated.Uint224</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint232' with indexed=false to class org.web3j.abi.datatypes.generated.Uint232</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint24' with indexed=false to class org.web3j.abi.datatypes.generated.Uint24</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint240' with indexed=false to class org.web3j.abi.datatypes.generated.Uint240</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint248' with indexed=false to class org.web3j.abi.datatypes.generated.Uint248</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint256' with indexed=false to class org.web3j.abi.datatypes.generated.Uint256</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint32' with indexed=false to class org.web3j.abi.datatypes.generated.Uint32</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint40' with indexed=false to class org.web3j.abi.datatypes.generated.Uint40</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint48' with indexed=false to class org.web3j.abi.datatypes.generated.Uint48</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint56' with indexed=false to class org.web3j.abi.datatypes.generated.Uint56</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint64' with indexed=false to class org.web3j.abi.datatypes.generated.Uint64</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint72' with indexed=false to class org.web3j.abi.datatypes.generated.Uint72</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint8' with indexed=false to class org.web3j.abi.datatypes.generated.Uint8</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint80' with indexed=false to class org.web3j.abi.datatypes.generated.Uint80</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint88' with indexed=false to class org.web3j.abi.datatypes.generated.Uint88</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should convert Solidity type 'uint96' with indexed=false to class org.web3j.abi.datatypes.generated.Uint96</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle dynamic array type</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle nested tuple components</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle non-tuple type with components</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle non-tuple type with null components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple array type with components</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple type with components</td>
<td class="success">0.013s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple type with empty components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple type with empty components in resolveComponentType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple type with null components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tuple type with null components in resolveComponentType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle unsupported type gracefully</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should throw exception for unsupported array element type</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should throw exception for unsupported nested component type</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.958176+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Unsupported type: unsupportedType&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.975+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Unsupported array element type: unsupportedElement&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.975562+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Processing nested tuple with 1 components&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;level_value&quot;:20000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.977952+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Unsupported type: unsupportedNestedType&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.978554+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Cannot invoke \&quot;java.util.List.iterator()\&quot; because \&quot;components\&quot; is null&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.980685+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Unsupported type: customType&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
{&quot;@timestamp&quot;:&quot;2025-07-30T17:39:38.981114+07:00&quot;,&quot;@version&quot;:&quot;1&quot;,&quot;message&quot;:&quot;Error creating dynamic struct type: Unsupported type: customType&quot;,&quot;logger_name&quot;:&quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter&quot;,&quot;thread_name&quot;:&quot;Test worker&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;level_value&quot;:40000,&quot;application&quot;:&quot;bcmonitoring&quot;}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
