<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" tests="10" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:39.062Z" hostname="tungvt.local" time="0.087">
  <properties/>
  <testcase name="should get object from S3" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.051"/>
  <testcase name="should handle exception when getting object from S3" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.005"/>
  <testcase name="should list common prefixes of objects" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.014"/>
  <testcase name="should handle empty common prefixes" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.001"/>
  <testcase name="should list objects with prefix" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.008"/>
  <testcase name="should handle exception when listing objects" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.0"/>
  <testcase name="should handle generic exception when getting object from S3" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.001"/>
  <testcase name="should handle exception with message when getting object" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.003"/>
  <testcase name="should handle exception with message when listing common prefixes" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.001"/>
  <testcase name="should handle exception with message when listing objects" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
