Index: src/test/groovy/adhoc/config/ConfigurationServiceITSpec.groovy
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package adhoc.config\n\nimport adhoc.base.BaseAdhocITSpec\nimport adhoc.helper.AdhocHelper\nimport com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig\nimport java.util.concurrent.TimeUnit\nimport org.springframework.beans.factory.annotation.Autowired\nimport org.springframework.boot.CommandLineRunner\nimport org.springframework.boot.test.context.SpringBootTest\nimport org.springframework.context.ApplicationContext\n\n@SpringBootTest(\nclasses = [BcmonitoringApplication.class],\nwebEnvironment = SpringBootTest.WebEnvironment.NONE\n)\nclass ConfigurationServiceITSpec extends BaseAdhocITSpec {\n\n\t@Autowired\n\tWeb3jConfig web3jConfig\n\n\t@Override\n\tWeb3jConfig getWeb3jConfig() {\n\t\treturn web3jConfig\n\t}\n\n\t@Autowired\n\tApplicationContext applicationContext\n\n\t@Autowired\n\tBcmonitoringConfigurationProperties properties\n\n\tdef setupSpec() {\n\t\tsetupSpecCommon()\n\t}\n\n\tdef cleanupSpec() {\n\t\tcleanupSpecCommon()\n\t}\n\n\tdef setup() {\n\t\tsetupCommon()\n\t\t// Upload real ABI files to\n\t\tAdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, \"3000\", [\n\t\t\t\"Token\",\n\t\t\t\"Account\",\n\t\t\t\"Provider\"\n\t\t])\n\n\t\t// Setup mock event stream and pending event\n\t\tsetUpEventStream(Collections.emptyList())\n\t\tsetUpPendingEvent(Collections.emptyList())\n\t}\n\n\tdef cleanup() {\n\t\tcleanupCommon()\n\t}\n\n\t/**\n\t * Successful load all configuration properties correctly to run application\n\t * Verifies all configuration properties correctly\n\t * Expected: Service logs \"Started bc monitoring\"\n\t */\n\tdef \"Should load all configuration properties correctly\"() {\n\t\tgiven: \"Running application with command line runner\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner.class)\n\n\t\twhen: \"Command line runner is executed\"\n\t\tscheduler.schedule({\n\t\t\tAdhocHelper.stopBCMonitoring()\n\t\t}, 10, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"Configuration properties are loaded correctly\"\n\t\tproperties.getAws().getRegion() == \"ap-northeast-1\"\n\t\tproperties.getAws().getAccessKeyId() == \"dummy\"\n\t\tproperties.getAws().getSecretAccessKey() == \"dummy\"\n\t\tproperties.getAws().getDynamodb().getRegion() == \"ap-northeast-1\"\n\t\tproperties.getAws().getDynamodb().getEndpoint() == \"http://localstack:4566\"\n\t\tproperties.getAws().getS3().getBucketName() == \"abijson-local-bucket\"\n\t\tproperties.getEthereum().getEndpoint() == \"\"\n\t\tproperties.getWebsocket().getUri().getHost() == \"localhost\"\n\t\tproperties.getWebsocket().getUri().getPort() == \"18541\"\n\t\tproperties.getSubscription().getCheckInterval() == \"3000\"\n\t\tproperties.getSubscription().getAllowableBlockTimestampDiffSec() == \"2\"\n\t\tproperties.getEnv() == \"local\"\n\t\tproperties.getAbiFormat() == \"hardhat\"\n\t\t//this properties != true cuz when run application in test need set this value == false to avoid infinite loop\n\t\t!properties.isEagerStart()\n\n\t\tand: \"Log should indicate bc monitoring has started\"\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Started bc monitoring\") }\n\t}\n\n\t/**\n\t * Successful run application with env is 'local'\n\t * Verifies ENV variable value is 'local'\n\t * Expected: Service logs \"Started bc monitoring\"\n\t */\n\tdef \"Should use LocalStack endpoint when env is 'local'\"() {\n\t\tgiven:\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner)\n\n\t\twhen: \"Run command line with -f\"\n\t\tscheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"env should be 'local'\"\n\t\tproperties.getEnv() == \"local\"\n\n\t\tand: \"Log should indicate bc monitoring has started\"\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Started bc monitoring\") }\n\t}\n\n\t/**\n\t * Successful run application with env is 'test'\n\t * Verifies ENV variable value is 'test'\n\t * Expected: Service logs \"Started bc monitoring\"\n\t */\n\tdef \"Should not use LocalStack endpoint when env is not 'local'\"() {\n\t\tgiven:\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner)\n\t\tproperties.setEnv(\"test\")\n\n\t\twhen: \"Run command line with -f\"\n\t\tscheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"env should be 'test'\"\n\t\tproperties.getEnv() == \"test\"\n\n\t\tand: \"Log should indicate bc monitoring has started\"\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Started bc monitoring\") }\n\n\t\tcleanup:\n\t\t// Reset the value to avoid affecting other tests\n\t\tproperties.setEnv(\"local\")\n\t}\n\n\t/**\n\t * Successful run application with default value when optional value missing\n\t * Verifies ENV variable value == default value\n\t * Expected: Service logs \"Started bc monitoring\"\n\t */\n\tdef \"Should use default values when optional env variables are missing\"() {\n\t\tgiven: \"CommandLineRunner is available\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner)\n\n\t\tand: \"System properties for optional env vars are cleared to simulate missing environment variables\"\n\t\tSystem.clearProperty(\"ENV\")\n\n\t\twhen: \"Application is started via command line runner\"\n\t\tscheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen: \"Application uses default values from application.properties\"\n\t\tproperties.getEnv() == \"local\"\n\n\t\tand: \"Application should still start monitoring\"\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Started bc monitoring\") }\n\t}\n\n\t/**\n\t * Run application fails with invalid subscription check interval value\n\t * Expected: Service logs \"Failed to convert checkInterval\"\n\t */\n\tdef \"Should handle invalid subscription check interval value gracefully\"() {\n\t\tgiven: \"Invalid system property is set\"\n\t\tproperties.getSubscription().setCheckInterval(\"invalid\")\n\n\t\tand: \"Application is started via command line runner\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner)\n\n\t\twhen: \"Run command line with -f\"\n\t\tscheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen:\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Failed to convert checkInterval:\") }\n\n\t\tcleanup:\n\t\tproperties.getSubscription().setCheckInterval(\"3000\")\n\t}\n\n\t/**\n\t * Run application fails with invalid subscription allowable block timestamp diff sec value\n\t * Expected: Service logs \"Failed to parse allowable timestamp difference\"\n\t */\n\tdef \"Should handle invalid subscription allowable block timestamp diff sec value gracefully\"() {\n\t\tgiven: \"Invalid system property is set\"\n\t\tproperties.getSubscription().setAllowableBlockTimestampDiffSec(\"invalid\")\n\n\t\tand: \"Application is started via command line runner\"\n\t\tdef commandLineRunner = applicationContext.getBean(CommandLineRunner)\n\n\t\twhen: \"Run command line with -f\"\n\t\tscheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)\n\t\tcommandLineRunner.run(\"-f\")\n\n\t\tthen:\n\t\tdef messages = logAppender.list*.formattedMessage\n\t\tassert messages.any { it.contains(\"Failed to parse allowable timestamp difference\") }\n\n\t\tcleanup:\n\t\tproperties.getSubscription().setAllowableBlockTimestampDiffSec(\"2\")\n\t}\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/test/groovy/adhoc/config/ConfigurationServiceITSpec.groovy b/src/test/groovy/adhoc/config/ConfigurationServiceITSpec.groovy
--- a/src/test/groovy/adhoc/config/ConfigurationServiceITSpec.groovy	(revision 5c89afec48bdda2d3449fd0d782f07abeddb42e3)
+++ b/src/test/groovy/adhoc/config/ConfigurationServiceITSpec.groovy	(date 1750809555201)
@@ -160,54 +160,48 @@
 		then: "Application uses default values from application.properties"
 		properties.getEnv() == "local"
 
-		and: "Application should still start monitoring"
-		def messages = logAppender.list*.formattedMessage
-		assert messages.any { it.contains("Started bc monitoring") }
-	}
+        and: "Application should still start monitoring"
+        def messages = logAppender.list*.formattedMessage
+        assert messages.any { it.contains("Started bc monitoring") }
+    }
 
-	/**
-	 * Run application fails with invalid subscription check interval value
-	 * Expected: Service logs "Failed to convert checkInterval"
-	 */
-	def "Should handle invalid subscription check interval value gracefully"() {
-		given: "Invalid system property is set"
-		properties.getSubscription().setCheckInterval("invalid")
+    /**
+     * Run application fails with invalid subscription check interval value
+     * Expected: Service logs "Failed to convert checkInterval"
+     */
+    def "Should handle invalid subscription check interval value gracefully"() {
+        given: "Invalid system property is set"
+        properties.getSubscription().setCheckInterval("invalid")
 
-		and: "Application is started via command line runner"
-		def commandLineRunner = applicationContext.getBean(CommandLineRunner)
+        and: "Application is started via command line runner"
+        def commandLineRunner = applicationContext.getBean(CommandLineRunner)
 
-		when: "Run command line with -f"
-		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
-		commandLineRunner.run("-f")
+        when: "Run command line with -f"
+        scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
+        commandLineRunner.run("-f")
 
-		then:
-		def messages = logAppender.list*.formattedMessage
-		assert messages.any { it.contains("Failed to convert checkInterval:") }
-
-		cleanup:
-		properties.getSubscription().setCheckInterval("3000")
-	}
+        then:
+        def messages = logAppender.list*.formattedMessage
+        assert messages.any { it.contains("Failed to convert checkInterval:") }
+    }
 
-	/**
-	 * Run application fails with invalid subscription allowable block timestamp diff sec value
-	 * Expected: Service logs "Failed to parse allowable timestamp difference"
-	 */
-	def "Should handle invalid subscription allowable block timestamp diff sec value gracefully"() {
-		given: "Invalid system property is set"
-		properties.getSubscription().setAllowableBlockTimestampDiffSec("invalid")
+    /**
+     * Run application fails with invalid subscription allowable block timestamp diff sec value
+     * Expected: Service logs "Failed to parse allowable timestamp difference"
+     */
+    def "Should handle invalid subscription allowable block timestamp diff sec value gracefully"() {
+        given: "Invalid system property is set"
+        properties.getSubscription().setAllowableBlockTimestampDiffSec("invalid")
 
-		and: "Application is started via command line runner"
-		def commandLineRunner = applicationContext.getBean(CommandLineRunner)
+        and: "Application is started via command line runner"
+        def commandLineRunner = applicationContext.getBean(CommandLineRunner)
 
-		when: "Run command line with -f"
-		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
-		commandLineRunner.run("-f")
+        when: "Run command line with -f"
+        scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 5, TimeUnit.SECONDS)
+        commandLineRunner.run("-f")
 
-		then:
-		def messages = logAppender.list*.formattedMessage
-		assert messages.any { it.contains("Failed to parse allowable timestamp difference") }
-
-		cleanup:
-		properties.getSubscription().setAllowableBlockTimestampDiffSec("2")
-	}
+        then:
+        def messages = logAppender.list*.formattedMessage
+        assert messages.any { it.contains("Failed to parse allowable timestamp difference") }
+    }
 }
