envName: stage-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-fin-b-core
    tag: "aaaaaa"

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-b-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/stage-fin-b-tokyo-core-alb-tg/1690d84a4bbe18f5

configMap:
  DB_BASE: "stage-fin-b-tokyo-rds-cluster.cluster-c10ik2mskf8y.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_hbd6PwBiO/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_hbd6PwBiO"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: "ap-northeast-3"
  SECRETS_MANAGER_REPLICA_REGION: "ap-northeast-3"