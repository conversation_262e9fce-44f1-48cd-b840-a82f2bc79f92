envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-fin-b-bctracker-transaction
    tag: "676809c"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-tokyo-eks-pod-bctracker-transaction

configMap:
  DB_BASE: "prod-fin-b-tokyo-rds-cluster.cluster-ctsscu4sabwv.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  LOCAL_STACK_ENDPOINT: ""
  ZONE_TYPE: "financial_zone"