<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MonitorEventService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.application.event</a> &gt; <span class="el_source">MonitorEventService.java</span></div><h1>MonitorEventService.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.application.event;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.ContextConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParsedTraceId;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.StructuredLogContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.stereotype.Service;

@Service
public class MonitorEventService {
  private final LoggingService log;
  private final EventLogRepository eventLogRepository;
  private final EventRepository eventRepository;
  private final BlockHeightRepository blockHeightRepository;
  private final ObjectMapper objectMapper;
  private final BcmonitoringConfigurationProperties properties;
<span class="fc" id="L32">  private final AtomicBoolean running = new AtomicBoolean(true);</span>
  private final Web3jConfig web3jConfig;
  private final EthEventLogDao ethEventLogDao;

  public MonitorEventService(
      LoggingService logger,
      EventLogRepository eventLogRepository,
      EventRepository eventRepository,
      BlockHeightRepository blockHeightRepository,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      EthEventLogDao ethEventLogDao,
<span class="fc" id="L44">      ObjectMapper objectMapper) {</span>
<span class="fc" id="L45">    this.log = logger;</span>
<span class="fc" id="L46">    this.eventLogRepository = eventLogRepository;</span>
<span class="fc" id="L47">    this.eventRepository = eventRepository;</span>
<span class="fc" id="L48">    this.blockHeightRepository = blockHeightRepository;</span>
<span class="fc" id="L49">    this.properties = properties;</span>
<span class="fc" id="L50">    this.web3jConfig = web3jConfig;</span>
<span class="fc" id="L51">    this.objectMapper = objectMapper;</span>
<span class="fc" id="L52">    this.ethEventLogDao = ethEventLogDao;</span>
<span class="fc" id="L53">  }</span>

  /**
   * Execute the monitoring process This method will run in a loop, checking for new events and
   * processing them.
   *
   * @throws NumberFormatException if the check interval is not a valid integer
   * @throw Exception if there is an error during the monitoring process
   */
  public void execute() throws Exception {
    int checkInterval;
    try {
<span class="fc" id="L65">      checkInterval = Integer.parseInt(properties.getSubscription().getCheckInterval());</span>
<span class="fc" id="L66">    } catch (NumberFormatException e) {</span>
<span class="fc" id="L67">      log.error(&quot;Failed to convert checkInterval: {}&quot;, e.getMessage());</span>
<span class="fc" id="L68">      throw e;</span>
<span class="fc" id="L69">    }</span>
    try {
<span class="fc" id="L71">      monitorEvents();</span>
<span class="fc" id="L72">    } catch (Exception e) {</span>
<span class="fc" id="L73">      log.error(&quot;Error in monitoring loop: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L74">      sleep(checkInterval);</span>
<span class="fc" id="L75">      throw e;</span>
<span class="fc" id="L76">    }</span>
<span class="fc" id="L77">  }</span>

  /**
   * Monitor events from the blockchain and process them. This method will subscribe to new events
   * and process pending transactions.
   *
   * @throw Exception if there is an error during the monitoring process
   */
  private void monitorEvents() throws Exception {
    // Get current block height
    long blockNumber;
    try {
<span class="fc" id="L89">      blockNumber = blockHeightRepository.get();</span>
<span class="fc" id="L90">      log.info(&quot;Get blockheight: {}&quot;, blockNumber);</span>
<span class="fc" id="L91">    } catch (Exception e) {</span>
<span class="fc" id="L92">      log.error(&quot;Failed to get blockheight: {}&quot;, e.getMessage());</span>
<span class="fc" id="L93">      throw e;</span>
<span class="fc" id="L94">    }</span>

    try {
<span class="fc" id="L97">      BlockingQueue&lt;Transaction&gt; transactionsQueue = eventLogRepository.subscribe();</span>
<span class="fc" id="L98">      List&lt;Transaction&gt; pendingTransactionsQueue =</span>
<span class="fc" id="L99">          eventLogRepository.getFilterLogs(blockNumber + 1);</span>

<span class="fc" id="L101">      processPendingTransactions(pendingTransactionsQueue);</span>
<span class="fc" id="L102">      processNewTransactions(transactionsQueue);</span>
<span class="fc" id="L103">    } catch (Exception e) {</span>
<span class="fc" id="L104">      log.error(&quot;Error in monitoring: {}&quot;, e.getMessage());</span>
<span class="fc" id="L105">      ethEventLogDao.unsubscribe();</span>
<span class="fc" id="L106">      throw e;</span>
<span class="fc" id="L107">    }</span>
<span class="fc" id="L108">  }</span>

  /**
   * Process pending transactions from the queue. This method will save the block height and events
   * to the database.
   *
   * @param pendingQueue List of pending transactions
   */
  private void processPendingTransactions(List&lt;Transaction&gt; pendingQueue) throws Exception {
<span class="fc" id="L117">    BlockHeight exBlockHeight = BlockHeight.builder().id(1).blockNumber(0).build();</span>

<span class="fc bfc" id="L119" title="All 2 branches covered.">    for (Transaction tx : pendingQueue) {</span>
      try {
        // Process block height change
<span class="fc bfc" id="L122" title="All 4 branches covered.">        if (exBlockHeight.blockNumber != 0</span>
            &amp;&amp; exBlockHeight.blockNumber != tx.blockHeight.blockNumber) {
<span class="fc bfc" id="L124" title="All 2 branches covered.">          if (!savePendingTransactionBlockNumber(exBlockHeight)) {</span>
<span class="fc" id="L125">            throw new Exception(&quot;Failed to save block height&quot;);</span>
          }
        }

<span class="fc bfc" id="L129" title="All 2 branches covered.">        if (tx.blockHeight.blockNumber == 0) {</span>
<span class="fc" id="L130">          throw new RuntimeException(&quot;Block height Number is zero&quot;);</span>
<span class="fc bfc" id="L131" title="All 2 branches covered.">        } else if (!savePendingTransaction(tx)) {</span>
<span class="fc" id="L132">          throw new Exception(&quot;Failed to save transaction&quot;);</span>
        }

<span class="fc" id="L135">        exBlockHeight = tx.blockHeight;</span>
<span class="fc" id="L136">      } catch (Exception e) {</span>
<span class="fc" id="L137">        log.error(&quot;Error while processing pending transactions: {}&quot;, e.getMessage());</span>
<span class="fc" id="L138">        throw e;</span>
<span class="fc" id="L139">      }</span>
<span class="fc" id="L140">    }</span>

    // Save last block height
<span class="fc bfc" id="L143" title="All 2 branches covered.">    if (exBlockHeight.blockNumber != 0) {</span>
<span class="fc bfc" id="L144" title="All 2 branches covered.">      if (!savePendingTransactionBlockNumber(exBlockHeight)) {</span>
<span class="fc" id="L145">        throw new Exception(&quot;Failed to save block height&quot;);</span>
      }
    }

<span class="fc" id="L149">    log.info(&quot;Success to process pending transactions&quot;);</span>
<span class="fc" id="L150">  }</span>

  /**
   * Process new transactions from the queue. This method will save the events to the database.
   *
   * @param transactionsQueue BlockingQueue of new transactions
   */
  private void processNewTransactions(BlockingQueue&lt;Transaction&gt; transactionsQueue)
      throws Exception {
<span class="fc bfc" id="L159" title="All 2 branches covered.">    while (ContextConfig.isServiceRunning()) {</span>
      try {
<span class="fc" id="L161">        Transaction tx = transactionsQueue.poll(5, TimeUnit.SECONDS);</span>
<span class="fc bfc" id="L162" title="All 2 branches covered.">        if (Objects.isNull(tx)) continue;</span>
<span class="fc bfc" id="L163" title="All 2 branches covered.">        if (tx.blockHeight.blockNumber == -1) {</span>
<span class="fc" id="L164">          throw new Exception(&quot;Websocket is disconnected&quot;);</span>
        }
<span class="fc bfc" id="L166" title="All 2 branches covered.">        if (tx.blockHeight.blockNumber == 0) {</span>
<span class="fc" id="L167">          throw new Exception(&quot;Block height Number is zero&quot;);</span>
        }

<span class="fc bfc" id="L170" title="All 2 branches covered.">        if (!saveTransaction(tx)) {</span>
<span class="fc" id="L171">          throw new Exception(&quot;Failed to save transaction&quot;);</span>
        }

<span class="fc" id="L174">      } catch (Exception e) {</span>
<span class="fc" id="L175">        log.error(&quot;Error while processing new transactions: &quot;, e.getMessage());</span>
<span class="fc" id="L176">        throw e;</span>
<span class="fc" id="L177">      }</span>
<span class="fc" id="L178">      log.info(&quot;Success to process new transactions&quot;);</span>
    }
<span class="fc" id="L180">  }</span>

  /**
   * Save transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean saveTransaction(Transaction tx) {
    // Process all events in the transaction
<span class="fc bfc" id="L190" title="All 2 branches covered.">    for (Event e : tx.events) {</span>
<span class="fc bfc" id="L191" title="All 4 branches covered.">      if (e.transactionHash == null || e.transactionHash.isEmpty()) {</span>
<span class="fc" id="L192">        log.error(&quot;Event transaction hash is zero&quot;);</span>
<span class="fc" id="L193">        return false;</span>
      }

<span class="fc" id="L196">      String traceId = fetchTraceId(e.nonIndexedValues);</span>
<span class="fc" id="L197">      try (var logContext =</span>
<span class="fc" id="L198">          StructuredLogContext.forBlockchainEvent(</span>
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

<span class="fc bfc" id="L206" title="All 2 branches covered.">        if (!eventRepository.save(e)) {</span>
<span class="fc" id="L207">          log.error(&quot;Failure to register event&quot;);</span>
<span class="fc" id="L208">          return false;</span>
        }
<span class="fc" id="L210">        log.info(&quot;Success to register event&quot;);</span>
<span class="pc bpc" id="L211" title="1 of 2 branches missed.">      }</span>
<span class="fc" id="L212">    }</span>

<span class="fc bfc" id="L214" title="All 2 branches covered.">    if (!blockHeightRepository.save(tx.blockHeight)) {</span>
<span class="fc" id="L215">      log.error(&quot;Failure to register block number&quot;);</span>
<span class="fc" id="L216">      return false;</span>
    }
<span class="fc" id="L218">    log.info(&quot;Success to register block number&quot;);</span>
<span class="fc" id="L219">    return true;</span>
  }

  /**
   * Save pending transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean savePendingTransaction(Transaction tx) {
<span class="fc bfc" id="L229" title="All 2 branches covered.">    for (Event e : tx.events) {</span>
<span class="fc bfc" id="L230" title="All 4 branches covered.">      if (e.transactionHash == null || e.transactionHash.isEmpty()) {</span>
<span class="fc" id="L231">        log.error(&quot;Event transaction hash is zero&quot;);</span>
<span class="fc" id="L232">        return false;</span>
      }

<span class="fc" id="L235">      String traceId = fetchTraceId(e.nonIndexedValues);</span>
<span class="fc" id="L236">      try (var logContext =</span>
<span class="fc" id="L237">          StructuredLogContext.forBlockchainEvent(</span>
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

<span class="fc bfc" id="L245" title="All 2 branches covered.">        if (!eventRepository.save(e)) {</span>
<span class="fc" id="L246">          log.error(&quot;Failure to register event&quot;);</span>
<span class="fc" id="L247">          return false;</span>
        }
<span class="fc" id="L249">        log.info(&quot;Success to register event&quot;);</span>
<span class="pc bpc" id="L250" title="1 of 2 branches missed.">      }</span>
<span class="fc" id="L251">    }</span>
<span class="fc" id="L252">    return true;</span>
  }

  /**
   * Save pending transaction block number to the database.
   *
   * @param blockHeight BlockHeight object containing the block number
   * @return true if the block height was saved successfully, false otherwise
   */
  private boolean savePendingTransactionBlockNumber(BlockHeight blockHeight) {
<span class="fc bfc" id="L262" title="All 2 branches covered.">    if (!blockHeightRepository.save(blockHeight)) {</span>
<span class="fc" id="L263">      log.error(&quot;Failure to register block number: {}&quot;, blockHeight.blockNumber);</span>
<span class="fc" id="L264">      return false;</span>
    }
<span class="fc" id="L266">    log.info(&quot;Success to register block number: {}&quot;, blockHeight.blockNumber);</span>
<span class="fc" id="L267">    return true;</span>
  }

  /**
   * Fetch trace ID from non-indexed values.
   *
   * @param nonIndexedValues Non-indexed values as a JSON string
   * @return Trace ID as a string
   */
  private String fetchTraceId(String nonIndexedValues) {
    try {
<span class="fc" id="L278">      ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);</span>
<span class="fc bfc" id="L279" title="All 4 branches covered.">      if (parsed.getTraceId() == null || parsed.getTraceId().length == 0) {</span>
<span class="fc" id="L280">        return &quot;&quot;;</span>
      }

<span class="fc" id="L283">      StringBuilder sb = new StringBuilder();</span>
<span class="fc bfc" id="L284" title="All 2 branches covered.">      for (byte b : parsed.getTraceId()) {</span>
<span class="fc bfc" id="L285" title="All 2 branches covered.">        if (b != 0) {</span>
<span class="fc" id="L286">          sb.append((char) b);</span>
        }
      }
<span class="fc" id="L289">      return sb.toString();</span>
<span class="fc" id="L290">    } catch (JsonProcessingException e) {</span>
<span class="fc" id="L291">      log.error(&quot;Error parsing trace ID: {}&quot;, e.getMessage());</span>
<span class="fc" id="L292">      return &quot;&quot;;</span>
    }
  }

  /**
   * Sleep for a specified number of milliseconds.
   *
   * @param milliseconds Number of milliseconds to sleep
   */
  private void sleep(int milliseconds) {
    try {
<span class="fc" id="L303">      Thread.sleep(milliseconds);</span>
<span class="fc" id="L304">    } catch (InterruptedException e) {</span>
<span class="fc" id="L305">      Thread.currentThread().interrupt();</span>
<span class="fc" id="L306">    }</span>
<span class="fc" id="L307">  }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>