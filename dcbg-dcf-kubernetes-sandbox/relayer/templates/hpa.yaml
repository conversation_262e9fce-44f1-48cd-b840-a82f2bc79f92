{{ if eq .Values.isMultitenancy true }}
{{ range $configMapKey, $configMapValue := .Values.configMap }}
---
# マルチテナント
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: "relayer-{{ $configMapKey }}"
  namespace: dcbg-ibc
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: "relayer-{{ $configMapKey }}"
  minReplicas: {{ $.Values.minReplicas }}
  maxReplicas: {{ $.Values.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 50
{{- end }}
{{- else -}}
# シングルテナント
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: relayer
  namespace: dcbg-ibc
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: relayer
  minReplicas: {{ .Values.minReplicas }}
  maxReplicas: {{ .Values.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 50
{{- end -}}