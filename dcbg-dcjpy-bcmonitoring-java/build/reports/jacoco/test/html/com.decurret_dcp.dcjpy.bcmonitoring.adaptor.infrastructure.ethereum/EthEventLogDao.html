<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EthEventLogDao</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</a> &gt; <span class="el_class">EthEventLogDao</span></div><h1>EthEventLogDao</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">25 of 1,021</td><td class="ctr2">97%</td><td class="bar">2 of 88</td><td class="ctr2">97%</td><td class="ctr1">3</td><td class="ctr2">63</td><td class="ctr1">7</td><td class="ctr2">249</td><td class="ctr1">1</td><td class="ctr2">19</td></tr></tfoot><tbody><tr><td id="a14"><a href="EthEventLogDao.java.html#L97" class="el_method">lambda$subscribeAll$1(Web3j, int, BlockingQueue, NewHeadsNotification)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="107" alt="107"/></td><td class="ctr2" id="c17">84%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">80%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h0">6</td><td class="ctr2" id="i0">38</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a13"><a href="EthEventLogDao.java.html#L100" class="el_method">lambda$subscribeAll$0(NewHeadsNotification)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="EthEventLogDao.java.html#L200" class="el_method">convBlock2EventEntities(EthBlock.Block)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="152" alt="152"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">34</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="EthEventLogDao.java.html#L260" class="el_method">convertEthLogToEventEntity(Log)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="93" height="10" title="119" alt="119"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="EthEventLogDao.java.html#L326" class="el_method">decodeEventParameters(List, List)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="114" alt="114"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g0">14</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">20</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a9"><a href="EthEventLogDao.java.html#L479" class="el_method">getPendingTransactions(long, boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="107" alt="107"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i3">25</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="EthEventLogDao.java.html#L397" class="el_method">decodeTupleArray(List, List)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="89" alt="89"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="16" alt="16"/></td><td class="ctr2" id="e4">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i7">15</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="EthEventLogDao.java.html#L366" class="el_method">decodeTuple(List, List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="83" alt="83"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="16" alt="16"/></td><td class="ctr2" id="e5">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i8">13</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="EthEventLogDao.java.html#L514" class="el_method">lambda$getPendingTransactions$5(Map, EthLog.LogResult)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="65" alt="65"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i5">17</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a17"><a href="EthEventLogDao.java.html#L73" class="el_method">subscribeAll()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="53" alt="53"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i6">16</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a10"><a href="EthEventLogDao.java.html#L183" class="el_method">isDelayed(EthBlock.Block, int)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="20" alt="20"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">100%</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a15"><a href="EthEventLogDao.java.html#L159" class="el_method">lambda$subscribeAll$2(BlockingQueue, Throwable)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="20" alt="20"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="EthEventLogDao.java.html#L442" class="el_method">getBlockTimestamp(BigInteger)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i10">8</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="EthEventLogDao.java.html#L59" class="el_method">EthEventLogDao(LoggingService, BcmonitoringConfigurationProperties, Web3jConfig, AbiParser, ObjectMapper)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a2"><a href="EthEventLogDao.java.html#L429" class="el_method">decodeDynamicArray(Object)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a18"><a href="EthEventLogDao.java.html#L548" class="el_method">unsubscribe()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d8"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">100%</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a8"><a href="EthEventLogDao.java.html#L464" class="el_method">getPendingTransactions(long)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a16"><a href="EthEventLogDao.java.html#L167" class="el_method">lambda$subscribeAll$3()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a11"><a href="EthEventLogDao.java.html#L496" class="el_method">lambda$getPendingTransactions$4(EthLog.LogResult)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>