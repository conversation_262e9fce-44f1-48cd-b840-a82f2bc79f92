envName: stage-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-fin-b-bctracker-transaction
    tag: "aaaaaa"

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-b-tokyo-eks-pod-bctracker-transaction

configMap:
  DB_BASE: "stage-fin-b-tokyo-rds-cluster.cluster-c10ik2mskf8y.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  LOCAL_STACK_ENDPOINT: ""