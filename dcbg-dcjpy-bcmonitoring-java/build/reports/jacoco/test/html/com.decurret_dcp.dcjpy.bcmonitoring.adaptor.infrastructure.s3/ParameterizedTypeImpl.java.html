<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ParameterizedTypeImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; <span class="el_source">ParameterizedTypeImpl.java</span></div><h1>ParameterizedTypeImpl.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * A custom implementation of the {@link ParameterizedType} interface.
 *
 * &lt;p&gt;This class is used to represent parameterized types (e.g., {@code List&lt;String&gt;}) at runtime,
 * which is useful when working with Java's generic type system via reflection. It allows specifying
 * both the raw type (e.g., {@code List.class}) and the actual type arguments (e.g., {@code
 * String.class}).
 *
 * &lt;p&gt;&lt;strong&gt;Note:&lt;/strong&gt; This implementation always returns {@code null} for {@link
 * #getOwnerType()}, assuming that the parameterized type is not a member of another type.
 *
 * &lt;p&gt;Example usage:
 *
 * &lt;pre&gt;{@code
 * ParameterizedType type = new ParameterizedTypeImpl(List.class, String.class);
 * }&lt;/pre&gt;
 */
public class ParameterizedTypeImpl implements ParameterizedType {

  private final Type rawType;
  private final Type[] typeArguments;

  /**
   * Constructs a {@code ParameterizedTypeImpl} with the given raw type and type arguments.
   *
   * @param rawType the raw class type (e.g., {@code List.class})
   * @param typeArguments the actual type arguments (e.g., {@code String.class})
   */
<span class="fc" id="L34">  public ParameterizedTypeImpl(Type rawType, Type... typeArguments) {</span>
<span class="fc" id="L35">    this.rawType = rawType;</span>
<span class="fc" id="L36">    this.typeArguments = typeArguments;</span>
<span class="fc" id="L37">  }</span>

  /**
   * Returns the actual type arguments for this parameterized type.
   *
   * @return an array of {@link Type} representing the actual type arguments
   */
  @Override
  public Type[] getActualTypeArguments() {
<span class="fc" id="L46">    return typeArguments;</span>
  }

  /**
   * Returns the raw type of this parameterized type.
   *
   * @return the raw {@link Type} (e.g., {@code List.class})
   */
  @Override
  public Type getRawType() {
<span class="fc" id="L56">    return rawType;</span>
  }

  /**
   * Returns the owner type of this type, or {@code null} if it is a top-level type.
   *
   * @return always {@code null} in this implementation
   */
  @Override
  public Type getOwnerType() {
<span class="fc" id="L66">    return null;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>