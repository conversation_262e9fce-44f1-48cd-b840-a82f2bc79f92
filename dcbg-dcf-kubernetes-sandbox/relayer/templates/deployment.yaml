{{ if eq .Values.isMultitenancy true }}
{{ range $configMapKey, $configMapValue := .Values.configMap }}
---
# マルチテナント
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "relayer-{{ $configMapKey }}"
  namespace: dcbg-ibc
  labels:
    app: "relayer-{{ $configMapKey }}"
spec:
  selector:
    matchLabels:
      app: "relayer-{{ $configMapKey }}"
  template:
    metadata:
      name: "relayer_pod"
      labels:
        app: "relayer-{{ $configMapKey }}"
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap-relayer-envs.yaml") . | sha256sum }}
    spec:
      shareProcessNamespace: true
      serviceAccountName: {{ $.Values.serviceAccount.name }}
      containers:
        - name: "relayer-account-sync-{{ $configMapKey }}"
          args: ["account-sync"]
          image: {{ $.Values.application.image.name }}:{{ $.Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: "relayer-envs-{{ $configMapKey }}"
          resources:
            requests:
              cpu: {{ $.Values.application.resources.requests.cpu }}
              memory: {{ $.Values.application.resources.requests.memory }}
            limits:
              cpu: {{ $.Values.application.resources.limits.cpu }}
              memory: {{ $.Values.application.resources.limits.memory }}
        - name: "relayer-balance-sync-{{ $configMapKey }}"
          args: ["balance-sync"]
          image: {{ $.Values.application.image.name }}:{{ $.Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: "relayer-envs-{{ $configMapKey }}"
          resources:
            requests:
              cpu: {{ $.Values.application.resources.requests.cpu }}
              memory: {{ $.Values.application.resources.requests.memory }}
            limits:
              cpu: {{ $.Values.application.resources.limits.cpu }}
              memory: {{ $.Values.application.resources.limits.memory }}
        - name: "relayer-token-transfer-{{ $configMapKey }}"
          args: ["token-transfer"]
          image: {{ $.Values.application.image.name }}:{{ $.Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: "relayer-envs-{{ $configMapKey }}"
          resources:
            requests:
              cpu: {{ $.Values.application.resources.requests.cpu }}
              memory: {{ $.Values.application.resources.requests.memory }}
            limits:
              cpu: {{ $.Values.application.resources.limits.cpu }}
              memory: {{ $.Values.application.resources.limits.memory }}
        - name: "newrelic-infrastructure-{{ $configMapKey }}"
          image: {{ $.Values.newrelic.imageName }}:{{ $.Values.newrelic.imageTag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: NRIA_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  key: license-key
                  name: newrelic-license-key
            - name: NRIA_LOG_LEVEL
              value: "info"
            - name: NRIA_VERBOSE
              value: "0"
            - name: DISABLE_KUBE_STATE_METRICS
              value: "true"
            - name: CLUSTER_NAME
              value: {{ $.Values.envName }}-eks-cluster
            - name: COMPUTE_TYPE
              value: serverless
            - name: NRK8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_DISPLAY_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_CUSTOM_ATTRIBUTES
              value: '{"clusterName":"$(CLUSTER_NAME)", "Environment":"{{ $.Values.envName }}"}'
            - name: NRIA_PASSTHROUGH_ENVIRONMENT
              value: KUBERNETES_SERVICE_HOST,KUBERNETES_SERVICE_PORT,CLUSTER_NAME,CADVISOR_PORT,NRK8S_NODE_NAME,KUBE_STATE_METRICS_URL,KUBE_STATE_METRICS_POD_LABEL,TIMEOUT,ETCD_TLS_SECRET_NAME,ETCD_TLS_SECRET_NAMESPACE,API_SERVER_SECURE_PORT,KUBE_STATE_METRICS_SCHEME,KUBE_STATE_METRICS_PORT,SCHEDULER_ENDPOINT_URL,ETCD_ENDPOINT_URL,CONTROLLER_MANAGER_ENDPOINT_URL,API_SERVER_ENDPOINT_URL,DISABLE_KUBE_STATE_METRICS,DISCOVERY_CACHE_TTL
          resources:
            limits:
              cpu: {{ $.Values.newrelic.resources.limits.cpu }}
              memory: {{ $.Values.newrelic.resources.limits.memory }}
            requests:
              cpu: {{ $.Values.newrelic.resources.requests.cpu }}
              memory: {{ $.Values.newrelic.resources.requests.memory }}
{{- end }}
{{- else -}}
# シングルテナント
apiVersion: apps/v1
kind: Deployment
metadata:
  name: relayer
  namespace: dcbg-ibc
  labels:
    app: relayer
spec:
  selector:
    matchLabels:
      app: relayer
  template:
    metadata:
      name: "relayer_pod"
      labels:
        app: relayer
    spec:
      shareProcessNamespace: true
      serviceAccountName: {{ .Values.serviceAccount.name }}
      containers:
        - name: relayer-account-sync
          args: ["account-sync"]
          image: {{ .Values.application.image.name }}:{{ .Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: relayer-envs
          resources:
            requests:
              cpu: {{ .Values.application.resources.requests.cpu }}
              memory: {{ .Values.application.resources.requests.memory }}
            limits:
              cpu: {{ .Values.application.resources.limits.cpu }}
              memory: {{ .Values.application.resources.limits.memory }}
        - name: relayer-balance-sync
          args: ["balance-sync"]
          image: {{ .Values.application.image.name }}:{{ .Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: relayer-envs
          resources:
            requests:
              cpu: {{ .Values.application.resources.requests.cpu }}
              memory: {{ .Values.application.resources.requests.memory }}
            limits:
              cpu: {{ .Values.application.resources.limits.cpu }}
              memory: {{ .Values.application.resources.limits.memory }}
        - name: relayer-token-transfer
          args: ["token-transfer"]
          image: {{ .Values.application.image.name }}:{{ .Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: relayer-envs
          resources:
            requests:
              cpu: {{ .Values.application.resources.requests.cpu }}
              memory: {{ .Values.application.resources.requests.memory }}
            limits:
              cpu: {{ .Values.application.resources.limits.cpu }}
              memory: {{ .Values.application.resources.limits.memory }}
        - name: newrelic-infrastructure
          image: {{ .Values.newrelic.imageName }}:{{ .Values.newrelic.imageTag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: NRIA_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  key: license-key
                  name: newrelic-license-key
            - name: NRIA_LOG_LEVEL
              value: "info"
            - name: NRIA_VERBOSE
              value: "0"
            - name: DISABLE_KUBE_STATE_METRICS
              value: "true"
            - name: CLUSTER_NAME
              value: {{ .Values.envName }}-eks-cluster
            - name: COMPUTE_TYPE
              value: serverless
            - name: NRK8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_DISPLAY_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_CUSTOM_ATTRIBUTES
              value: '{"clusterName":"$(CLUSTER_NAME)", "Environment":"{{ .Values.envName }}"}'
            - name: NRIA_PASSTHROUGH_ENVIRONMENT
              value: KUBERNETES_SERVICE_HOST,KUBERNETES_SERVICE_PORT,CLUSTER_NAME,CADVISOR_PORT,NRK8S_NODE_NAME,KUBE_STATE_METRICS_URL,KUBE_STATE_METRICS_POD_LABEL,TIMEOUT,ETCD_TLS_SECRET_NAME,ETCD_TLS_SECRET_NAMESPACE,API_SERVER_SECURE_PORT,KUBE_STATE_METRICS_SCHEME,KUBE_STATE_METRICS_PORT,SCHEDULER_ENDPOINT_URL,ETCD_ENDPOINT_URL,CONTROLLER_MANAGER_ENDPOINT_URL,API_SERVER_ENDPOINT_URL,DISABLE_KUBE_STATE_METRICS,DISCOVERY_CACHE_TTL
          resources:
            limits:
              cpu: {{ .Values.newrelic.resources.limits.cpu }}
              memory: {{ .Values.newrelic.resources.limits.memory }}
            requests:
              cpu: {{ .Values.newrelic.resources.requests.cpu }}
              memory: {{ .Values.newrelic.resources.requests.memory }}
{{- end -}}