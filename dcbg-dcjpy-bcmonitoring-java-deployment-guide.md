# dcbg-dcjpy-bcmonitoring-java Kubernetesデプロイ手順書

## 概要

本手順書は、`dcbg-dcjpy-bcmonitoring-java`アプリケーションをKubernetes経由でデプロイするための包括的なガイドです。

## 前提条件

- AWS EKSクラスターが各環境で稼働していること
- Helmがインストールされていること
- kubectl が適切に設定されていること
- ECRへのアクセス権限があること

## デプロイ対象環境

- **dev-fin-tokyo**: 開発環境（FinZone）
- **dev-biz-tokyo**: 開発環境（BizZone）  
- **stage**: ステージング環境
- **prod**: 本番環境

## デプロイ手順

### 1. 事前準備（Cloud開発者担当）

#### 1.1 ECRリポジトリの作成
```bash
# 各環境用のECRリポジトリを作成
aws ecr create-repository --repository-name dev-fin-bcmonitoring-java --region ap-northeast-1
aws ecr create-repository --repository-name dev-biz-bcmonitoring-java --region ap-northeast-1
aws ecr create-repository --repository-name stage-bcmonitoring-java --region ap-northeast-1
aws ecr create-repository --repository-name prod-bcmonitoring-java --region ap-northeast-1
```

#### 1.2 IAMロールの作成
各環境用のServiceAccount用IAMロールを作成し、必要な権限を付与：
- S3バケットへの読み書き権限
- DynamoDBテーブルへのアクセス権限
- CloudWatchログ出力権限

#### 1.3 New Relicライセンスキーの設定
```bash
# 各環境のクラスターにSecretを作成
kubectl create secret generic newrelic-license-key \
  --from-literal=license-key=<YOUR_LICENSE_KEY> \
  --namespace=dcbg-core
```

### 2. アプリケーションの準備（BE開発者担当）

#### 2.1 CLIアプリケーション設定
```yaml
# application.yml
spring:
  main:
    web-application-type: none  # Webサーバーを無効化
```

#### 2.2 環境変数対応の設定
```yaml
# application.yml
server:
  port: ${SERVER_PORT:2345}

websocket:
  uri:
    host: ${WEBSOCKET_URI_HOST}
    port: ${WEBSOCKET_URI_PORT:8541}

aws:
  s3:
    bucket-name: ${S3_BUCKET_NAME}
```

#### 2.3 ログ設定の最適化
```yaml
# logback-spring.xml
logging:
  level:
    root: ${LOGGING_LEVEL_ROOT:INFO}
    com.decurret: ${LOGGING_LEVEL_COM_DECURRET:DEBUG}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 3. Dockerイメージのビルドとプッシュ

#### 3.1 アプリケーションのビルド
```bash
cd dcbg-dcjpy-bcmonitoring-java
./gradlew clean build
```

#### 3.2 Dockerイメージのビルド
```bash
docker build -t dcbg-dcjpy-bcmonitoring-java:latest .
```

#### 3.3 ECRへのプッシュ
```bash
# ECRログイン
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 342284557389.dkr.ecr.ap-northeast-1.amazonaws.com

# タグ付けとプッシュ
docker tag dcbg-dcjpy-bcmonitoring-java:latest 342284557389.dkr.ecr.ap-northeast-1.amazonaws.com/dev-fin-bcmonitoring-java:latest
docker push 342284557389.dkr.ecr.ap-northeast-1.amazonaws.com/dev-fin-bcmonitoring-java:latest
```

### 4. Helmデプロイメント

#### 4.1 dev環境へのデプロイ
```bash
cd dcbg-dcf-kubernetes-sandbox/bcmonitoring-java

# dev-fin環境
helm upgrade --install bcmonitoring-java-dev-fin . \
  -f dev/values-dev-fin-tokyo.yaml \
  --namespace dcbg-core

# dev-biz環境  
helm upgrade --install bcmonitoring-java-dev-biz . \
  -f dev/values-dev-biz-tokyo.yaml \
  --namespace dcbg-core
```

#### 4.2 stage環境へのデプロイ
```bash
helm upgrade --install bcmonitoring-java-stage . \
  -f stage/values-stage-fin-tokyo.yaml \
  --namespace dcbg-core
```

#### 4.3 prod環境へのデプロイ
```bash
helm upgrade --install bcmonitoring-java-prod . \
  -f prod/values-prod-fin-tokyo.yaml \
  --namespace dcbg-core
```

### 5. デプロイ後の確認

#### 5.1 Pod状態の確認
```bash
kubectl get pods -n dcbg-core -l app=bcmonitoring-java
kubectl describe pod <pod-name> -n dcbg-core
```

#### 5.2 ログの確認
```bash
kubectl logs -f <pod-name> -c bcmonitoring-java -n dcbg-core
```

#### 5.3 アプリケーション動作確認
```bash
# ログでアプリケーションの動作を確認
kubectl logs -f <pod-name> -c bcmonitoring-java -n dcbg-core | grep "Started bc monitoring"
```

## トラブルシューティング

### よくある問題と解決方法

1. **Pod起動失敗**
   - ConfigMapの設定値を確認
   - IAMロールの権限を確認
   - イメージのプル権限を確認

2. **アプリケーション動作確認**
   - ログで「Started bc monitoring」メッセージを確認
   - WebSocket接続エラーがないかログを確認

3. **リソース不足**
   - CPU/メモリのリクエスト・リミット値を調整
   - HPA設定を確認

## 運用監視

- New Relicによるアプリケーション監視
- CloudWatchによるログ監視
- Kubernetesメトリクス監視
- アラート設定による異常検知

## 関連ドキュメント

- [BCMonitoring JAVA 構成ドキュメント](https://decurret.atlassian.net/wiki/spaces/DIG/pages/3612312322/BCMonitoring+JAVA)
- [環境変数一覧](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2893185483/_24.07+_+BCMonitoring+BCMonitoring+Stream#BCMonitoring-Stream)
