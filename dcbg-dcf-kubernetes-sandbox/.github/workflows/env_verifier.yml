name: "env-verifier"
run-name: "[${{ github.event.inputs.environment }}][${{ github.event.inputs.component }}][${{ github.ref_name }}] Env Verifier"

on:
  workflow_dispatch:
    inputs:
      component:
        type: choice
        required: true
        options:
          - "bcclient"
          - "bcclient-stream"
          - "bcmonitoring"
          - "bctracker-balance"
          - "bctracker-transaction"
          - "core"
          - "relayer"
        description: "Component Name"
      environment:
        type: environment
        required: true
        description: "AWS Profile Name"
      check_type:
        type: choice
        required: true
        options:
          - "config_definition_check"
          - "resource_sync_check"
        description: "Check Type"

env:
  SCRIPT_BASE: "tools/env_verifier"

permissions:
  id-token: write
  contents: read
jobs:
  verify-env:
    runs-on: ubuntu-22.04
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: "Clean up"
        run: |
          if [ -e ${{ github.workspace }}/*.csv ]; then
            rm -f ${{ github.workspace }}/*.csv
          fi
          if [ -e ${{ github.workspace }}/*.out ]; then
            rm -f ${{ github.workspace }}/*.out
          fi

      - uses: actions/checkout@v4
      - name: "Set up Helm"
        id: setup-helm
        uses: azure/setup-helm@v4.2.0
        with:
          version: "latest"

      - name: "Configure Prod AWS Credentials [${{ github.event.inputs.environment }}]"
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1

      - name: "Checkout kubernetes-sandbox"
        id: checkout-kubernetes-sandbox
        uses: actions/checkout@v4
        with:
          repository: decurret-lab/dcbg-dcf-kubernetes-sandbox
          ref: ${{ github.ref_name }}
          path: ./dcbg-dcf-kubernetes-sandbox

      - name: "Checkout dcbg-dcf-terraform-core-template (Only scripts)"
        id: checkout-terraform-core-template
        uses: actions/checkout@v4
        with:
          repository: decurret-lab/dcbg-dcf-terraform-core-template
          ref: "develop"
          path: ./dcbg-dcf-terraform-core-template
          sparse-checkout: scripts
          sparse-checkout-cone-mode: true
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}

      - name: "Run Get AWS resource data script"
        id: get-aws-core-resources
        working-directory: "./dcbg-dcf-terraform-core-template/scripts"
        run: |
          env_prefix=$(echo ${{ github.event.inputs.environment }} | sed -E 's/(^[a-z0-9]+)-.*$/\1/')
          dir="${{ github.workspace }}/dcbg-dcf-kubernetes-sandbox/${{ github.event.inputs.component }}/$env_prefix"          
          files=($(ls "$dir"))
          for file in "${files[@]}"; do
            if [[ "$file" != *"${env}"* ]] || [[ "$file" != *"${{ github.event.inputs.environment }}"* ]]; then
              continue
            fi
            if [[ "$file" == *"tokyo"* ]]; then
              export AWS_DEFAULT_REGION=ap-northeast-1
            elif [[ "$file" == *"osaka"* ]]; then
              export AWS_DEFAULT_REGION=ap-northeast-3
            fi
            pushd ${{ github.workspace }}/dcbg-dcf-kubernetes-sandbox
            helm template --values "${{ github.event.inputs.component }}/$env_prefix/$file" "./${{ github.event.inputs.component }}" > ${{ github.workspace }}/"$file".output
            popd
            if [[ ${{ github.event.inputs.check_type }} == 'config_definition_check' ]]; then
              file_name=$file
            else
              file_name=${{ github.event.inputs.environment }}
            fi
            if [[ "${{ github.event.inputs.environment }}" != *"ibc"* ]]; then
              ./list_main_env_resources_info.sh &> ${{ github.workspace }}/"$file_name".csv
            else
              ./list_ibc_env_resources_info.sh &> ${{ github.workspace }}/"$file_name".csv
            fi
            if [ -f ${{ github.workspace }}/"$file_name".output ]; then
              cat ${{ github.workspace }}/"$file_name".output
            else
              echo "$file_name.output not found."
            fi
            if [ -f ${{ github.workspace }}/"$file_name".csv ]; then
              cat ${{ github.workspace }}/"$file_name".csv
            else
              echo "$file_name.csv not found."
            fi
          done

      - name: "Set up Python"
        id: setup-python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      - name: "Install python dependencies"
        id: install-python-dependencies
        run: |
          python -m pip install --upgrade pip --root-user-action=ignore
          pip install -r tools/env_verifier/requirements.txt

      - name: "Run Verify Script(Config Definition Check)"
        id: run-script-config-definition-check
        if: ${{ github.event.inputs.check_type == 'config_definition_check' }}
        run: |
          env_prefix=$(echo ${{ github.event.inputs.environment }} | sed -E 's/(^[a-z0-9]+)-.*$/\1/')
          dir="${{ github.workspace }}/dcbg-dcf-kubernetes-sandbox/${{ github.event.inputs.component }}/$env_prefix"
          files=($(ls "$dir"))
          for file in "${files[@]}"; do
            if [[ "$file" != *"${{ github.event.inputs.environment }}"* ]]; then
              continue
            fi
            python tools/env_verifier/app/env_verifier.py -c ${{ github.event.inputs.component }} -yaml ${{ github.workspace }}/"$file".output -doc ${{ github.workspace }}/"$file".csv          
          done

      - name: "Set up kubectl"
        if: ${{ github.event.inputs.check_type == 'resource_sync_check' }}
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.29.9'

      - name: "Run Verify Script(Resource Sync Check)"
        id: run-script-resource-sync-check
        if: ${{ github.event.inputs.check_type == 'resource_sync_check' }}
        run: |
          environment="${{ github.event.inputs.environment }}"
          if [[ "$environment" != *"sandbox"* ]]; then
            EKS_CLUSTER_NAME=$environment-tokyo-eks-cluster
          else
            EKS_CLUSTER_NAME="${environment//sandbox/sbx}-tokyo-eks-cluster"
          fi
          aws eks update-kubeconfig --name $EKS_CLUSTER_NAME --region ap-northeast-1
          if [[ "$environment" != *"ibc"* ]]; then
            namespace=dcbg-core
          else
            namespace=dcbg-ibc
          fi
          component=${{ github.event.inputs.component }}
          pods=$(kubectl get pod -n $namespace -o custom-columns=NAME:.metadata.name | grep -E "^${component}-[a-zA-Z0-9]+-[a-zA-Z0-9]+")
          for pod in $pods; do
            containers=$(kubectl get pod $pod -n $namespace -o jsonpath='{.spec.containers[*].name}' | tr ' ' '\n' | grep -E "^$component")
            for container in $containers; do
              echo "--------------------------------------------------------------------"
              env=$(kubectl exec -i $pod -n $namespace --container $container -- /bin/sh -c "env" | sort)
              echo $env > ${{ github.workspace }}/"$pod".output
              echo -e "Target Pod       \t: "$pod
              echo -e "Target Container \t: "$container
              python tools/env_verifier/app/env_verifier.py \
                -c $component \
                -doc ${{ github.workspace }}/$environment.csv \
                -env ${{ github.workspace }}/"$pod".output
            done
          done
