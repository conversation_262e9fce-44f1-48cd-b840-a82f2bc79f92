{"address": "0x17A20DB36EA698A0492e5F95bF2CB814D2E36a22", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "backupAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupBusinessZoneAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "bizAccountsAll", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupFinancialZoneAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "financialZoneAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "dischargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple", "name": "cumulativeTransactionLimits", "components": [{"type": "uint256", "name": "cumulativeMintLimit"}, {"type": "uint256", "name": "cumulativeMintAmount"}, {"type": "uint256", "name": "cumulativeBurnLimit"}, {"type": "uint256", "name": "cumulativeBurnAmount"}, {"type": "uint256", "name": "cumulativeChargeLimit"}, {"type": "uint256", "name": "cumulativeChargeAmount"}, {"type": "uint256", "name": "cumulativeDischargeLimit"}, {"type": "uint256", "name": "cumulativeDischargeAmount"}, {"type": "uint256", "name": "cumulativeTransferLimit"}, {"type": "uint256", "name": "cumulativeTransferAmount"}]}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupProviders", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "providers", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32[]", "name": "availableIssuerIds"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "newField1"}, {"type": "bool", "name": "newField2"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupValidators", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "validators", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xa03539853eaba3050a790b4302e8972d69cc8d6e5d9571f586c2c17c0fbedfa1", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0x17A20DB36EA698A0492e5F95bF2CB814D2E36a22", "transactionIndex": 0, "gasUsed": "4782966", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x6ec8a3fa86203ff526d6d835a4fb0f41f87d6252b058832ddcfcb8c3cfb3c764", "blockNumber": 249, "cumulativeGasUsed": "4782966", "status": 1}}