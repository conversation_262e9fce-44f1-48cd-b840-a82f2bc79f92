{{ if eq .Values.isMultitenancy true }}
{{ range $configMapKey, $configMapValue := .Values.configMap }}
---
# マルチテナント
apiVersion: v1
kind: ConfigMap
metadata:
  name: "relayer-envs-{{ $configMapKey }}"
  namespace: dcbg-ibc
data:
{{- range $key, $val := $configMapValue }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
{{- end }}
{{- else -}}
# シングルテナント
apiVersion: v1
kind: ConfigMap
metadata:
  name: relayer-envs
  namespace: dcbg-ibc
data:
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
{{- end -}}



