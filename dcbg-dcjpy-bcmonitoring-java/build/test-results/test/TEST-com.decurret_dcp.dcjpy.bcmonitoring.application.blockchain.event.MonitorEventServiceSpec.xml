<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" tests="67" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:39.198Z" hostname="tungvt.local" time="0.856">
  <properties/>
  <testcase name="execute should process one iteration and terminate when running is set to false" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.011"/>
  <testcase name="execute should catch NumberFormatException and log error when checkInterval is invalid" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="execute should handle exceptions in monitoring loop" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.104"/>
  <testcase name="execute should catch Exception in monitoring loop, log error, and continue execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.107"/>
  <testcase name="monitorEvents should process block height and transactions" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.008"/>
  <testcase name="monitorEvents should handle exceptions" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.106"/>
  <testcase name="monitorEvents should catch Exception, log error, and continue execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.315"/>
  <testcase name="monitorEvents should process new transactions when finalBlockHeight is valid" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.109"/>
  <testcase name="processPendingTransactions should process transactions and update block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.008"/>
  <testcase name="processPendingTransactions should handle empty queue" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.004"/>
  <testcase name="processPendingTransactions should handle block height change" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.01"/>
  <testcase name="processPendingTransactions should handle zero block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processPendingTransactions should handle save failures" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="processPendingTransactions should handle interrupted exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processPendingTransactions should handle block height save failure on block change" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processPendingTransactions should save block height one time when consecutive transactions have same block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="processPendingTransactions should handle last block height save failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="saveTransaction should handle empty transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle null transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should save events and block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle event save failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle block height save failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="fetchTraceId should handle valid JSON" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="fetchTraceId should handle empty traceId" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="fetchTraceId should handle null traceId" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="fetchTraceId should handle JSON parsing exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="fetchTraceId should skip zero bytes when building trace ID string" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="fetchTraceId should handle JSON with unknown properties" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should process transactions successfully" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should process transactions is empty" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="processNewTransactions should exit when block height is zero" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should exit when saveTransaction returns false" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should handle InterruptedException" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should exit loop when running is false" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should exit early when saveTransaction returns false due to block height save failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="savePendingTransaction should handle empty transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="savePendingTransaction should handle null transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="savePendingTransactionBlockNumber should handle block height save failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="monitorEvents should log error when exception occurs in monitoring process" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should log error when exception escapes from executor task" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should handle exceptions in processNewTransactions" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="monitorEvents should log error when exception occurs while getting filter logs" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should not call processNewTransactions when processPendingTransactions returns null" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should run without errors when all operations succeed" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="savePendingTransaction should return false when eventRepository.save fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="processNewTransactions should handle null transaction from queue" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processNewTransactions should handle websocket disconnection (block number -1)" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.0"/>
  <testcase name="processNewTransactions should handle saveTransaction failure" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="sleep should handle InterruptedException" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle StructuredLogContext close exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.003"/>
  <testcase name="savePendingTransaction should handle StructuredLogContext close exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="processNewTransactions should continue when saveTransaction succeeds" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should complete successfully with all operations" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="savePendingTransaction should complete successfully with all operations" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle all edge cases in try-with-resources" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="savePendingTransaction should handle all edge cases in try-with-resources" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="monitorEvents should handle exception when getting block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle multiple events in transaction" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <testcase name="savePendingTransaction should handle multiple events in transaction" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should fail on first event when multiple events exist" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="savePendingTransaction should fail on second event when multiple events exist" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="processPendingTransactions should handle first transaction with non-zero block height" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.003"/>
  <testcase name="saveTransaction should handle transaction with empty events list" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="savePendingTransaction should handle transaction with empty events list" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="saveTransaction should handle exception in try-with-resources close" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.001"/>
  <testcase name="savePendingTransaction should handle exception in try-with-resources close" classname="com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[Exception in thread "Thread-6" java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because "pendingQueue" is null
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.processPendingTransactions(MonitorEventService.java:119)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.monitorEvents(MonitorEventService.java:101)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.execute(MonitorEventService.java:71)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec$__spock_feature_0_6_closure5.doCall(MonitorEventServiceSpec.groovy:205)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec$__spock_feature_0_6_closure5.doCall(MonitorEventServiceSpec.groovy)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:280)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovy.lang.Closure.call(Closure.java:412)
	at groovy.lang.Closure.run(Closure.java:505)
	at java.base/java.lang.Thread.run(Thread.java:1583)
]]></system-err>
</testsuite>
