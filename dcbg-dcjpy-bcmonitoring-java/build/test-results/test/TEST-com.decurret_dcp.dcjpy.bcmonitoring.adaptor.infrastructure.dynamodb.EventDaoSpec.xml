<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:29.384Z" hostname="tungvt.local" time="0.065">
  <properties/>
  <testcase name="should successfully save an event" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" time="0.02"/>
  <testcase name="should handle exception when saving event fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" time="0.01"/>
  <testcase name="should return true when handling null event" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" time="0.001"/>
  <testcase name="should attempt to save event even with empty attribute map" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" time="0.029"/>
  <testcase name="should handle generic Exception during save operation" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDaoSpec" time="0.004"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
