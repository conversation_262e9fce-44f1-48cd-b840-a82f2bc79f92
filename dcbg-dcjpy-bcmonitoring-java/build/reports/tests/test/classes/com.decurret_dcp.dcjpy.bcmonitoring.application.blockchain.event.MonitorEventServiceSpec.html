<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - MonitorEventServiceSpec</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>MonitorEventServiceSpec</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.html">com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event</a> &gt; MonitorEventServiceSpec</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">67</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.849s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard error</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">execute should catch Exception in monitoring loop, log error, and continue execution</td>
<td class="success">0.107s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">execute should catch NumberFormatException and log error when checkInterval is invalid</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">execute should handle exceptions in monitoring loop</td>
<td class="success">0.104s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">execute should process one iteration and terminate when running is set to false</td>
<td class="success">0.011s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should handle JSON parsing exception</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should handle JSON with unknown properties</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should handle empty traceId</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should handle null traceId</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should handle valid JSON</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fetchTraceId should skip zero bytes when building trace ID string</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should catch Exception, log error, and continue execution</td>
<td class="success">0.315s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should handle exception when getting block height</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should handle exceptions</td>
<td class="success">0.106s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should handle exceptions in processNewTransactions</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should log error when exception escapes from executor task</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should log error when exception occurs in monitoring process</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should log error when exception occurs while getting filter logs</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should not call processNewTransactions when processPendingTransactions returns null</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should process block height and transactions</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should process new transactions when finalBlockHeight is valid</td>
<td class="success">0.109s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">monitorEvents should run without errors when all operations succeed</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should continue when saveTransaction succeeds</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should exit early when saveTransaction returns false due to block height save failure</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should exit loop when running is false</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should exit when block height is zero</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should exit when saveTransaction returns false</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should handle InterruptedException</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should handle null transaction from queue</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should handle saveTransaction failure</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should handle websocket disconnection (block number -1)</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should process transactions is empty</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processNewTransactions should process transactions successfully</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle block height change</td>
<td class="success">0.010s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle block height save failure on block change</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle empty queue</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle first transaction with non-zero block height</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle interrupted exception</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle last block height save failure</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle save failures</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should handle zero block height</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should process transactions and update block height</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">processPendingTransactions should save block height one time when consecutive transactions have same block height</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should complete successfully with all operations</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should fail on second event when multiple events exist</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle StructuredLogContext close exception</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle all edge cases in try-with-resources</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle empty transaction hash</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle exception in try-with-resources close</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle multiple events in transaction</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle null transaction hash</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should handle transaction with empty events list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransaction should return false when eventRepository.save fails</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">savePendingTransactionBlockNumber should handle block height save failure</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should complete successfully with all operations</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should fail on first event when multiple events exist</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle StructuredLogContext close exception</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle all edge cases in try-with-resources</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle block height save failure</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle empty transaction hash</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle event save failure</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle exception in try-with-resources close</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle multiple events in transaction</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle null transaction hash</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should handle transaction with empty events list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">saveTransaction should save events and block height</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">sleep should handle InterruptedException</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard error</h2>
<span class="code">
<pre>Exception in thread &quot;Thread-6&quot; java.lang.NullPointerException: Cannot invoke &quot;java.util.List.iterator()&quot; because &quot;pendingQueue&quot; is null
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.processPendingTransactions(MonitorEventService.java:119)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.monitorEvents(MonitorEventService.java:101)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService.execute(MonitorEventService.java:71)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec$__spock_feature_0_6_closure5.doCall(MonitorEventServiceSpec.groovy:205)
	at com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.event.MonitorEventServiceSpec$__spock_feature_0_6_closure5.doCall(MonitorEventServiceSpec.groovy)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:280)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovy.lang.Closure.call(Closure.java:412)
	at groovy.lang.Closure.run(Closure.java:505)
	at java.base/java.lang.Thread.run(Thread.java:1583)
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
