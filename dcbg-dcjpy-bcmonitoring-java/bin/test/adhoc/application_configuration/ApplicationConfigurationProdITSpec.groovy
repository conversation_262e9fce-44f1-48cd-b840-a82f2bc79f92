package adhoc.application_configuration

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("prod")
class ApplicationConfigurationProdITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successfully initializes application with env == 'prod'
	 * Verifies that the ENV variable value is 'prod'
	 * Expected: Property values are loaded from application-prod.properties
	 */
	def "Should load all configuration properties correctly when env is 'prod'"() {
		when: "initialize application"

		then: "env should be 'prod'"
		properties.getEnv() == "prod"
		properties.getAws().getAccessKeyId() == "prod-access-key"
		properties.getAws().getSecretAccessKey() == "prod-secret-key"
	}
}
