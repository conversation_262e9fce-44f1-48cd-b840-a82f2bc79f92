envName: prod-biz-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-biz-a-core
    tag: "774319d"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-biz-a-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/prod-biz-a-tokyo-core-alb-tg/6e007cd5eadbc2a6

configMap:
  DB_BASE: "prod-biz-a-tokyo-rds-cluster.cluster-cy4qhuaqvu6v.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_UxBqhHktS/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_UxBqhHktS"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "business_zone"
