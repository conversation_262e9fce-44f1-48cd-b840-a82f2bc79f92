<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AbiParser.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; <span class="el_source">AbiParser.java</span></div><h1>AbiParser.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.protocol.core.methods.response.AbiDefinition;
import org.web3j.protocol.core.methods.response.Log;

@Component
public class AbiParser {
<span class="fc" id="L23">  private static final Logger log = LoggerFactory.getLogger(AbiParser.class);</span>
<span class="fc" id="L24">  private final ObjectMapper mapper = new ObjectMapper();</span>
<span class="fc" id="L25">  public static Map&lt;String, ContractEvents&gt; contractEventStore = new HashMap&lt;&gt;();</span>
<span class="fc" id="L26">  public static List&lt;String&gt; contractAddresses = new ArrayList&lt;&gt;();</span>

  private final BcmonitoringConfigurationProperties properties;

  /** ABI event input parameter that stores name, type reference and indexing information */
  public static class AbiEventInput {
    private final String name;
    private final String type;
    private final boolean indexed;
    private final List&lt;AbiEventInput&gt; components;

<span class="fc" id="L37">    public AbiEventInput(String name, String type, boolean indexed) {</span>
<span class="fc" id="L38">      this.name = name;</span>
<span class="fc" id="L39">      this.type = type;</span>
<span class="fc" id="L40">      this.indexed = indexed;</span>
<span class="fc" id="L41">      this.components = null;</span>
<span class="fc" id="L42">    }</span>

    public AbiEventInput(
<span class="fc" id="L45">        String name, String type, boolean indexed, List&lt;AbiEventInput&gt; components) {</span>
<span class="fc" id="L46">      this.name = name;</span>
<span class="fc" id="L47">      this.type = type;</span>
<span class="fc" id="L48">      this.indexed = indexed;</span>
<span class="fc bfc" id="L49" title="All 2 branches covered.">      this.components = components != null ? List.copyOf(components) : null;</span>
<span class="fc" id="L50">    }</span>

    public String getName() {
<span class="fc" id="L53">      return name;</span>
    }

    public String getType() {
<span class="fc" id="L57">      return type;</span>
    }

    public boolean isIndexed() {
<span class="fc" id="L61">      return indexed;</span>
    }

    public List&lt;AbiEventInput&gt; getComponents() {
<span class="fc" id="L65">      return components;</span>
    }

    public boolean isTuple() {
<span class="fc bfc" id="L69" title="All 4 branches covered.">      return type != null &amp;&amp; type.startsWith(&quot;tuple&quot;);</span>
    }
  }

  /** Contract ABI event definition with parameter metadata */
  public static class ContractAbiEvent {
    private final Event event;
    private final List&lt;AbiEventInput&gt; inputs;

<span class="fc" id="L78">    public ContractAbiEvent(Event event, List&lt;AbiEventInput&gt; inputs) {</span>
<span class="fc" id="L79">      this.event = event;</span>
<span class="fc" id="L80">      this.inputs = List.copyOf(inputs);</span>
<span class="fc" id="L81">    }</span>

    public Event getEvent() {
<span class="fc" id="L84">      return event;</span>
    }

    public List&lt;AbiEventInput&gt; getInputs() {
<span class="fc" id="L88">      return inputs;</span>
    }
  }

<span class="fc" id="L92">  public AbiParser(BcmonitoringConfigurationProperties properties) {</span>
<span class="fc" id="L93">    this.properties = properties;</span>
<span class="fc" id="L94">  }</span>

  /**
   * Parse ABI content from JSON string
   *
   * @param abiContent ABI content in JSON format
   * @throws IOException If parsing fails
   */
  public Map&lt;String, ContractAbiEvent&gt; parseAbi(String abiContent) throws IOException {
<span class="fc" id="L103">    Map&lt;String, ContractAbiEvent&gt; stringEventMap = new HashMap&lt;&gt;();</span>
<span class="fc bfc" id="L104" title="All 4 branches covered.">    if (abiContent == null || abiContent.isEmpty()) {</span>
<span class="fc" id="L105">      log.warn(&quot;Empty ABI content provided&quot;);</span>
<span class="fc" id="L106">      return stringEventMap;</span>
    }

    try {
      // Parse ABI content as array of AbiDefinition objects
<span class="fc" id="L111">      AbiDefinition[] abiDefinitions = mapper.readValue(abiContent, AbiDefinition[].class);</span>

<span class="fc bfc" id="L113" title="All 2 branches covered.">      for (AbiDefinition abiDefinition : abiDefinitions) {</span>
<span class="fc bfc" id="L114" title="All 2 branches covered.">        if (&quot;event&quot;.equals(abiDefinition.getType())) {</span>
<span class="fc" id="L115">          String eventName = abiDefinition.getName();</span>
<span class="fc bfc" id="L116" title="All 4 branches covered.">          if (eventName == null || eventName.isEmpty()) {</span>
            // Skip events with missing or empty names
<span class="fc" id="L118">            continue;</span>
          }

<span class="fc" id="L121">          List&lt;TypeReference&lt;?&gt;&gt; typeReferences = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L122">          List&lt;AbiEventInput&gt; eventInputs = new ArrayList&lt;&gt;();</span>

          // Parse inputs from AbiDefinition
<span class="fc" id="L125">          List&lt;AbiDefinition.NamedType&gt; inputs = abiDefinition.getInputs();</span>
<span class="fc bfc" id="L126" title="All 2 branches covered.">          if (inputs != null) {</span>
<span class="fc bfc" id="L127" title="All 2 branches covered.">            for (int i = 0; i &lt; inputs.size(); i++) {</span>
<span class="fc" id="L128">              TypeReference&lt;?&gt; typeReference = createTypeReference(inputs.get(i));</span>
<span class="fc" id="L129">              typeReferences.add(typeReference);</span>

              // Create AbiEventInput with tuple component processing
<span class="fc" id="L132">              AbiEventInput eventInput = createAbiEventInput(inputs.get(i), i);</span>
<span class="fc" id="L133">              eventInputs.add(eventInput);</span>
            }

            // Create event (Web3j expects 2 arguments)
<span class="fc" id="L137">            Event event = new Event(eventName, typeReferences);</span>
<span class="fc" id="L138">            ContractAbiEvent contractAbiEvent = new ContractAbiEvent(event, eventInputs);</span>

            // Create signature for the event
<span class="fc" id="L141">            String signature = EventEncoder.encode(event);</span>
<span class="fc" id="L142">            log.debug(</span>
                &quot;Event: {}, Parameters: {}, Signature: {}&quot;, eventName, eventInputs, signature);

            // Store contract ABI event in contractEventStore
<span class="fc" id="L146">            stringEventMap.put(signature, contractAbiEvent);</span>

<span class="fc" id="L148">            log.debug(&quot;Parsed event: {} with signature: {}&quot;, eventName, signature);</span>
          }
        }
      }

<span class="fc" id="L153">      log.info(&quot;Successfully parsed ABI with {} events&quot;, stringEventMap.size());</span>
<span class="fc" id="L154">    } catch (Exception e) {</span>
<span class="fc" id="L155">      log.error(&quot;Failed to parse ABI content&quot;, e);</span>
<span class="fc" id="L156">      throw new IOException(&quot;ABI parsing failed: &quot; + e.getMessage(), e);</span>
<span class="fc" id="L157">    }</span>
<span class="fc" id="L158">    return stringEventMap;</span>
  }

  /** Create appropriate TypeReference based on the Solidity type */
  private TypeReference&lt;?&gt; createTypeReference(AbiDefinition.NamedType input) {
<span class="fc" id="L163">    boolean indexed = input.isIndexed();</span>
<span class="fc" id="L164">    String solidityType = input.getType();</span>

<span class="fc" id="L166">    return AbiTypeConverter.convertType(solidityType, indexed, input.getComponents());</span>
  }

  /** Create AbiEventInput with recursive tuple component processing */
  private AbiEventInput createAbiEventInput(AbiDefinition.NamedType input, int indexArg) {
<span class="fc bfc" id="L171" title="All 2 branches covered.">    String name = &quot;&quot;.equals(input.getName()) ? &quot;arg&quot; + indexArg : input.getName();</span>
<span class="fc" id="L172">    String type = input.getType();</span>
<span class="fc" id="L173">    boolean indexed = input.isIndexed();</span>

    // Handle tuple types by recursively processing components
<span class="fc bfc" id="L176" title="All 2 branches covered.">    if (type.startsWith(&quot;tuple&quot;)) {</span>
<span class="fc" id="L177">      List&lt;AbiEventInput&gt; components = extractTupleComponents(input);</span>
<span class="fc" id="L178">      return new AbiEventInput(name, type, indexed, components);</span>
    } else {
      // Non-tuple types
<span class="fc" id="L181">      return new AbiEventInput(name, type, indexed);</span>
    }
  }

  /**
   * Extract tuple components recursively, similar to initInputParameter reference implementation
   */
  private List&lt;AbiEventInput&gt; extractTupleComponents(AbiDefinition.NamedType input) {
<span class="fc" id="L189">    List&lt;AbiDefinition.NamedType&gt; components = input.getComponents();</span>

<span class="fc bfc" id="L191" title="All 2 branches covered.">    if (components.isEmpty()) {</span>
<span class="fc" id="L192">      log.debug(&quot;Tuple type {} has no components&quot;, input.getType());</span>
<span class="fc" id="L193">      return Collections.emptyList();</span>
    }

    // Recursively process each component (following initInputParameter pattern)
<span class="fc" id="L197">    List&lt;AbiEventInput&gt; componentInputs = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L198" title="All 2 branches covered.">    for (int i = 0; i &lt; components.size(); i++) {</span>
<span class="fc" id="L199">      AbiEventInput componentInput = createAbiEventInput(components.get(i), i);</span>
<span class="fc" id="L200">      componentInputs.add(componentInput);</span>
    }

<span class="fc" id="L203">    log.debug(</span>
<span class="fc" id="L204">        &quot;Extracted {} components from tuple type {}&quot;, componentInputs.size(), input.getType());</span>
<span class="fc" id="L205">    return componentInputs;</span>
  }

  /**
   * Parse ABI content from an input stream and register contracts/events
   *
   * @param inputStream The input stream containing ABI JSON
   * @param objectKey The S3 object key
   * @param lastModified Last modified timestamp
   * @return Contract information including address and name
   * @throws IOException If parsing fails
   */
  public ContractInfo parseAbiContent(InputStream inputStream, String objectKey, Date lastModified)
      throws IOException {
    try {
<span class="fc" id="L220">      byte[] abiJson = inputStream.readAllBytes();</span>

      // Extract contract name from object key
<span class="fc" id="L223">      String[] pathParts = objectKey.split(&quot;/&quot;);</span>
<span class="fc" id="L224">      String contractName = pathParts[1].replace(&quot;.json&quot;, &quot;&quot;);</span>

      // Extract address from JSON based on ABI format
<span class="fc" id="L227">      String abiFormat = properties.getAbiFormat();</span>
      String address;

<span class="fc" id="L230">      JsonNode rootNode = mapper.readTree(abiJson);</span>
<span class="fc bfc" id="L231" title="All 2 branches covered.">      if (&quot;truffle&quot;.equals(abiFormat)) {</span>
        // For Truffle format, find address in networks section
<span class="fc" id="L233">        JsonNode networksNode = rootNode.path(&quot;networks&quot;);</span>
<span class="fc" id="L234">        address = findFirstAddressInNetworks(networksNode);</span>
<span class="fc" id="L235">      } else {</span>
        // For other formats (like Hardhat), get address directly
<span class="fc" id="L237">        address = rootNode.path(&quot;address&quot;).asText();</span>
      }

<span class="fc" id="L240">      address = address.toLowerCase();</span>

      // Parse ABI section
<span class="fc" id="L243">      JsonNode abiNode = rootNode.path(&quot;abi&quot;);</span>
<span class="fc bfc" id="L244" title="All 2 branches covered.">      if (abiNode.isMissingNode()) {</span>
<span class="fc" id="L245">        String errorMessage = &quot;ABI section not found in JSON&quot;;</span>
<span class="fc" id="L246">        log.error(errorMessage);</span>
<span class="fc" id="L247">        throw new IOException(errorMessage);</span>
      }

      // append the contract address
<span class="fc" id="L251">      appendContractAddress(address);</span>

      // parse and register events
<span class="fc" id="L254">      parseAndRegisterEvents(address, contractName, abiNode.toString());</span>

<span class="fc" id="L256">      log.info(</span>
          &quot;ABI file processed: address={}, contract_name={}, last_modified={}, events={}&quot;,
          address,
          contractName,
          lastModified,
<span class="fc" id="L261">          contractEventStore.size());</span>

<span class="fc" id="L263">      return ContractInfo.builder()</span>
<span class="fc" id="L264">          .address(address)</span>
<span class="fc" id="L265">          .name(contractName)</span>
<span class="fc" id="L266">          .lastModified(lastModified)</span>
<span class="fc" id="L267">          .build();</span>
    } finally {
<span class="fc" id="L269">      inputStream.close();</span>
    }
  }

  /**
   * Find the first address in the networks section of the ABI JSON
   *
   * @param networksNode The networks node from the ABI JSON
   * @return The first address found, or an empty string if none found
   */
  private String findFirstAddressInNetworks(JsonNode networksNode) {
<span class="fc bfc" id="L280" title="All 2 branches covered.">    if (networksNode.isObject()) {</span>
<span class="fc" id="L281">      Iterator&lt;JsonNode&gt; elements = networksNode.elements();</span>
<span class="fc bfc" id="L282" title="All 2 branches covered.">      while (elements.hasNext()) {</span>
<span class="fc" id="L283">        JsonNode network = elements.next();</span>
<span class="fc bfc" id="L284" title="All 2 branches covered.">        if (network.has(DCFConst.ADDRESS)) {</span>
<span class="fc" id="L285">          return network.get(DCFConst.ADDRESS).asText();</span>
        }
<span class="fc" id="L287">      }</span>
    }
<span class="fc" id="L289">    return &quot;&quot;;</span>
  }

  /**
   * Adds a contract address to the list of monitored addresses
   *
   * @param address The contract address to add
   */
  public void appendContractAddress(String address) {
<span class="fc bfc" id="L298" title="All 2 branches covered.">    if (!contractAddresses.contains(address)) {</span>
<span class="fc" id="L299">      contractAddresses.add(address);</span>
<span class="fc" id="L300">      log.info(&quot;Added contract address: {}&quot;, address);</span>
    }
<span class="fc" id="L302">  }</span>

  /**
   * Parse ABI JSON and register events for a contract
   *
   * @param address Contract address
   * @param contractName Contract name
   * @param abiJson ABI JSON string
   * @throws IOException If parsing fails
   */
  public void parseAndRegisterEvents(String address, String contractName, String abiJson)
      throws IOException {
<span class="fc" id="L314">    contractEventStore.put(</span>
        address,
<span class="fc" id="L316">        ContractEvents.builder().contractName(contractName).events(parseAbi(abiJson)).build());</span>
<span class="fc" id="L317">    log.info(&quot;Registered events for contract: {} at address: {}&quot;, contractName, address);</span>
<span class="fc" id="L318">  }</span>

  /**
   * Retrieves the ABI event definition for a given log.
   *
   * @param ethLog The Ethereum log to process
   * @return The ABI event definition for the log
   * @throws Exception If no matching event is found
   */
  public org.web3j.abi.datatypes.Event getABIEventByLog(Log ethLog) throws Exception {
<span class="fc" id="L328">    String eventId = ethLog.getTopics().get(0).toLowerCase();</span>
<span class="fc" id="L329">    String logAddress = ethLog.getAddress().toLowerCase();</span>

<span class="fc" id="L331">    log.info(&quot;Looking for event with signature: {} for address: {}&quot;, eventId, logAddress);</span>

    Map&lt;String, ContractAbiEvent&gt; events =
<span class="fc bfc" id="L334" title="All 2 branches covered.">        contractEventStore.containsKey(logAddress)</span>
<span class="fc" id="L335">            ? contractEventStore.get(logAddress).events</span>
<span class="fc" id="L336">            : Collections.emptyMap();</span>

<span class="fc" id="L338">    log.info(&quot;Available signatures for address {}: {}&quot;, logAddress, events.keySet());</span>

<span class="fc bfc" id="L340" title="All 2 branches covered.">    if (events.containsKey(eventId)) {</span>
<span class="fc" id="L341">      log.info(&quot;Found matching event for signature: {}&quot;, eventId);</span>
<span class="fc" id="L342">      return events.get(eventId).getEvent();</span>
    }

<span class="fc" id="L345">    log.warn(&quot;No matching event found for signature: {} at address: {}&quot;, eventId, logAddress);</span>
<span class="fc" id="L346">    return null;</span>
  }

  /**
   * Retrieves the contract ABI event definition for a given log.
   *
   * @param log The Ethereum log to process
   * @return The contract ABI event definition for the log
   * @throws Exception If no matching event is found
   */
  public ContractAbiEvent getContractAbiEventByLog(Log log) throws Exception {
<span class="fc" id="L357">    String eventId = log.getTopics().get(0).toLowerCase();</span>
<span class="fc" id="L358">    String logAddress = log.getAddress().toLowerCase();</span>

    Map&lt;String, ContractAbiEvent&gt; events =
<span class="fc bfc" id="L361" title="All 2 branches covered.">        contractEventStore.containsKey(logAddress)</span>
<span class="fc" id="L362">            ? contractEventStore.get(logAddress).events</span>
<span class="fc" id="L363">            : Collections.emptyMap();</span>
<span class="fc bfc" id="L364" title="All 2 branches covered.">    if (events.containsKey(eventId)) {</span>
<span class="fc" id="L365">      return events.get(eventId);</span>
    }

<span class="fc" id="L368">    return null;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>