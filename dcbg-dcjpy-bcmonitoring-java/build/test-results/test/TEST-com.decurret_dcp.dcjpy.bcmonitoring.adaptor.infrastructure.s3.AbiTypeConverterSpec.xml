<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" tests="123" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:38.934Z" hostname="tungvt.local" time="0.047">
  <properties/>
  <testcase name="should convert Solidity type 'bool' with indexed=true to class org.web3j.abi.datatypes.Bool" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.002"/>
  <testcase name="should convert Solidity type 'boolean' with indexed=true to class org.web3j.abi.datatypes.Bool" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'address' with indexed=false to class org.web3j.abi.datatypes.Address" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'string' with indexed=false to class org.web3j.abi.datatypes.Utf8String" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'double' with indexed=false to class org.web3j.abi.datatypes.primitive.Double" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'float' with indexed=false to class org.web3j.abi.datatypes.primitive.Float" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'char' with indexed=false to class org.web3j.abi.datatypes.primitive.Char" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'short' with indexed=false to class org.web3j.abi.datatypes.primitive.Short" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'long' with indexed=false to class org.web3j.abi.datatypes.primitive.Long" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'byte' with indexed=true to class org.web3j.abi.datatypes.primitive.Byte" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes' with indexed=false to class org.web3j.abi.datatypes.DynamicBytes" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes1' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes1" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes2' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes2" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes3' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes3" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes4' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes4" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes5' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes5" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes6' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes6" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes7' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes7" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes8' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes8" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes9' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes9" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes10' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes10" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes11' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes11" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes12' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes12" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes13' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes13" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes14' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes14" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes15' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes15" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes16' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes16" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes17' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes17" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes18' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes18" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'bytes19' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes19" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes20' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes20" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes21' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes21" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes22' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes22" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes23' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes23" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes24' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes24" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes25' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes25" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes26' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes26" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes27' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes27" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes28' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes28" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'bytes29' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes29" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes30' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes30" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes31' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes31" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'bytes32' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes32" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint' with indexed=false to class org.web3j.abi.datatypes.Uint" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'uint8' with indexed=false to class org.web3j.abi.datatypes.generated.Uint8" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint16' with indexed=false to class org.web3j.abi.datatypes.generated.Uint16" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint24' with indexed=false to class org.web3j.abi.datatypes.generated.Uint24" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint32' with indexed=false to class org.web3j.abi.datatypes.generated.Uint32" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint40' with indexed=false to class org.web3j.abi.datatypes.generated.Uint40" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint48' with indexed=false to class org.web3j.abi.datatypes.generated.Uint48" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint56' with indexed=false to class org.web3j.abi.datatypes.generated.Uint56" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'uint64' with indexed=false to class org.web3j.abi.datatypes.generated.Uint64" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint72' with indexed=false to class org.web3j.abi.datatypes.generated.Uint72" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint80' with indexed=false to class org.web3j.abi.datatypes.generated.Uint80" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint88' with indexed=false to class org.web3j.abi.datatypes.generated.Uint88" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint96' with indexed=false to class org.web3j.abi.datatypes.generated.Uint96" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint104' with indexed=false to class org.web3j.abi.datatypes.generated.Uint104" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint112' with indexed=false to class org.web3j.abi.datatypes.generated.Uint112" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'uint120' with indexed=false to class org.web3j.abi.datatypes.generated.Uint120" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint128' with indexed=false to class org.web3j.abi.datatypes.generated.Uint128" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint136' with indexed=false to class org.web3j.abi.datatypes.generated.Uint136" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint144' with indexed=false to class org.web3j.abi.datatypes.generated.Uint144" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint152' with indexed=false to class org.web3j.abi.datatypes.generated.Uint152" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint160' with indexed=false to class org.web3j.abi.datatypes.generated.Uint160" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint168' with indexed=false to class org.web3j.abi.datatypes.generated.Uint168" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint176' with indexed=false to class org.web3j.abi.datatypes.generated.Uint176" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint184' with indexed=false to class org.web3j.abi.datatypes.generated.Uint184" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint192' with indexed=false to class org.web3j.abi.datatypes.generated.Uint192" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'uint200' with indexed=false to class org.web3j.abi.datatypes.generated.Uint200" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint208' with indexed=false to class org.web3j.abi.datatypes.generated.Uint208" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint216' with indexed=false to class org.web3j.abi.datatypes.generated.Uint216" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint224' with indexed=false to class org.web3j.abi.datatypes.generated.Uint224" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint232' with indexed=false to class org.web3j.abi.datatypes.generated.Uint232" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint240' with indexed=false to class org.web3j.abi.datatypes.generated.Uint240" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint248' with indexed=false to class org.web3j.abi.datatypes.generated.Uint248" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'uint256' with indexed=false to class org.web3j.abi.datatypes.generated.Uint256" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int8' with indexed=false to class org.web3j.abi.datatypes.generated.Int8" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int16' with indexed=false to class org.web3j.abi.datatypes.generated.Int16" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int24' with indexed=false to class org.web3j.abi.datatypes.generated.Int24" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int32' with indexed=false to class org.web3j.abi.datatypes.generated.Int32" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'int40' with indexed=false to class org.web3j.abi.datatypes.generated.Int40" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int48' with indexed=false to class org.web3j.abi.datatypes.generated.Int48" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int56' with indexed=false to class org.web3j.abi.datatypes.generated.Int56" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int64' with indexed=false to class org.web3j.abi.datatypes.generated.Int64" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int72' with indexed=false to class org.web3j.abi.datatypes.generated.Int72" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int80' with indexed=false to class org.web3j.abi.datatypes.generated.Int80" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int88' with indexed=false to class org.web3j.abi.datatypes.generated.Int88" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int96' with indexed=false to class org.web3j.abi.datatypes.generated.Int96" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int104' with indexed=false to class org.web3j.abi.datatypes.generated.Int104" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int112' with indexed=false to class org.web3j.abi.datatypes.generated.Int112" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int120' with indexed=false to class org.web3j.abi.datatypes.generated.Int120" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int128' with indexed=false to class org.web3j.abi.datatypes.generated.Int128" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int136' with indexed=false to class org.web3j.abi.datatypes.generated.Int136" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int144' with indexed=false to class org.web3j.abi.datatypes.generated.Int144" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int152' with indexed=false to class org.web3j.abi.datatypes.generated.Int152" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int160' with indexed=false to class org.web3j.abi.datatypes.generated.Int160" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should convert Solidity type 'int168' with indexed=false to class org.web3j.abi.datatypes.generated.Int168" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int176' with indexed=false to class org.web3j.abi.datatypes.generated.Int176" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int184' with indexed=false to class org.web3j.abi.datatypes.generated.Int184" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int192' with indexed=false to class org.web3j.abi.datatypes.generated.Int192" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int200' with indexed=false to class org.web3j.abi.datatypes.generated.Int200" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int208' with indexed=false to class org.web3j.abi.datatypes.generated.Int208" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int216' with indexed=false to class org.web3j.abi.datatypes.generated.Int216" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int224' with indexed=false to class org.web3j.abi.datatypes.generated.Int224" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int232' with indexed=false to class org.web3j.abi.datatypes.generated.Int232" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int240' with indexed=false to class org.web3j.abi.datatypes.generated.Int240" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int248' with indexed=false to class org.web3j.abi.datatypes.generated.Int248" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should convert Solidity type 'int256' with indexed=false to class org.web3j.abi.datatypes.generated.Int256" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should handle unsupported type gracefully" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.003"/>
  <testcase name="should handle tuple type with components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.013"/>
  <testcase name="should handle tuple array type with components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.002"/>
  <testcase name="should handle dynamic array type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should throw exception for unsupported array element type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should handle nested tuple components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.002"/>
  <testcase name="should throw exception for unsupported nested component type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="constructor should be callable for completeness" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should handle tuple type with null components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should handle tuple type with empty components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should handle non-tuple type with null components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <testcase name="should handle non-tuple type with components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should handle tuple type with null components in resolveComponentType" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should handle tuple type with empty components in resolveComponentType" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.0"/>
  <testcase name="should call getType method in dynamic array TypeReference" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec" time="0.001"/>
  <system-out><![CDATA[{"@timestamp":"2025-07-30T17:39:38.958176+07:00","@version":"1","message":"Error creating dynamic struct type: Unsupported type: unsupportedType","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.975+07:00","@version":"1","message":"Error creating dynamic struct type: Unsupported array element type: unsupportedElement","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.975562+07:00","@version":"1","message":"Processing nested tuple with 1 components","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.977952+07:00","@version":"1","message":"Error creating dynamic struct type: Unsupported type: unsupportedNestedType","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.978554+07:00","@version":"1","message":"Error creating dynamic struct type: Cannot invoke \"java.util.List.iterator()\" because \"components\" is null","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.980685+07:00","@version":"1","message":"Error creating dynamic struct type: Unsupported type: customType","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:38.981114+07:00","@version":"1","message":"Error creating dynamic struct type: Unsupported type: customType","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
