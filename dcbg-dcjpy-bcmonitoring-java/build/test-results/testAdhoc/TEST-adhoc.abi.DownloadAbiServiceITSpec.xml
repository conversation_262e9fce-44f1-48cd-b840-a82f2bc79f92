<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="adhoc.abi.DownloadAbiServiceITSpec" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:40:01.621Z" hostname="tungvt.local" time="81.51">
  <properties/>
  <testcase name="Should service start up successfully with command line runner and process ABI files" classname="adhoc.abi.DownloadAbiServiceITSpec" time="15.493"/>
  <testcase name="Should processes ABI files from multiple zones" classname="adhoc.abi.DownloadAbiServiceITSpec" time="11.995"/>
  <testcase name="Should correctly parses ABI files based on truffle environment variable" classname="adhoc.abi.DownloadAbiServiceITSpec" time="10.621"/>
  <testcase name="Should skip non-json file" classname="adhoc.abi.DownloadAbiServiceITSpec" time="10.581"/>
  <testcase name="Should skip deeply nested files and only process direct child objects" classname="adhoc.abi.DownloadAbiServiceITSpec" time="10.524"/>
  <testcase name="Should start fails when parsing malformed JSON" classname="adhoc.abi.DownloadAbiServiceITSpec" time="5.469"/>
  <testcase name="Should start fails when ABI file lacks required abi section" classname="adhoc.abi.DownloadAbiServiceITSpec" time="5.453"/>
  <testcase name="Should start fails when s3 connect timeout" classname="adhoc.abi.DownloadAbiServiceITSpec" time="5.46"/>
  <testcase name="Should fails to start when S3 bucket is inaccessible" classname="adhoc.abi.DownloadAbiServiceITSpec" time="5.904"/>
  <system-out><![CDATA[{"@timestamp":"2025-07-30T17:39:41.842481+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:41.931115+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.356943+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.358479+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.404854+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.405266+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.405445+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.406041+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.424352+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.687453+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:42.811014+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: 2209c26fb1c978298b247da9dcd6e9df36d4452dd5e7b7c45aae5427e8ed553c","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:43.549303+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT1.142492S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:43.560268+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:43.566071+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:44.308718+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: cd5f4c08c8cd2040f24ca40afc41636bf0acd1fdba409cc6d2034422e503546c","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.318062+07:00","@version":"1","message":"Container docker/compose:1.29.2 started in PT5.751986S","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.327083+07:00","@version":"1","message":"Docker Compose container is running for command: up -d","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.332462+07:00","@version":"1","message":"STDERR: Creating network \"k4mptavh4fcn_default\" with the default driver","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--672313395","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.333623+07:00","@version":"1","message":"Docker Compose has finished running","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.333613+07:00","@version":"1","message":"STDERR: Creating k4mptavh4fcn_localstack_1 ... ","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--672313395","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.334789+07:00","@version":"1","message":"STDERR: Creating k4mptavh4fcn_localstack_1 ... done","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--672313395","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.344771+07:00","@version":"1","message":"Creating container for image: alpine/socat:1.7.4.3-r0","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:49.418683+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 is starting: 97f28bb7c0fecebd34228a7dbe650bd19cb197914ab09f4b08f7194de5bb6ff2","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:39:50.248853+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 started in PT0.904428S","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LocalStack started successfully on port: 55630
Checking if table exists: local-Events
Table does not exist: local-Events
Attempting to create table: local-BlockHeight
Checking if table exists: local-BlockHeight
Table does not exist: local-BlockHeight
Table does not exist, creating: local-BlockHeight
Table created successfully: local-BlockHeight
Attempting to create bucket: abijson-local-bucket
Checking if bucket exists: abijson-local-bucket
Bucket exists: abijson-local-bucket
Bucket already exists: abijson-local-bucket
17:40:02,197 |-WARN in ch.qos.logback.core.model.processor.AppenderModelHandler - Appender named [CONSOLE_TEXT] not referenced. Skipping further processing.
17:40:02,197 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE_JSON]
17:40:02,197 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
17:40:02,199 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE_JSON] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
17:40:02,199 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE_JSON] - console in production environments, especially in high volume systems.
17:40:02,199 |-INFO in ch.qos.logback.core.ConsoleAppender[CONSOLE_JSON] - See also https://logback.qos.ch/codes.html#slowConsole
17:40:02,199 |-WARN in ch.qos.logback.core.model.processor.AppenderModelHandler - Appender named [FILE_TEXT] not referenced. Skipping further processing.
17:40:02,199 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [FILE_JSON]
17:40:02,199 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
17:40:02,200 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@622702525 - setting totalSizeCap to 1 GB
17:40:02,200 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@622702525 - No compression will be used
17:40:02,200 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@622702525 - Will use the pattern logs/app.%d{yyyy-MM-dd}.json for the active file
17:40:02,200 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd' from file name pattern 'logs/app.%d{yyyy-MM-dd}.json'.
17:40:02,200 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at midnight.
17:40:02,201 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to 2025-07-30T10:39:50.249Z
17:40:02,201 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE_JSON] - Active log file name: logs/app.json
17:40:02,201 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE_JSON] - File property is set to [logs/app.json]
17:40:02,201 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
17:40:02,201 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@279dac48 - Propagating INFO level on Logger[ROOT] onto the JUL framework
17:40:02,202 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE_JSON] to Logger[ROOT]
17:40:02,202 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE_JSON] to Logger[ROOT]
17:40:02,202 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.decurret_dcp.dcjpy] to DEBUG
17:40:02,202 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@279dac48 - Propagating DEBUG level on Logger[com.decurret_dcp.dcjpy] onto the JUL framework
17:40:02,202 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.springframework] to INFO
17:40:02,202 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@279dac48 - Propagating INFO level on Logger[org.springframework] onto the JUL framework
17:40:02,202 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [software.amazon.awssdk] to INFO
17:40:02,202 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@279dac48 - Propagating INFO level on Logger[software.amazon.awssdk] onto the JUL framework
17:40:02,202 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@e6342de - End of configuration.
17:40:02,202 |-INFO in org.springframework.boot.logging.logback.SpringBootJoranConfigurator@612c909a - Registering current configuration as safe fallback point


  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.3)

{"@timestamp":"2025-07-30T17:40:02.312697+07:00","@version":"1","message":"Starting DownloadAbiServiceITSpec using Java 21.0.8 with PID 16923 (started by thanhtungvu in /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java)","logger_name":"adhoc.abi.DownloadAbiServiceITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:02.314119+07:00","@version":"1","message":"The following 1 profile is active: \"test\"","logger_name":"adhoc.abi.DownloadAbiServiceITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:03.29288+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:03.651662+07:00","@version":"1","message":"Using LocalStack credentials for local environment","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:03.654828+07:00","@version":"1","message":"Configuring S3 client for local environment with endpoint: http://localhost:55630 and region: ap-northeast-1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:03.713838+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:03.822565+07:00","@version":"1","message":"Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)","logger_name":"org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration","thread_name":"Test worker","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:04.090948+07:00","@version":"1","message":"Started DownloadAbiServiceITSpec in 1.982 seconds (process running for 23.887)","logger_name":"adhoc.abi.DownloadAbiServiceITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
{"@timestamp":"2025-07-30T17:40:04.812963+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:04.813381+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:04.902898+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:04.995162+07:00","@version":"1","message":"Added contract address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.297662+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10a23da, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d3419ac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c4b6076, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@19d7f0d2], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.298229+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.298601+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@491314c1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74578d36, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23481b1a], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.298825+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.299486+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3eb3663, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b051f7a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ba914e7], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.299705+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.30002+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28cf3047, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b1214eb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@382f2742], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.300575+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.313599+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.317421+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.318703+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7d201db5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c50ba3d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b321a1a], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.318947+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.322467+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.322966+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@61c613f9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@48eeccae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4307fd63, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c9d00a4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d958f15, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a0bfb5], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.323191+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.323542+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bac1135], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.323712+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.324013+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c0b838a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@167382d2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@dcd1c90], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.324197+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.324355+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.324512+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.324948+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Wed Jul 30 17:40:04 ICT 2025, events=1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.326631+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.340581+07:00","@version":"1","message":"Added contract address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.34352+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3083f394, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7d59d4c8], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.34376+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.344016+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@296fb238, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@192c70fb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@369c3f19, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5504296], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.344209+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.34448+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51fade76, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b2bc71a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c184191], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.344663+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.344902+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b5be876, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c559005, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9b38b82], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.345084+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.345303+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@379957ec], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.345472+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.345704+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@13a51131, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@43207ef8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@565a0285], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.345888+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.346127+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b810995, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1096b94d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5001d219], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.346522+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.347127+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75a7da4b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@505e5963, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@e91c86b], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.347365+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.347625+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15267903, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5e189c6e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7caf538f], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.347824+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.347994+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.348339+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.348569+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Wed Jul 30 17:40:04 ICT 2025, events=2","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.348905+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.366318+07:00","@version":"1","message":"Added contract address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.369265+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@527f865d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@244ca7e0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@406aa363, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@a526b3a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24314943], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.369554+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.369831+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65039982, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@70e75ffa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6975f167, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47a0d65b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3e1688a4], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.370023+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.370288+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74c40813, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@32fc3125, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@732f3b2f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7976ce46, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2da6d5eb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7e80bc63, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@747c06df, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bb7f2e8], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.370514+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.37078+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@20a561f8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5bbdb22b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f631d7d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b4786cb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@406684fa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@767c5a37, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@21a33970, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@56a58251, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22108d2b], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.371033+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.371302+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@36a9c6bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@e7911a0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7434bd34, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@52eb1f4c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2a0fbf8b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6cbd4f7b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3db192c5], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.371702+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.371999+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@372afb40], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.372197+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.372466+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@600ef9c9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@66cea424, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@57583582, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@374824d2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@209f5343, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33633cf0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2581eb3b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@539935ed], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.372703+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.37295+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9467196, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23e858f2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@91aa787, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@13b6dd14], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.373142+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.37338+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@29c6d435, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@48082637, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1472962e], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.373575+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.382458+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.383253+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5bc9e520, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@78ce78da], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.383466+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.383644+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.383886+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.384031+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Wed Jul 30 17:40:04 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.384295+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.384423+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.39102+07:00","@version":"1","message":"Created new DynamoDB connection","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.559277+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.559685+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.608717+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:05.61013+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
Uploading ABI files from hardhat/3001 for network: 3001 to bucket: abijson-local-bucket
Uploading ABI file: 3001/Token.json (22752 bytes)
Uploading ABI file: 3001/Account.json (27014 bytes)
Uploading ABI file: 3001/Provider.json (17085 bytes)
{"@timestamp":"2025-07-30T17:40:18.775449+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.776309+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.831342+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
{"@timestamp":"2025-07-30T17:40:18.842399+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@178792cf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@123c674d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4739033b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1142224b], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.843111+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.843315+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a78db46, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b0fd54b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44a027ca], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.843455+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.843623+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44f0156f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38ae445c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@d3e6b8f], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.843845+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.844016+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c7aac9b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@46bc95ec], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.844143+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.849136+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.851413+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.851794+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fad865, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@27cb2ec3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@18313b59], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.852011+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.85399+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.854342+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7a9f40a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4dcd40e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1526cc3f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10365f1c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10acc7b2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72321d0d], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.854518+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.854698+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51567040], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.8548+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.854953+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@733a1bc9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ddd34b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@67657c88], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.855072+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.855163+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.855256+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.855331+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.855588+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.864137+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5cf70c4f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69fb83e3], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.864412+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.86457+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@f973a72, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7915a240, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1de44c59, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2835309d], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.864695+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.864833+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ec7db43, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4a146d79, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75ad66e6], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.86494+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865072+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@749d0116, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5420c105, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3db6115d], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865175+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865296+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2332ca5b], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865386+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865513+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fbbfa9a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6dc12c81, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60f0f49d], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865617+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.865783+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55a59aca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35aac5ce, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51eb52de], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.86588+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866015+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@36d81062, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f52afb9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c358728], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866131+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866258+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@27435dd2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51ee0258, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b9b50bb], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866364+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866455+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866578+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866671+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.866842+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875103+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7164bb9f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@78b053d2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@39b34d87, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55f4f6fe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@118079c], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875419+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875588+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bcceaae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@58b05f25, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2a0111cb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6be4c7f0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77e1c9b8], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875703+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875865+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2d72e916, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@f5f2a3a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@42c9a766, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3b9c1a8e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11c7eb2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77649a51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7819e14c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@238accab], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.875986+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876149+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4101df86, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@126463e5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25c0f364, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@50da9ce3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fe88491, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ea2a7c2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c5b3aa1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@8e2824d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@53f553cc], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876264+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876411+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@68d272c7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d89d34e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@21e5ce58, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5877829b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23488718, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5028339c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5f30a0cf], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876519+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876642+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bb782e8], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876739+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.876885+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ca0b2c1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d05de1a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@176939ad, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@104e596a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4921c718, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ab72528, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@744dc582, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@162a5768], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.877011+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.877152+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51f44bb2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4279aac1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@588206d9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c578bb6], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.877251+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.87738+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6f8dfa55, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60ad7ac7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28510cc5], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.877479+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.88166+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.882079+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@8a4245a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d40ccb], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.882197+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.882284+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.882367+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.882442+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.930035+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3001/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.939244+07:00","@version":"1","message":"Added contract address: 0xfb4e85f8cc1228f650d30099e6b8adb58f588368","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.940912+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@118ddb1e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45963dc2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fef5c91], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941072+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941224+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74a9871c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b83e7bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7f0fa323], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941336+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941512+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a417387, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@26882b4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16422b42], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941639+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941789+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25ba1054, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@63014b75], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.941959+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.944783+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.94511+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@31058a94, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@58b62522, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f1c874c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b875304, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@564b21ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3f3d9114], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945263+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945405+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@14b05758], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.9455+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945652+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65de29b9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a1401b9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@397288de], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945763+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945853+07:00","@version":"1","message":"Successfully parsed ABI with 7 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.945976+07:00","@version":"1","message":"Registered events for contract: Account at address: 0xfb4e85f8cc1228f650d30099e6b8adb58f588368","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.946101+07:00","@version":"1","message":"ABI file processed: address=0xfb4e85f8cc1228f650d30099e6b8adb58f588368, contract_name=Account, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.946267+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3001/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.957423+07:00","@version":"1","message":"Added contract address: 0x2ce44ab07487a4021fbea8c8c2f0354d6bce08c7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.958645+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3836e47, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73b68e88], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.958782+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.958935+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@573c34e3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@145cddd2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c356848, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b306588], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.959047+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.959188+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7d9dcb21, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@f5d2b0d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@288d6586], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.959524+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.959783+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60d9593a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@730e52c3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@156d1aec], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.959889+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960024+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@568fec92], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960115+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960245+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c357ff0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7dacb2ff, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6b6ddc], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960352+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.96049+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@29a9ea3f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7e34ca05, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6697a476], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960591+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960728+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d4920f5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5b57dd71, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@36b50fc], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960831+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.960964+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5804f0f3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2cbe2d8e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49e7c13d], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.961068+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.961151+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.961241+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0x2ce44ab07487a4021fbea8c8c2f0354d6bce08c7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.961315+07:00","@version":"1","message":"ABI file processed: address=0x2ce44ab07487a4021fbea8c8c2f0354d6bce08c7, contract_name=Provider, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.961472+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3001/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.968174+07:00","@version":"1","message":"Added contract address: 0x57fe7860bd66790b7e38021374ae00b3d030f3c7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.969538+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@414cbabc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5d90efd4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60f39523, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@643a3a79, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4087f518], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.96968+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.969843+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ab35c93, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6695986b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@53c99f32, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c998f66, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2cce19a2], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.969957+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970204+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@66d28a90, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@79fdb977, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@19ffeb91, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45a0f205, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37d4662b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@237657a7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45b3833b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7417152], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970328+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970498+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1330aaaf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ea1c01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5b19fd3c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@57deccec, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@41ad616a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1375674a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7918805, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@26099db7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c050e2d], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970616+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970779+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34f267d0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fa810eb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5526c68c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71f6ce63, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@569deb6a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@294d961a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4abc2719], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.970928+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971047+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@dfe6dfb], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971132+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971267+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34011195, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71b9ac49, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5a16c988, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2479e684, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@18c8c627, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24afea93, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@48193d07, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@233faf46], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971388+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971536+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7d18bcf8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@26b83aba, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2748ca6f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7953145f], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971633+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971767+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@411498f1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@269e0783, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47e4aa8d], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.971887+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976029+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976403+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@434268fd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1536cd09], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976511+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976643+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976718+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x57fe7860bd66790b7e38021374ae00b3d030f3c7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.976791+07:00","@version":"1","message":"ABI file processed: address=0x57fe7860bd66790b7e38021374ae00b3d030f3c7, contract_name=Token, last_modified=Wed Jul 30 17:40:18 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.97691+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.977182+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.977315+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.992511+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.992872+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.994566+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:18.994792+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
S3 bucket abijson-local-bucket successfully cleared
Uploading ABI files from truffle/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Account.json (26279 bytes)
{"@timestamp":"2025-07-30T17:40:29.556259+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.556741+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.61303+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
{"@timestamp":"2025-07-30T17:40:29.624119+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3f40ba92, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@a07134c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@12f8f720, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ac8f75d], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.624469+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.624681+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@30e648bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@249b09f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@575d1601], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.624818+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.625009+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1a6261a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@184379f0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5741ff2f], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.625134+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.625311+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@689316d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62fa3c64], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.625435+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.6292+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.629583+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4acfebce, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15bf089, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@32f1f5da, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@587d9280, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d5983da, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3358e940], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.629757+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.629912+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@e3a85d4], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630035+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630188+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ae7ef01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b01ecd4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6f67141d], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630298+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630383+07:00","@version":"1","message":"Successfully parsed ABI with 7 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630453+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630531+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Wed Jul 30 17:40:29 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630647+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.63071+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.630788+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.65367+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.653957+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.655729+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:29.655896+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
S3 bucket abijson-local-bucket successfully cleared
Uploading ABI files from invalid for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/nonJson.txt (4 bytes)
{"@timestamp":"2025-07-30T17:40:40.119123+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.119348+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.178175+07:00","@version":"1","message":"This object will be skipped because the extension is not .json: 3000/nonJson.txt","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.178447+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.178517+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.178598+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.194644+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.194865+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.196213+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:40.196329+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/nested_directory/FinancialCheck.json (9118 bytes)
{"@timestamp":"2025-07-30T17:40:50.597856+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.598181+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.649623+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
{"@timestamp":"2025-07-30T17:40:50.657481+07:00","@version":"1","message":"Added contract address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658612+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2a15e54, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fcbcea, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@709b0805, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c70bce9], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658701+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658782+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24ab04cd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@42d6f75e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a250421], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658832+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658907+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@439b6a78, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3f6a83aa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4af3ef1e], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.658964+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.659038+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@8b5fe6d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10e58da9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bf92bcc], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.659091+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.661284+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.66283+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.663088+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7e7c3f0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1456519e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2e062494], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.663157+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664284+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664452+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@335f560c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15a33b58, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@a1e8ffc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@779afb4f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3e3c3c86, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@32c9a9e2], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664515+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664585+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c56a128], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664661+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664754+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c367b6e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33b1b262, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c489b4f], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.66481+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664859+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.664897+07:00","@version":"1","message":"Registered events for contract: Account at address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.66493+07:00","@version":"1","message":"ABI file processed: address=, contract_name=Account, last_modified=Wed Jul 30 17:40:50 ICT 2025, events=7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.665044+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.672592+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1890fbb8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1426a699], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.672833+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.672948+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@42ba4ff0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f3e34b5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@506c5bd1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71df3d2b], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673014+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673111+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@618a392f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5f2f2237, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d2f58d0], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673172+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673264+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6591063b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@556218e7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73fab6e6], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673326+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673413+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@64049991], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.67349+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673586+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9c9fbf4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c296f0a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a42bdef], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673642+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673731+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2a4a7927, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c9a31c1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6b4c4267], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673795+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673896+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@141e2724, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6de4f7c1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@50cdc785], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.673979+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674073+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@599bf881, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3cd9c391, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@76497134], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674133+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674185+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674228+07:00","@version":"1","message":"Registered events for contract: Provider at address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674263+07:00","@version":"1","message":"ABI file processed: address=, contract_name=Provider, last_modified=Wed Jul 30 17:40:50 ICT 2025, events=7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.674368+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.680931+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ae485e3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4a348dbe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@342593f7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1f2f9ac2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@41badf59], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681133+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681249+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2f0de3ad, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@372f7790, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5435123d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@43624ff5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@332dcbf], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681315+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681467+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f6db8be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3335ff94, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@627903e0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@773bfc34, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55310225, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49649e48, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@63a8e287, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b5a689a], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681572+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681695+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16a76bfe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bdc31bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5f44589a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47c2f550, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a978ecd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@705c03f2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@da115eb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5d19197a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b2e15f2], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681757+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.68187+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b7dd4f6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ef6843f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@bb19c52, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@70cb8953, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@56439d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2f199237, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@284dd88a], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.681933+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682018+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4e05dc1], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682084+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682195+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@206e1e7c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4385a86c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5cab11e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@342ea33, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@530f99d9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d279d1c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d3589e7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@79b2f500], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682262+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682358+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@565c6abe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5712d214, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@162903a0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@78ddfe90], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682425+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682514+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d6162af, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ca28b65, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5248ad52], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.682571+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685454+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685695+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34486fe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b25dc50], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685744+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685781+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685811+07:00","@version":"1","message":"Registered events for contract: Token at address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685841+07:00","@version":"1","message":"ABI file processed: address=, contract_name=Token, last_modified=Wed Jul 30 17:40:50 ICT 2025, events=7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.68592+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685949+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.685986+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.696817+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.696996+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.69802+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:40:50.698096+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
Uploading ABI files from invalid for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/MalformedJson.json (259 bytes)
{"@timestamp":"2025-07-30T17:41:01.071183+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.071787+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.131379+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
{"@timestamp":"2025-07-30T17:41:01.147883+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@29c41af2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55f35bb2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7940abfb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.148614+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.14899+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f8b199b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@eaf513, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c899986], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.149245+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.149542+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3613d6a6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2143563e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@76567d68], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.149801+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.150458+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@46239f8d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6e2dd709, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55e49e54], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.15066+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.156009+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.162083+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.162809+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4a0c2d01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3beb1c2a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ba24e06], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.16297+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.166667+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.166977+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6381fe05, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@e8a413, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@59e31876, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@64211a94, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@8bcb1c3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4db154fb], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167085+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167244+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@cd0d593], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167313+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167472+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60499b27, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@d160c44, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ac79791], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167557+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167632+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.1677+07:00","@version":"1","message":"Registered events for contract: Account at address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.16776+07:00","@version":"1","message":"ABI file processed: address=, contract_name=Account, last_modified=Wed Jul 30 17:41:01 ICT 2025, events=7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.167954+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/MalformedJson.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.2235+07:00","@version":"1","message":"Failed to parse S3 abi object: 3000/MalformedJson.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"com.fasterxml.jackson.core.io.JsonEOFException: Unexpected end-of-input: expected close marker for Array (start marker at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 8, column: 17])\n at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 13, column: 10]\n\tat com.fasterxml.jackson.core.base.ParserMinimalBase._reportInvalidEOF(ParserMinimalBase.java:641)\n\tat com.fasterxml.jackson.core.base.ParserBase._handleEOF(ParserBase.java:530)\n\tat com.fasterxml.jackson.core.base.ParserBase._eofAsNextChar(ParserBase.java:547)\n\tat com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipWSOrEnd(UTF8StreamJsonParser.java:3066)\n\tat com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:716)\n\tat com.fasterxml.jackson.databind.deser.std.BaseNodeDeserializer._deserializeContainerNoRecursion(JsonNodeDeserializer.java:609)\n\tat com.fasterxml.jackson.databind.deser.std.JsonNodeDeserializer.deserialize(JsonNodeDeserializer.java:100)\n\tat com.fasterxml.jackson.databind.deser.std.JsonNodeDeserializer.deserialize(JsonNodeDeserializer.java:25)\n\tat com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)\n\tat com.fasterxml.jackson.databind.ObjectMapper._readTreeAndClose(ObjectMapper.java:4959)\n\tat com.fasterxml.jackson.databind.ObjectMapper.readTree(ObjectMapper.java:3295)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.parseAbiContent(AbiParser.java:230)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:115)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_5(DownloadAbiServiceITSpec.groovy:234)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\n","application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:01.226982+07:00","@version":"1","message":"Error starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Failed to parse S3 abi object: 3000/MalformedJson.json\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:119)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_5(DownloadAbiServiceITSpec.groovy:234)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\nCaused by: com.fasterxml.jackson.core.io.JsonEOFException: Unexpected end-of-input: expected close marker for Array (start marker at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 8, column: 17])\n at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 13, column: 10]\n\tat com.fasterxml.jackson.core.base.ParserMinimalBase._reportInvalidEOF(ParserMinimalBase.java:641)\n\tat com.fasterxml.jackson.core.base.ParserBase._handleEOF(ParserBase.java:530)\n\tat com.fasterxml.jackson.core.base.ParserBase._eofAsNextChar(ParserBase.java:547)\n\tat com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipWSOrEnd(UTF8StreamJsonParser.java:3066)\n\tat com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:716)\n\tat com.fasterxml.jackson.databind.deser.std.BaseNodeDeserializer._deserializeContainerNoRecursion(JsonNodeDeserializer.java:609)\n\tat com.fasterxml.jackson.databind.deser.std.JsonNodeDeserializer.deserialize(JsonNodeDeserializer.java:100)\n\tat com.fasterxml.jackson.databind.deser.std.JsonNodeDeserializer.deserialize(JsonNodeDeserializer.java:25)\n\tat com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)\n\tat com.fasterxml.jackson.databind.ObjectMapper._readTreeAndClose(ObjectMapper.java:4959)\n\tat com.fasterxml.jackson.databind.ObjectMapper.readTree(ObjectMapper.java:3295)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.parseAbiContent(AbiParser.java:230)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:115)\n\t... 110 common frames omitted\n","application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
Uploading ABI files from invalid for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/LacksAbiSection.json (244 bytes)
{"@timestamp":"2025-07-30T17:41:06.531627+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.531953+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.589019+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
LOGBACK: No context given for ch.qos.logback.core.read.ListAppender[null]
{"@timestamp":"2025-07-30T17:41:06.609705+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a9d80d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1e019892, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4de89da7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fd956b8], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.612447+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.613212+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@527d9cfe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@402a5b2b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4926e32c], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.613484+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.613735+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c1f0026, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@223be537, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@305a1158], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.613902+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.614045+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4e9bbbfc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ae19c39, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@570ed60f], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.614214+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.621166+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.624283+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.624731+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24f94d7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2350843f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62c4b0c7], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.624845+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.626995+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627215+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@76714c33, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51275fbe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16a28fd5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2a3e95ed, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5652444d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3d632b96], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627319+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627422+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fbdd7c8], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.62749+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627591+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6172bc2f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2546ec60, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15d4cc5], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627674+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627751+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627813+07:00","@version":"1","message":"Registered events for contract: Account at address: ","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.627871+07:00","@version":"1","message":"ABI file processed: address=, contract_name=Account, last_modified=Wed Jul 30 17:41:06 ICT 2025, events=7","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.628109+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/LacksAbiSection.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.676199+07:00","@version":"1","message":"ABI section not found in JSON","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.67651+07:00","@version":"1","message":"Failed to parse S3 abi object: 3000/LacksAbiSection.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: ABI section not found in JSON\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.parseAbiContent(AbiParser.java:247)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:115)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_6(DownloadAbiServiceITSpec.groovy:259)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\n","application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:06.677836+07:00","@version":"1","message":"Error starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Failed to parse S3 abi object: 3000/LacksAbiSection.json\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:119)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_6(DownloadAbiServiceITSpec.groovy:259)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\nCaused by: java.io.IOException: ABI section not found in JSON\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.parseAbiContent(AbiParser.java:247)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:115)\n\t... 110 common frames omitted\n","application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
S3 bucket abijson-local-bucket successfully cleared
{"@timestamp":"2025-07-30T17:41:12.147857+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:12.148295+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:12.152707+07:00","@version":"1","message":"Error starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"software.amazon.awssdk.core.exception.ApiCallTimeoutException: Client execution did not complete before the specified timeout configuration: 1 millis\n\tat software.amazon.awssdk.core.exception.ApiCallTimeoutException$BuilderImpl.build(ApiCallTimeoutException.java:106)\n\tat software.amazon.awssdk.core.exception.ApiCallTimeoutException.create(ApiCallTimeoutException.java:38)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.generateApiCallTimeoutException(ApiCallTimeoutTrackingStage.java:156)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.handleInterruptedException(ApiCallTimeoutTrackingStage.java:144)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.translatePipelineException(ApiCallTimeoutTrackingStage.java:109)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:64)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:43)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:50)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:32)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\n\tat software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:210)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:173)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:80)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:182)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:74)\n\tat software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\n\tat software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:53)\n\tat software.amazon.awssdk.services.s3.DefaultS3Client.listObjectsV2(DefaultS3Client.java:8101)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor.listCommonPrefixesObjects(S3ClientAdaptor.java:37)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:62)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_7(DownloadAbiServiceITSpec.groovy:300)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\n","application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
S3 bucket abijson-local-bucket successfully cleared
Web3j mock setup completed
Web3jCaller mock setup completed
Reset running flag to true
Uploading ABI files from hardhat/3000 for network: 3000 to bucket: abijson-local-bucket
Uploading ABI file: 3000/Token.json (23124 bytes)
Uploading ABI file: 3000/Account.json (31881 bytes)
Uploading ABI file: 3000/Provider.json (18879 bytes)
{"@timestamp":"2025-07-30T17:41:17.433806+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:17.434086+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-30T17:41:18.018285+07:00","@version":"1","message":"Error starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"software.amazon.awssdk.core.exception.SdkClientException: Received an UnknownHostException when attempting to interact with a service. See cause for the exact endpoint that is failing to resolve. If this is happening on an endpoint that previously worked, there may be a network connectivity issue or your DNS cache could be storing endpoints for too long.\n\tat software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:130)\n\tat software.amazon.awssdk.awscore.interceptor.HelpfulUnknownHostExceptionInterceptor.modifyException(HelpfulUnknownHostExceptionInterceptor.java:59)\n\tat software.amazon.awssdk.core.interceptor.ExecutionInterceptorChain.modifyException(ExecutionInterceptorChain.java:181)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.utils.ExceptionReportingUtils.runModifyException(ExceptionReportingUtils.java:54)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.utils.ExceptionReportingUtils.reportFailureToInterceptors(ExceptionReportingUtils.java:38)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:39)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\n\tat software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:210)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:173)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:80)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:182)\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:74)\n\tat software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\n\tat software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:53)\n\tat software.amazon.awssdk.services.s3.DefaultS3Client.listObjectsV2(DefaultS3Client.java:8101)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor.listCommonPrefixesObjects(S3ClientAdaptor.java:37)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService.execute(DownloadAbiService.java:62)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.startBCMonitoring(MonitoringRunnerConfig.java:55)\n\tat com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig.lambda$commandLineRunner$0(MonitoringRunnerConfig.java:43)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.abi.DownloadAbiServiceITSpec.$spock_feature_1_8(DownloadAbiServiceITSpec.groovy:336)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:336)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:51)\n\tat org.spockframework.runtime.IterationNode.execute(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:68)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:244)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:226)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:68)\n\tat org.spockframework.runtime.IterationNode.around(IterationNode.java:13)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask$DefaultDynamicTestExecutor.execute(NodeTestTask.java:231)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)\n\tat org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:19)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:73)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:208)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:199)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:73)\n\tat org.spockframework.runtime.FeatureNode.around(FeatureNode.java:30)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\nCaused by: software.amazon.awssdk.core.exception.SdkClientException: Unable to execute HTTP request: abijson-local-bucket.localhost (SDK Attempt Count: 4)\n\tat software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:130)\n\tat software.amazon.awssdk.core.exception.SdkClientException$BuilderImpl.build(SdkClientException.java:95)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.utils.RetryableStageHelper.retryPolicyDisallowedRetryException(RetryableStageHelper.java:168)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:73)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:36)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:53)\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:35)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.executeWithTimer(ApiCallTimeoutTrackingStage.java:82)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:62)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:43)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:50)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:32)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\n\t... 122 common frames omitted\n\tSuppressed: software.amazon.awssdk.core.exception.SdkClientException: Request attempt 1 failure: Unable to execute HTTP request: abijson-local-bucket.localhost: nodename nor servname provided, or not known\n\tSuppressed: software.amazon.awssdk.core.exception.SdkClientException: Request attempt 2 failure: Unable to execute HTTP request: abijson-local-bucket.localhost\n\tSuppressed: software.amazon.awssdk.core.exception.SdkClientException: Request attempt 3 failure: Unable to execute HTTP request: abijson-local-bucket.localhost\nCaused by: java.net.UnknownHostException: abijson-local-bucket.localhost\n\tat java.base/java.net.InetAddress$CachedLookup.get(InetAddress.java:988)\n\tat java.base/java.net.InetAddress.getAllByName0(InetAddress.java:1818)\n\tat java.base/java.net.InetAddress.getAllByName(InetAddress.java:1688)\n\tat org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45)\n\tat org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112)\n\tat org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)\n\tat software.amazon.awssdk.http.apache.internal.conn.ClientConnectionManagerFactory$DelegatingHttpClientConnectionManager.connect(ClientConnectionManagerFactory.java:86)\n\tat org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)\n\tat org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)\n\tat org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)\n\tat org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)\n\tat software.amazon.awssdk.http.apache.internal.impl.ApacheSdkHttpClient.execute(ApacheSdkHttpClient.java:72)\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient.execute(ApacheHttpClient.java:259)\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient.access$600(ApacheHttpClient.java:104)\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient$1.call(ApacheHttpClient.java:236)\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient$1.call(ApacheHttpClient.java:233)\n\tat software.amazon.awssdk.core.internal.util.MetricUtils.measureDurationUnsafe(MetricUtils.java:102)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.MakeHttpRequestStage.executeHttpRequest(MakeHttpRequestStage.java:88)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.MakeHttpRequestStage.execute(MakeHttpRequestStage.java:64)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.MakeHttpRequestStage.execute(MakeHttpRequestStage.java:46)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:74)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:43)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:79)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:41)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:55)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:39)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.executeRequest(RetryableStage.java:93)\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:56)\n\t... 134 common frames omitted\n","application":"bcmonitoring"}
S3 bucket abijson-local-bucket successfully cleared
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
