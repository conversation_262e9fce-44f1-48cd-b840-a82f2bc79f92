<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="@localhost" uuid="0bfe5c89-2cba-47fb-8cfc-111f6f706b9c">
      <driver-ref>dynamo</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.jetbrains.DynamoDriver</jdbc-driver>
      <jdbc-url>****************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="Profile" value="localstack" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>