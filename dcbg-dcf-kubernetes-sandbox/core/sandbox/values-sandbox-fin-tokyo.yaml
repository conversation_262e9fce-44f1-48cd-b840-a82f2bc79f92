envName: sbx-fin

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/sbx-fin-core
    tag: "787a42f"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/sbx-fin-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/sbx-fin-tokyo-core-alb-tg/9dbb8a15522c71a0

configMap:
  DB_BASE: "sbx-fin-tokyo-rds-cluster.cluster-crywzgfrboya.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_h2baqPXb8/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_h2baqPXb8"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"
