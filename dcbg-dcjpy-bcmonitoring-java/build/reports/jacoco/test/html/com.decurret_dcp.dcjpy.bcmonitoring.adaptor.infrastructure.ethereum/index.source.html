<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <span class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</span></div><h1>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">25 of 1,068</td><td class="ctr2">97%</td><td class="bar">2 of 88</td><td class="ctr2">97%</td><td class="ctr1">3</td><td class="ctr2">66</td><td class="ctr1">7</td><td class="ctr2">263</td><td class="ctr1">1</td><td class="ctr2">22</td><td class="ctr1">0</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="EthEventLogDao.java.html" class="el_source">EthEventLogDao.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="117" height="10" title="996" alt="996"/></td><td class="ctr2" id="c1">97%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="117" height="10" title="86" alt="86"/></td><td class="ctr2" id="e0">97%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">63</td><td class="ctr1" id="h0">7</td><td class="ctr2" id="i0">249</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="EventLogRepositoryImpl.java.html" class="el_source">EventLogRepositoryImpl.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="47" alt="47"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">3</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>