Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ParameterInfo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ParameterInfo.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ParameterInfo.java
new file mode 100644
--- /dev/null	(date 1750839315290)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ParameterInfo.java	(date 1750839315290)
@@ -0,0 +1,14 @@
+package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;
+
+import lombok.Builder;
+import org.web3j.abi.TypeReference;
+
+/**
+ * Represents parameter information from ABI including name and type reference
+ */
+@Builder
+public class ParameterInfo {
+  public final String name;
+  public final TypeReference<?> typeReference;
+  public final boolean indexed;
+}
Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/EventDefinition.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/EventDefinition.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/EventDefinition.java
new file mode 100644
--- /dev/null	(date 1750839339874)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/EventDefinition.java	(date 1750839339874)
@@ -0,0 +1,33 @@
+package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;
+
+import lombok.Builder;
+import org.web3j.abi.datatypes.Event;
+import java.util.List;
+
+/**
+ * Enhanced event definition that includes parameter names from ABI
+ */
+@Builder
+public class EventDefinition {
+  public final String name;
+  public final Event web3jEvent;
+  public final List<ParameterInfo> parameters;
+  
+  /**
+   * Get indexed parameters with their names
+   */
+  public List<ParameterInfo> getIndexedParameters() {
+    return parameters.stream()
+        .filter(param -> param.indexed)
+        .toList();
+  }
+  
+  /**
+   * Get non-indexed parameters with their names
+   */
+  public List<ParameterInfo> getNonIndexedParameters() {
+    return parameters.stream()
+        .filter(param -> !param.indexed)
+        .toList();
+  }
+}
Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;\n\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;\nimport com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;\nimport com.fasterxml.jackson.databind.ObjectMapper;\nimport io.reactivex.disposables.Disposable;\nimport java.io.IOException;\nimport java.math.BigInteger;\nimport java.time.Instant;\nimport java.util.*;\nimport java.util.concurrent.BlockingQueue;\nimport java.util.concurrent.ExecutionException;\nimport java.util.concurrent.LinkedBlockingQueue;\nimport org.springframework.stereotype.Component;\nimport org.web3j.abi.EventValues;\nimport org.web3j.abi.TypeReference;\nimport org.web3j.abi.datatypes.Type;\nimport org.web3j.protocol.Web3j;\nimport org.web3j.protocol.core.DefaultBlockParameter;\nimport org.web3j.protocol.core.DefaultBlockParameterNumber;\nimport org.web3j.protocol.core.methods.request.EthFilter;\nimport org.web3j.protocol.core.methods.response.*;\nimport org.web3j.tx.Contract;\n\n@Component\npublic class EthEventLogDao {\n  private final LoggingService logger;\n  private final BcmonitoringConfigurationProperties properties;\n  private final Web3jConfig web3jConfig;\n  private final AbiParser abiParser;\n  private final ObjectMapper objectMapper;\n  private Disposable subscription;\n\n  /**\n   * Constructor for EthEventLogDao.\n   *\n   * @param log The logging service.\n   * @param properties The configuration properties.\n   * @param web3jConfig The Web3j configuration.\n   * @param abiParser The ABI parser.\n   * @param objectMapper The object mapper.\n   */\n  public EthEventLogDao(\n      LoggingService log,\n      BcmonitoringConfigurationProperties properties,\n      Web3jConfig web3jConfig,\n      AbiParser abiParser,\n      ObjectMapper objectMapper) {\n    this.logger = log;\n    this.properties = properties;\n    this.web3jConfig = web3jConfig;\n    this.abiParser = abiParser;\n    this.objectMapper = objectMapper;\n  }\n\n  /**\n   * Subscribes to all blocks and processes transactions.\n   *\n   * @return A BlockingQueue of Transaction objects.\n   */\n  public BlockingQueue<Transaction> subscribeAll() {\n    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);\n\n    // Check if the difference is valid\n    int allowableDiff;\n    try {\n      allowableDiff =\n          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());\n    } catch (NumberFormatException e) {\n      logger.error(\"Failed to parse allowable timestamp difference\", e);\n      return null;\n    }\n\n    try {\n      // Create a new Web3j instance for this subscription\n      Web3j web3j = web3jConfig.getWeb3j();\n      // Subscribe to new blocks\n      this.subscription =\n          web3j\n              .newHeadsNotifications()\n              .subscribe(\n                  newHeadsNotification -> {\n                    try {\n                      web3j\n                          .ethGetBlockByNumber(\n                              () -> newHeadsNotification.getParams().getResult().getNumber(), true)\n                          .sendAsync()\n                          .thenApply(EthBlock::getBlock)\n                          .thenAccept(\n                              block -> {\n                                BigInteger blockNumber = block.getNumber();\n\n                                // Check delay in block processing\n                                if (isDelayed(block, allowableDiff)) {\n                                  // todo: update log message to similar to go\n                                  logger.warn(\n                                      \"Block {} is delayed by more than {} seconds\",\n                                      blockNumber,\n                                      allowableDiff);\n                                }\n\n                                // Process block transactions and events\n                                List<Event> events = convBlock2EventEntities(block);\n                                if (!events.isEmpty()) {\n\n                                  BlockHeight blockHeight =\n                                      BlockHeight.builder()\n                                          .blockNumber(blockNumber.longValue())\n                                          .build();\n                                  Transaction transaction =\n                                      Transaction.builder()\n                                          .events(events)\n                                          .blockHeight(blockHeight)\n                                          .build();\n\n                                  try {\n                                    transactions.put(transaction);\n                                  } catch (InterruptedException e) {\n                                    throw new RuntimeException(e);\n                                  }\n                                }\n                              });\n                    } catch (Exception e) {\n                      logger.error(\"Error processing block\", e);\n                    }\n                  },\n                  error -> {\n                    logger.error(\"Subscription error\", error);\n                    unsubscribe();\n                    web3jConfig.shutdownWeb3j();\n                    transactions.put(\n                        Transaction.builder()\n                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())\n                            .build());\n                  },\n                  () -> logger.info(\"Subscription completed\"));\n      return transactions;\n    } catch (Exception e) {\n      logger.error(\"Failed to create Web3j subscription\", e);\n      throw e;\n    }\n  }\n\n  /**\n   * Checks if the block is delayed based on the allowable difference in seconds.\n   *\n   * @param block The block to check.\n   * @param allowableDiffSeconds The allowable difference in seconds.\n   * @return true if the block is delayed, false otherwise.\n   */\n  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {\n    long blockTimestamp = block.getTimestamp().longValue();\n    long currentTime = Instant.now().getEpochSecond();\n    long diff = currentTime - blockTimestamp;\n\n    return diff > allowableDiffSeconds;\n  }\n\n  /**\n   * Converts a block to a collection of event entities.\n   *\n   * @param block Ethereum block\n   * @return Collection of events found in the block\n   * @throws IOException If there is an error communicating with the Ethereum node\n   * @throws ExecutionException If there is an error executing the transaction\n   * @throws InterruptedException If the operation is interrupted\n   */\n  public List<Event> convBlock2EventEntities(EthBlock.Block block) {\n    List<Event> events = new ArrayList<>();\n\n    try {\n      // Create a new Web3j instance for this operation\n      Web3j web3j = web3jConfig.getWeb3j();\n\n      for (EthBlock.TransactionResult txResult : block.getTransactions()) {\n        try {\n          EthGetTransactionReceipt receiptResponse =\n              web3j.ethGetTransactionReceipt(txResult.get().toString()).send();\n\n          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);\n          if (receipt == null) {\n            continue;\n          }\n\n          for (Log log : receipt.getLogs()) {\n            try {\n              logger.info(\"Event found tx_hash={}\", log.getTransactionHash());\n              Event event =\n                  convertEthLogToEventEntity(log)\n                      .withBlockTimestamp(block.getTimestamp().longValue());\n              logger.info(\"Event parsed tx_hash={}, name={}\", event.transactionHash, event.name);\n\n              if (event.transactionHash != null && !event.transactionHash.isEmpty()) {\n                events.add(event);\n              }\n            } catch (Exception e) {\n              logger.error(\"Error processing log for transaction {}\", log.getTransactionHash());\n            }\n          }\n        } catch (Exception e) {\n          logger.error(\"Error processing transaction\", e);\n        }\n      }\n    } catch (Exception e) {\n      logger.error(\"Error creating Web3j instance\", e);\n    }\n\n    return events;\n  }\n\n  /**\n   * Converts an Ethereum log to an Event entity.\n   *\n   * @param ethLog The Ethereum log to convert\n   * @return Converted Event entity\n   * @throws Exception If conversion fails\n   */\n  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {\n    try {\n      // Get ABI event definition for the log\n      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);\n      // todo: never return null\n      if (abiEvent == null) {\n        logger.info(\"Event definition not found in ABI\");\n        throw new Exception(\"Event definition not found in ABI\");\n      }\n\n      // Extract event parameters using Web3j's utilities\n      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);\n      if (eventValues == null) {\n        logger.info(\"No event values found for log: {}\", ethLog);\n        throw new Exception(\"No event values found for log\");\n      }\n\n      // Process indexed parameters\n      Map<String, Object> indexedValues = new HashMap<>();\n      List<Type> indexedParameters = eventValues.getIndexedValues();\n      List<TypeReference<Type>> indexedReferences = abiEvent.getIndexedParameters();\n\n      for (int i = 0; i < indexedParameters.size(); i++) {\n        // wrong name\n        String name = indexedReferences.get(i).getType().getTypeName();\n        Object value = indexedParameters.get(i).getValue();\n        indexedValues.put(name, value);\n      }\n      String indexedJson = objectMapper.writeValueAsString(indexedValues);\n\n      // Process non-indexed parameters\n      Map<String, Object> nonIndexedValues = new HashMap<>();\n      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();\n      List<TypeReference<Type>> nonIndexedReferences = abiEvent.getNonIndexedParameters();\n\n      for (int i = 0; i < nonIndexedParameters.size(); i++) {\n        // wrong name\n        String name = nonIndexedReferences.get(i).getType().getTypeName();\n        Object value = nonIndexedParameters.get(i).getValue();\n        nonIndexedValues.put(name, value);\n      }\n      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);\n\n      // Serialize log to JSON\n      String logJson = objectMapper.writeValueAsString(ethLog);\n\n      // Create and return new Event entity\n      return Event.builder()\n          .name(abiEvent.getName())\n          .transactionHash(ethLog.getTransactionHash())\n          .logIndex((int) ethLog.getLogIndex().longValue())\n          .indexedValues(indexedJson)\n          .nonIndexedValues(nonIndexedJson)\n          .log(logJson)\n          .build();\n    } catch (Exception e) {\n      logger.error(\"Error converting log to event entity\", e);\n      return null;\n    }\n  }\n\n  /**\n   * Retrieves the block timestamp for a given block number.\n   *\n   * @param blockNumber The block number to retrieve the timestamp for.\n   * @return The block timestamp in seconds since epoch.\n   * @throws IOException If there is an error communicating with the Ethereum node\n   */\n  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {\n    // Create a new Web3j instance for this operation\n    Web3j web3j = web3jConfig.getWeb3j();\n\n    try {\n      return web3j\n          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)\n          .send()\n          .getBlock()\n          .getTimestamp()\n          .longValue();\n    } finally {\n      // Shutdown the Web3j instance to free resources\n      web3j.shutdown();\n    }\n  }\n\n  /**\n   * Get filtered logs from a specific block height\n   *\n   * @param blockHeight Block number to start from\n   * @return Queue of transactions containing events\n   */\n  public List<Transaction> getPendingTransactions(long blockHeight) {\n    return getPendingTransactions(blockHeight, false);\n  }\n\n  /**\n   * Get filtered logs from a specific block height with an option to force an error in the outer\n   * catch block This method is primarily used for testing the error handling in the outer catch\n   * block\n   *\n   * @param blockHeight Block number to start from\n   * @param forceOuterError Whether to force an error in the outer catch block (for testing)\n   * @return Queue of transactions containing events\n   */\n  public List<Transaction> getPendingTransactions(long blockHeight, boolean forceOuterError) {\n    try {\n      // Create a new Web3j instance for this operation\n      Web3j web3j = web3jConfig.getWeb3j();\n\n      // Create filter to get logs from the specified block height\n      EthFilter filter =\n          new EthFilter(\n              DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),\n              DefaultBlockParameter.valueOf(\"latest\"),\n              Collections.emptyList());\n\n      // Get logs synchronously\n      List<EthLog.LogResult> filterLogs = web3j.ethGetLogs(filter).send().getLogs();\n\n      logger.info(\"Retrieved {} logs from block height {}\", filterLogs.size(), blockHeight);\n\n      // Collect block numbers\n      List<BigInteger> blockNumbers =\n          filterLogs.stream().map(result -> (Log) result.get()).map(Log::getBlockNumber).toList();\n\n      // Fetch timestamps per block\n      Map<BigInteger, BigInteger> blockTimestamps = new HashMap<>();\n      for (BigInteger blockNumber : blockNumbers) {\n        EthBlock block =\n            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();\n        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());\n      }\n\n      if (forceOuterError) {\n        throw new RuntimeException(\"Forced error in outer catch block for testing\");\n      }\n\n      return filterLogs.stream()\n          .map(\n              logResult -> {\n                try {\n                  Log ethLog = (Log) logResult.get();\n                  logger.info(\"Event found tx_hash={}\", ethLog.getTransactionHash());\n\n                  Event event =\n                      convertEthLogToEventEntity(ethLog)\n                          .withBlockTimestamp(\n                              blockTimestamps.get(ethLog.getBlockNumber()).longValue());\n                  logger.info(\n                      \"Event parsed tx_hash={}, name={}\", event.transactionHash, event.name);\n\n                  BlockHeight height =\n                      BlockHeight.builder()\n                          .blockNumber(ethLog.getBlockNumber().longValue())\n                          .build();\n\n                  return Transaction.builder()\n                      .events(Collections.singletonList(event))\n                      .blockHeight(height)\n                      .build();\n                } catch (Exception e) {\n                  logger.error(\"Error processing individual log\", e);\n                  return null;\n                }\n              })\n          .filter(Objects::nonNull)\n          .toList();\n\n    } catch (Exception e) {\n      logger.error(\"Error getting filtered logs\", e);\n      throw new RuntimeException(\"Error getting filtered logs\", e);\n    }\n  }\n\n  public void unsubscribe() {\n    if (subscription != null) {\n      subscription.dispose();\n    }\n  }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java
--- a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java	(revision f36bd7a8ccb625739ada0e018f7c06d21874ba7d)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java	(date 1750839488397)
@@ -5,6 +5,8 @@
 import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
+import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.EventDefinition;
+import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParameterInfo;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
 import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
 import com.fasterxml.jackson.databind.ObjectMapper;
@@ -222,14 +224,15 @@
    */
   public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
     try {
-      // Get ABI event definition for the log
-      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
-      // todo: never return null
-      if (abiEvent == null) {
+      // Get enhanced event definition with parameter names
+      EventDefinition eventDefinition = abiParser.getEventDefinitionByLog(ethLog);
+      if (eventDefinition == null) {
         logger.info("Event definition not found in ABI");
         throw new Exception("Event definition not found in ABI");
       }
 
+      org.web3j.abi.datatypes.Event abiEvent = eventDefinition.web3jEvent;
+
       // Extract event parameters using Web3j's utilities
       EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
       if (eventValues == null) {
@@ -237,27 +240,25 @@
         throw new Exception("No event values found for log");
       }
 
-      // Process indexed parameters
+      // Process indexed parameters with correct names
       Map<String, Object> indexedValues = new HashMap<>();
       List<Type> indexedParameters = eventValues.getIndexedValues();
-      List<TypeReference<Type>> indexedReferences = abiEvent.getIndexedParameters();
+      List<ParameterInfo> indexedParameterInfos = eventDefinition.getIndexedParameters();
 
-      for (int i = 0; i < indexedParameters.size(); i++) {
-        // wrong name
-        String name = indexedReferences.get(i).getType().getTypeName();
+      for (int i = 0; i < indexedParameters.size() && i < indexedParameterInfos.size(); i++) {
+        String name = indexedParameterInfos.get(i).name;
         Object value = indexedParameters.get(i).getValue();
         indexedValues.put(name, value);
       }
       String indexedJson = objectMapper.writeValueAsString(indexedValues);
 
-      // Process non-indexed parameters
+      // Process non-indexed parameters with correct names
       Map<String, Object> nonIndexedValues = new HashMap<>();
       List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();
-      List<TypeReference<Type>> nonIndexedReferences = abiEvent.getNonIndexedParameters();
+      List<ParameterInfo> nonIndexedParameterInfos = eventDefinition.getNonIndexedParameters();
 
-      for (int i = 0; i < nonIndexedParameters.size(); i++) {
-        // wrong name
-        String name = nonIndexedReferences.get(i).getType().getTypeName();
+      for (int i = 0; i < nonIndexedParameters.size() && i < nonIndexedParameterInfos.size(); i++) {
+        String name = nonIndexedParameterInfos.get(i).name;
         Object value = nonIndexedParameters.get(i).getValue();
         nonIndexedValues.put(name, value);
       }
Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ContractEvents.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;\n\nimport java.util.Map;\nimport lombok.Builder;\nimport org.web3j.abi.datatypes.Event;\n\n@Builder\npublic class ContractEvents {\n  public final String contractName;\n  public final Map<String, Event> events;\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ContractEvents.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ContractEvents.java
--- a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ContractEvents.java	(revision f36bd7a8ccb625739ada0e018f7c06d21874ba7d)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/ContractEvents.java	(date 1750839348844)
@@ -8,4 +8,5 @@
 public class ContractEvents {
   public final String contractName;
   public final Map<String, Event> events;
+  public final Map<String, EventDefinition> eventDefinitions;
 }
Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;\n\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;\nimport com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo;\nimport com.fasterxml.jackson.databind.JsonNode;\nimport com.fasterxml.jackson.databind.ObjectMapper;\nimport java.io.IOException;\nimport java.io.InputStream;\nimport java.util.ArrayList;\nimport java.util.Collections;\nimport java.util.Date;\nimport java.util.HashMap;\nimport java.util.Iterator;\nimport java.util.List;\nimport java.util.Map;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\nimport org.springframework.stereotype.Component;\nimport org.web3j.abi.EventEncoder;\nimport org.web3j.abi.TypeReference;\nimport org.web3j.abi.datatypes.Event;\nimport org.web3j.protocol.core.methods.response.Log;\n\n@Component\npublic class AbiParser {\n  private static final Logger log = LoggerFactory.getLogger(AbiParser.class);\n  private final ObjectMapper mapper = new ObjectMapper();\n  public static Map<String, ContractEvents> contractEventStore = new HashMap<>();\n  public static List<String> contractAddresses = new ArrayList<>();\n\n  private final BcmonitoringConfigurationProperties properties;\n\n  public AbiParser(BcmonitoringConfigurationProperties properties) {\n    this.properties = properties;\n  }\n\n  /**\n   * Parse ABI content from JSON string\n   *\n   * @param abiContent ABI content in JSON format\n   * @throws IOException If parsing fails\n   */\n  public Map<String, Event> parseAbi(String abiContent) throws IOException {\n    Map<String, Event> stringEventMap = new HashMap<>();\n    if (abiContent == null || abiContent.isEmpty()) {\n      log.warn(\"Empty ABI content provided\");\n      return stringEventMap;\n    }\n\n    try {\n      JsonNode abiJson = mapper.readTree(abiContent);\n\n      for (JsonNode node : abiJson) {\n        if (\"event\".equals(node.get(\"type\").asText())) {\n          JsonNode nameNode = node.get(\"name\");\n          if (nameNode == null || nameNode.asText().isEmpty()) {\n            // Skip events with missing or empty names\n            continue;\n          }\n\n          String eventName = nameNode.asText();\n          List<TypeReference<?>> parameters = new ArrayList<>();\n\n          // Parse inputs\n          JsonNode inputs = node.get(\"inputs\");\n          for (JsonNode input : inputs) {\n            String type = input.get(\"type\").asText();\n            boolean indexed = input.has(\"indexed\") && input.get(\"indexed\").asBoolean();\n\n            TypeReference<?> typeReference = createTypeReference(type, indexed);\n            parameters.add(typeReference);\n          }\n\n          // Create event (Web3j expects 2 arguments)\n          Event event = new Event(eventName, parameters);\n\n          // Create signature for the event\n          String signature = EventEncoder.encode(event);\n          log.debug(\"Event: {}, Parameters: {}, Signature: {}\", eventName, parameters, signature);\n\n          // Store event in contractEventStore\n          stringEventMap.put(signature, event);\n\n          log.debug(\"Parsed event: {} with signature: {}\", eventName, signature);\n        }\n      }\n\n      log.info(\"Successfully parsed ABI with {} events\", stringEventMap.size());\n    } catch (Exception e) {\n      log.error(\"Failed to parse ABI content\", e);\n      throw new IOException(\"ABI parsing failed: \" + e.getMessage(), e);\n    }\n    return stringEventMap;\n  }\n\n  // todo: add convert type\n  /** Create appropriate TypeReference based on the Solidity type */\n  private TypeReference<?> createTypeReference(String solidityType, boolean indexed) {\n    if (solidityType.startsWith(DCFConst.UINT)) {\n      return new TypeReference<org.web3j.abi.datatypes.generated.Uint256>(indexed) {};\n    } else if (solidityType.equals(DCFConst.ADDRESS)) {\n      return new TypeReference<org.web3j.abi.datatypes.Address>(indexed) {};\n    } else if (solidityType.equals(DCFConst.STRING)) {\n      return new TypeReference<org.web3j.abi.datatypes.Utf8String>(indexed) {};\n    } else if (solidityType.equals(DCFConst.BOOL)) {\n      return new TypeReference<org.web3j.abi.datatypes.Bool>(indexed) {};\n    } else if (solidityType.equals(DCFConst.BYTES_32)) {\n      return new TypeReference<org.web3j.abi.datatypes.generated.Bytes32>(indexed) {};\n    } else if (solidityType.startsWith(DCFConst.BYTES)) {\n      return new TypeReference<org.web3j.abi.datatypes.DynamicBytes>(indexed) {};\n    } else {\n      // Default fallback\n      return new TypeReference<>(indexed) {};\n    }\n  }\n\n  /**\n   * Parse ABI content from an input stream and register contracts/events\n   *\n   * @param inputStream The input stream containing ABI JSON\n   * @param objectKey The S3 object key\n   * @param lastModified Last modified timestamp\n   * @return Contract information including address and name\n   * @throws IOException If parsing fails\n   */\n  public ContractInfo parseAbiContent(InputStream inputStream, String objectKey, Date lastModified)\n      throws IOException {\n    try {\n      byte[] abiJson = inputStream.readAllBytes();\n\n      // Extract contract name from object key\n      String[] pathParts = objectKey.split(\"/\");\n      String contractName = pathParts[1].replace(\".json\", \"\");\n\n      // Extract address from JSON based on ABI format\n      String abiFormat = properties.getAbiFormat();\n      String address;\n\n      JsonNode rootNode = mapper.readTree(abiJson);\n      if (\"truffle\".equals(abiFormat)) {\n        // For Truffle format, find address in networks section\n        JsonNode networksNode = rootNode.path(\"networks\");\n        address = findFirstAddressInNetworks(networksNode);\n      } else {\n        // For other formats (like Hardhat), get address directly\n        address = rootNode.path(\"address\").asText();\n      }\n\n      address = address.toLowerCase();\n\n      // Parse ABI section\n      JsonNode abiNode = rootNode.path(\"abi\");\n      if (abiNode.isMissingNode()) {\n        String errorMessage = \"ABI section not found in JSON\";\n        log.error(errorMessage);\n        throw new IOException(errorMessage);\n      }\n\n      // append the contract address\n      appendContractAddress(address);\n\n      // parse and register events\n      parseAndRegisterEvents(address, contractName, abiNode.toString());\n\n      log.info(\n          \"ABI file processed: address={}, contract_name={}, last_modified={}, events={}\",\n          address,\n          contractName,\n          lastModified,\n          contractEventStore.size());\n\n      return ContractInfo.builder()\n          .address(address)\n          .name(contractName)\n          .lastModified(lastModified)\n          .build();\n    } finally {\n      inputStream.close();\n    }\n  }\n\n  /**\n   * Find the first address in the networks section of the ABI JSON\n   *\n   * @param networksNode The networks node from the ABI JSON\n   * @return The first address found, or an empty string if none found\n   */\n  private String findFirstAddressInNetworks(JsonNode networksNode) {\n    if (networksNode.isObject()) {\n      Iterator<JsonNode> elements = networksNode.elements();\n      while (elements.hasNext()) {\n        JsonNode network = elements.next();\n        if (network.has(DCFConst.ADDRESS)) {\n          return network.get(DCFConst.ADDRESS).asText();\n        }\n      }\n    }\n    return \"\";\n  }\n\n  /**\n   * Adds a contract address to the list of monitored addresses\n   *\n   * @param address The contract address to add\n   */\n  public void appendContractAddress(String address) {\n    if (!contractAddresses.contains(address)) {\n      contractAddresses.add(address);\n      log.info(\"Added contract address: {}\", address);\n    }\n  }\n\n  /**\n   * Parse ABI JSON and register events for a contract\n   *\n   * @param address Contract address\n   * @param contractName Contract name\n   * @param abiJson ABI JSON string\n   * @throws IOException If parsing fails\n   */\n  public void parseAndRegisterEvents(String address, String contractName, String abiJson)\n      throws IOException {\n    contractEventStore.put(\n        address,\n        ContractEvents.builder().contractName(contractName).events(parseAbi(abiJson)).build());\n    log.info(\"Registered events for contract: {} at address: {}\", contractName, address);\n  }\n\n  /**\n   * Retrieves the ABI event definition for a given log.\n   *\n   * @param log The Ethereum log to process\n   * @return The ABI event definition for the log\n   * @throws Exception If no matching event is found\n   */\n  public org.web3j.abi.datatypes.Event getABIEventByLog(Log log) throws Exception {\n    String eventId = log.getTopics().get(0).toString().toLowerCase();\n    String logAddress = log.getAddress().toString().toLowerCase();\n\n    Map<String, org.web3j.abi.datatypes.Event> events =\n        contractEventStore.containsKey(logAddress)\n            ? contractEventStore.get(logAddress).events\n            : Collections.emptyMap();\n    if (events.containsKey(eventId)) {\n      return events.get(eventId);\n    }\n\n    // Todo: handle exceptions the exception class(NotFoundException) should be created\n    throw new Exception(\"Event definition not found in ABI\");\n  }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java
--- a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java	(revision f36bd7a8ccb625739ada0e018f7c06d21874ba7d)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java	(date 1750839433367)
@@ -4,6 +4,8 @@
 import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo;
+import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.EventDefinition;
+import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParameterInfo;
 import com.fasterxml.jackson.databind.JsonNode;
 import com.fasterxml.jackson.databind.ObjectMapper;
 import java.io.IOException;
@@ -95,6 +97,81 @@
     return stringEventMap;
   }
 
+  /**
+   * Parse ABI content from JSON string with parameter names preserved
+   *
+   * @param abiContent ABI content in JSON format
+   * @throws IOException If parsing fails
+   */
+  public Map<String, EventDefinition> parseAbiWithParameterNames(String abiContent) throws IOException {
+    Map<String, EventDefinition> eventDefinitionMap = new HashMap<>();
+    if (abiContent == null || abiContent.isEmpty()) {
+      log.warn("Empty ABI content provided");
+      return eventDefinitionMap;
+    }
+
+    try {
+      JsonNode abiJson = mapper.readTree(abiContent);
+
+      for (JsonNode node : abiJson) {
+        if ("event".equals(node.get("type").asText())) {
+          JsonNode nameNode = node.get("name");
+          if (nameNode == null || nameNode.asText().isEmpty()) {
+            // Skip events with missing or empty names
+            continue;
+          }
+
+          String eventName = nameNode.asText();
+          List<TypeReference<?>> web3jParameters = new ArrayList<>();
+          List<ParameterInfo> parameterInfos = new ArrayList<>();
+
+          // Parse inputs with parameter names
+          JsonNode inputs = node.get("inputs");
+          for (JsonNode input : inputs) {
+            String type = input.get("type").asText();
+            String paramName = input.has("name") ? input.get("name").asText() : "param" + parameterInfos.size();
+            boolean indexed = input.has("indexed") && input.get("indexed").asBoolean();
+
+            TypeReference<?> typeReference = createTypeReference(type, indexed);
+            web3jParameters.add(typeReference);
+
+            ParameterInfo paramInfo = ParameterInfo.builder()
+                .name(paramName)
+                .typeReference(typeReference)
+                .indexed(indexed)
+                .build();
+            parameterInfos.add(paramInfo);
+          }
+
+          // Create Web3j event
+          Event web3jEvent = new Event(eventName, web3jParameters);
+
+          // Create enhanced event definition
+          EventDefinition eventDefinition = EventDefinition.builder()
+              .name(eventName)
+              .web3jEvent(web3jEvent)
+              .parameters(parameterInfos)
+              .build();
+
+          // Create signature for the event
+          String signature = EventEncoder.encode(web3jEvent);
+          log.debug("Event: {}, Parameters: {}, Signature: {}", eventName, parameterInfos, signature);
+
+          // Store event definition
+          eventDefinitionMap.put(signature, eventDefinition);
+
+          log.debug("Parsed event with parameter names: {} with signature: {}", eventName, signature);
+        }
+      }
+
+      log.info("Successfully parsed ABI with {} events including parameter names", eventDefinitionMap.size());
+    } catch (Exception e) {
+      log.error("Failed to parse ABI content with parameter names", e);
+      throw new IOException("ABI parsing with parameter names failed: " + e.getMessage(), e);
+    }
+    return eventDefinitionMap;
+  }
+
   // todo: add convert type
   /** Create appropriate TypeReference based on the Solidity type */
   private TypeReference<?> createTypeReference(String solidityType, boolean indexed) {
@@ -222,9 +299,16 @@
    */
   public void parseAndRegisterEvents(String address, String contractName, String abiJson)
       throws IOException {
+    Map<String, Event> events = parseAbi(abiJson);
+    Map<String, EventDefinition> eventDefinitions = parseAbiWithParameterNames(abiJson);
+
     contractEventStore.put(
         address,
-        ContractEvents.builder().contractName(contractName).events(parseAbi(abiJson)).build());
+        ContractEvents.builder()
+            .contractName(contractName)
+            .events(events)
+            .eventDefinitions(eventDefinitions)
+            .build());
     log.info("Registered events for contract: {} at address: {}", contractName, address);
   }
 
@@ -247,7 +331,28 @@
       return events.get(eventId);
     }
 
-    // Todo: handle exceptions the exception class(NotFoundException) should be created
-    throw new Exception("Event definition not found in ABI");
+    throw new Exception("Event not found for log: " + eventId + " at address: " + logAddress);
+  }
+
+  /**
+   * Retrieves the enhanced event definition with parameter names for a given log.
+   *
+   * @param log The Ethereum log to process
+   * @return The enhanced event definition for the log
+   * @throws Exception If no matching event is found
+   */
+  public EventDefinition getEventDefinitionByLog(Log log) throws Exception {
+    String eventId = log.getTopics().get(0).toString().toLowerCase();
+    String logAddress = log.getAddress().toString().toLowerCase();
+
+    Map<String, EventDefinition> eventDefinitions =
+        contractEventStore.containsKey(logAddress)
+            ? contractEventStore.get(logAddress).eventDefinitions
+            : Collections.emptyMap();
+    if (eventDefinitions.containsKey(eventId)) {
+      return eventDefinitions.get(eventId);
+    }
+
+    throw new Exception("Event definition not found for log: " + eventId + " at address: " + logAddress);
   }
 }
Index: src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.decurret_dcp.dcjpy.bcmonitoring.application.event;\n\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository;\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository;\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao;\nimport com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository;\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.ContextConfig;\nimport com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;\nimport com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;\nimport com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;\nimport com.decurret_dcp.dcjpy.bcmonitoring.logging.StructuredLogContext;\nimport com.fasterxml.jackson.core.JsonProcessingException;\nimport com.fasterxml.jackson.databind.ObjectMapper;\nimport java.util.List;\nimport java.util.Objects;\nimport java.util.concurrent.*;\nimport java.util.concurrent.atomic.AtomicBoolean;\nimport org.springframework.stereotype.Service;\n\n@Service\npublic class MonitorEventService {\n  private final LoggingService log;\n  private final EventLogRepository eventLogRepository;\n  private final EventRepository eventRepository;\n  private final BlockHeightRepository blockHeightRepository;\n  private final ObjectMapper objectMapper;\n  private final BcmonitoringConfigurationProperties properties;\n  private final AtomicBoolean running = new AtomicBoolean(true);\n  private final Web3jConfig web3jConfig;\n  private final EthEventLogDao ethEventLogDao;\n\n  public MonitorEventService(\n      LoggingService logger,\n      EventLogRepository eventLogRepository,\n      EventRepository eventRepository,\n      BlockHeightRepository blockHeightRepository,\n      BcmonitoringConfigurationProperties properties,\n      Web3jConfig web3jConfig,\n      EthEventLogDao ethEventLogDao) {\n    this.log = logger;\n    this.eventLogRepository = eventLogRepository;\n    this.eventRepository = eventRepository;\n    this.blockHeightRepository = blockHeightRepository;\n    this.properties = properties;\n    this.web3jConfig = web3jConfig;\n    this.objectMapper = new ObjectMapper();\n    this.ethEventLogDao = ethEventLogDao;\n  }\n\n  /**\n   * Execute the monitoring process This method will run in a loop, checking for new events and\n   * processing them.\n   *\n   * @throws NumberFormatException if the check interval is not a valid integer\n   * @throw Exception if there is an error during the monitoring process\n   */\n  public void execute() throws Exception {\n    int checkInterval;\n    try {\n      checkInterval = Integer.parseInt(properties.getSubscription().getCheckInterval());\n    } catch (NumberFormatException e) {\n      log.error(\"Failed to convert checkInterval: {}\", e.getMessage());\n      throw e;\n    }\n    try {\n      monitorEvents();\n    } catch (Exception e) {\n      log.error(\"Error in monitoring loop: {}\", e.getMessage(), e);\n      sleep(checkInterval);\n      throw e;\n    }\n  }\n\n  /**\n   * Monitor events from the blockchain and process them. This method will subscribe to new events\n   * and process pending transactions.\n   *\n   * @throw Exception if there is an error during the monitoring process\n   */\n  private void monitorEvents() throws Exception {\n    // Get current block height\n    long blockNumber;\n    try {\n      blockNumber = blockHeightRepository.get();\n      log.info(\"Get blockheight: {}\", blockNumber);\n    } catch (Exception e) {\n      log.error(\"Failed to get blockheight: {}\", e.getMessage());\n      throw e;\n    }\n\n    try {\n      BlockingQueue<Transaction> transactionsQueue = eventLogRepository.subscribe();\n      List<Transaction> pendingTransactionsQueue =\n          eventLogRepository.getFilterLogs(blockNumber + 1);\n\n      processPendingTransactions(pendingTransactionsQueue);\n      processNewTransactions(transactionsQueue);\n    } catch (Exception e) {\n      log.error(\"Error in monitoring: {}\", e.getMessage());\n      ethEventLogDao.unsubscribe();\n      throw e;\n    }\n  }\n\n  /**\n   * Process pending transactions from the queue. This method will save the block height and events\n   * to the database.\n   *\n   * @param pendingQueue BlockingQueue of pending transactions\n   */\n  private void processPendingTransactions(List<Transaction> pendingQueue) throws Exception {\n    BlockHeight exBlockHeight = BlockHeight.builder().blockNumber(0).build();\n\n    for (Transaction tx : pendingQueue) {\n      try {\n        // Process block height change\n        if (exBlockHeight.blockNumber != 0\n            && exBlockHeight.blockNumber != tx.blockHeight.blockNumber) {\n          if (!savePendingTransactionBlockNumber(exBlockHeight)) {\n            throw new Exception(\"Failed to save block height\");\n          }\n        }\n\n        if (tx.blockHeight.blockNumber == 0) {\n          throw new RuntimeException(\"Block height Number is zero\");\n        } else if (!savePendingTransaction(tx)) {\n          throw new Exception(\"Failed to save transaction\");\n        }\n\n        exBlockHeight = tx.blockHeight;\n      } catch (Exception e) {\n        log.error(\"Error while processing pending transactions: {}\", e.getMessage());\n        throw e;\n      }\n    }\n    log.info(\"Success to process pending transactions\");\n  }\n\n  /**\n   * Process new transactions from the queue. This method will save the events to the database.\n   *\n   * @param transactionsQueue BlockingQueue of new transactions\n   */\n  private void processNewTransactions(BlockingQueue<Transaction> transactionsQueue)\n      throws Exception {\n    while (ContextConfig.isServiceRunning()) {\n      try {\n        Transaction tx = transactionsQueue.poll(5, TimeUnit.SECONDS);\n        if (Objects.isNull(tx)) continue;\n        if (tx.blockHeight.blockNumber == -1) {\n          throw new Exception(\"Websocket is disconnected\");\n        }\n        if (tx.blockHeight.blockNumber == 0) {\n          throw new Exception(\"Block height Number is zero\");\n        }\n\n        if (!saveTransaction(tx)) {\n          throw new Exception(\"Failed to save transaction\");\n        }\n\n      } catch (Exception e) {\n        log.error(\"Error while processing new transactions: \", e.getMessage());\n        throw e;\n      }\n      log.info(\"Success to process new transactions\");\n    }\n  }\n\n  /**\n   * Save transaction to the database.\n   *\n   * @param tx Transaction object containing the events and block height\n   * @return true if the transaction was saved successfully, false otherwise\n   */\n  private boolean saveTransaction(Transaction tx) {\n    // Process all events in the transaction\n    for (Event e : tx.events) {\n      if (e.transactionHash.isEmpty()) {\n        log.error(\"Event transaction hash is zero\");\n        return false;\n      }\n\n      String traceId = fetchTraceId(e.nonIndexedValues);\n      try (var logContext =\n          StructuredLogContext.forBlockchainEvent(\n              e.name,\n              e.transactionHash,\n              tx.blockHeight.blockNumber,\n              e.logIndex,\n              e.blockTimestamp,\n              traceId)) {\n\n        if (!eventRepository.save(e)) {\n          log.error(\"Failure to register event\");\n          return false;\n        }\n        log.info(\"Success to register event\");\n      }\n    }\n\n    if (!blockHeightRepository.save(tx.blockHeight)) {\n      log.error(\"Failure to register block number\");\n      return false;\n    }\n    log.info(\"Success to register block number\");\n    return true;\n  }\n\n  /**\n   * Save pending transaction to the database.\n   *\n   * @param tx Transaction object containing the events and block height\n   * @return true if the transaction was saved successfully, false otherwise\n   */\n  private boolean savePendingTransaction(Transaction tx) {\n    for (Event e : tx.events) {\n      if (e.transactionHash.isEmpty()) {\n        log.error(\"Event transaction hash is zero\");\n        return false;\n      }\n\n      String traceId = fetchTraceId(e.nonIndexedValues);\n      try (var logContext =\n          StructuredLogContext.forBlockchainEvent(\n              e.name,\n              e.transactionHash,\n              tx.blockHeight.blockNumber,\n              e.logIndex,\n              e.blockTimestamp,\n              traceId)) {\n\n        if (!eventRepository.save(e)) {\n          log.error(\"Failure to register event\");\n          return false;\n        }\n        log.info(\"Success to register event\");\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Save pending transaction block number to the database.\n   *\n   * @param blockHeight BlockHeight object containing the block number\n   * @return true if the block height was saved successfully, false otherwise\n   */\n  private boolean savePendingTransactionBlockNumber(BlockHeight blockHeight) {\n    if (!blockHeightRepository.save(blockHeight)) {\n      log.error(\"Failure to register block number: {}\", blockHeight.blockNumber);\n      return false;\n    }\n    log.info(\"Success to register block number: {}\", blockHeight.blockNumber);\n    return true;\n  }\n\n  private static class ParsedTraceId {\n    public byte[] traceId;\n  }\n\n  /**\n   * Fetch trace ID from non-indexed values.\n   *\n   * @param nonIndexedValues Non-indexed values as a JSON string\n   * @return Trace ID as a string\n   */\n  private String fetchTraceId(String nonIndexedValues) {\n    try {\n      ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);\n      if (parsed.traceId == null || parsed.traceId.length == 0) {\n        return \"\";\n      }\n\n      StringBuilder sb = new StringBuilder();\n      for (byte b : parsed.traceId) {\n        if (b != 0) {\n          sb.append((char) b);\n        }\n      }\n      return sb.toString();\n    } catch (JsonProcessingException e) {\n      log.error(\"Error parsing trace ID: {}\", e.getMessage());\n      return \"\";\n    }\n  }\n\n  /**\n   * Sleep for a specified number of milliseconds.\n   *\n   * @param milliseconds Number of milliseconds to sleep\n   */\n  private void sleep(int milliseconds) {\n    try {\n      Thread.sleep(milliseconds);\n    } catch (InterruptedException e) {\n      Thread.currentThread().interrupt();\n    }\n  }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java
--- a/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java	(revision f36bd7a8ccb625739ada0e018f7c06d21874ba7d)
+++ b/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java	(date 1750839533400)
@@ -9,6 +9,7 @@
 import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
+import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParsedTraceId;
 import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
 import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
 import com.decurret_dcp.dcjpy.bcmonitoring.logging.StructuredLogContext;
@@ -257,10 +258,6 @@
     return true;
   }
 
-  private static class ParsedTraceId {
-    public byte[] traceId;
-  }
-
   /**
    * Fetch trace ID from non-indexed values.
    *
@@ -270,12 +267,12 @@
   private String fetchTraceId(String nonIndexedValues) {
     try {
       ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);
-      if (parsed.traceId == null || parsed.traceId.length == 0) {
+      if (parsed.getTraceId() == null || parsed.getTraceId().length == 0) {
         return "";
       }
 
       StringBuilder sb = new StringBuilder();
-      for (byte b : parsed.traceId) {
+      for (byte b : parsed.getTraceId()) {
         if (b != 0) {
           sb.append((char) b);
         }
