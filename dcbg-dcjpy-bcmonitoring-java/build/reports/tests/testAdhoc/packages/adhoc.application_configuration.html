<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package adhoc.application_configuration</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package adhoc.application_configuration</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; adhoc.application_configuration</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">33.703s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/adhoc.application_configuration.ApplicationConfigurationDefaultITSpec.html">ApplicationConfigurationDefaultITSpec</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>10.403s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/adhoc.application_configuration.ApplicationConfigurationITSpec.html">ApplicationConfigurationITSpec</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>22.834s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/adhoc.application_configuration.ApplicationConfigurationProdITSpec.html">ApplicationConfigurationProdITSpec</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.466s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:58:20</p>
</div>
</div>
</body>
</html>
