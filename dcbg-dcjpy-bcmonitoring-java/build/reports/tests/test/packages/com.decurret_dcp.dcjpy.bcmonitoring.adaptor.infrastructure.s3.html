<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">216</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.425s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiEventInputSpec.html">AbiEventInputSpec</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParserContentSpec.html">AbiParserContentSpec</a>
</td>
<td>43</td>
<td>0</td>
<td>0</td>
<td>0.261s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverterSpec.html">AbiTypeConverterSpec</a>
</td>
<td>123</td>
<td>0</td>
<td>0</td>
<td>0.036s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.ParameterizedTypeImplSpec.html">ParameterizedTypeImplSpec</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptorSpec.html">S3ClientAdaptorSpec</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.085s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.StructGeneratorSpec.html">StructGeneratorSpec</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>0.021s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
