Index: src/test/groovy/adhoc/json/ErrorJson.json
===================================================================
diff --git a/src/test/groovy/adhoc/json/ErrorJson.json b/src/test/groovy/adhoc/json/ErrorJson.json
deleted file mode 100644
--- a/src/test/groovy/adhoc/json/ErrorJson.json	(revision 49818eea9f2cd2a63e8336e1f8ae2af0741cb8b1)
+++ /dev/null	(revision 49818eea9f2cd2a63e8336e1f8ae2af0741cb8b1)
@@ -1,13 +0,0 @@
-{
-  "contractName": "AccountManager",
-  "abi": [
-    {
-      "type": "event",
-      "anonymous": false,
-      "name": "AccountEnabled",
-      "inputs": [
-        {
-          "type": "bytes32",
-          "name": "accountId",
-          "indexed": true
-        }
\ No newline at end of file
Index: src/test/groovy/adhoc/json/ErrorABI.json
===================================================================
diff --git a/src/test/groovy/adhoc/json/ErrorABI.json b/src/test/groovy/adhoc/json/ErrorABI.json
deleted file mode 100644
--- a/src/test/groovy/adhoc/json/ErrorABI.json	(revision 49818eea9f2cd2a63e8336e1f8ae2af0741cb8b1)
+++ /dev/null	(revision 49818eea9f2cd2a63e8336e1f8ae2af0741cb8b1)
@@ -1,16 +0,0 @@
-{
-  "abc": [
-    {
-      "type": "event",
-      "anonymous": false,
-      "name": "AccountEnabled",
-      "inputs": [
-        {
-          "type": "bytes32",
-          "name": "accountId",
-          "indexed": true
-        }
-      ]
-    }
-  ]
-}
diff --git a/src/test/groovy/com/decurret_dcp/dcjpy/bcmonitoring/config/BcmonitoringConfigurationPropertiesTest.groovy b/src/test/groovy/com/decurret_dcp/dcjpy/bcmonitoring/config/BcmonitoringConfigurationPropertiesTest.groovy
deleted file mode 100644
