<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" tests="13" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:29.452Z" hostname="tungvt.local" time="0.061">
  <properties/>
  <testcase name="should successfully execute operation with connection" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.021"/>
  <testcase name="should handle InterruptedException during connection acquisition" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.007"/>
  <testcase name="should handle DynamoDbException during operation execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should handle generic Exception during operation execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.002"/>
  <testcase name="should release connection even when operation throws exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should not release connection when acquisition fails" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should handle null client gracefully in finally block" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.002"/>
  <testcase name="should execute operation with proper return type" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should execute operation that returns null" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.002"/>
  <testcase name="should preserve thread interruption status when InterruptedException occurs" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should handle multiple operations with same service instance" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should handle operation that modifies client state" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <testcase name="should handle nested exception in operation" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbServiceSpec" time="0.003"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
