<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - EthEventLogDaoSpec</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>EthEventLogDaoSpec</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</a> &gt; EthEventLogDaoSpec</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">92</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">9.064s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">convBlock2EventEntities should handle Web3j creation exception</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should handle empty transaction lists</td>
<td class="success">0.544s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should handle exceptions during log processing</td>
<td class="success">0.010s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should handle exceptions during transaction processing</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should handle missing transaction receipts</td>
<td class="success">0.085s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should handle null transaction</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should include events with empty transaction hash</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should include events with null transaction hash</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should process events from a block with logs</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convBlock2EventEntities should process transactions with valid logs</td>
<td class="success">0.098s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should failed convert a log with EventValues is null </td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle ABI parser exception</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle block retrieval exception</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle empty topics list</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle general exceptions during processing</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle null ABI event</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should handle real AddProviderRole event with bytes32 parameters</td>
<td class="success">0.023s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should successfully convert a log to an event with ABI event</td>
<td class="success">0.193s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should successfully create Event object with real AddProviderRole event</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertEthLogToEventEntity should test ObjectMapper serialization functionality</td>
<td class="success">0.034s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeDynamicArray should decode array of Type objects</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle [Mock for type 'Type'] type list and [] component list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle [Mock for type 'Type'] type list and null component list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle [] type list and [] component list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle basic type parameters</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle dynamic array type parameters</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle more inputs than values</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle null type list and null component list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle tuple array type parameters</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle tuple array type parameters but value type is not a list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle tuple type parameters</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeEventParameters should handle tuple type parameters but value type is not a list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should decode simple tuple with basic types</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle DynamicArray in tuple</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle [Mock for type 'Type'] type and [] components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle [Mock for type 'Type'] type and null components</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle [] type and [] components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle nested tuple types</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle null type and null components</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTuple should handle when tuple types but value of Type is not a list</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should fallback decode if item is List&lt;Type&gt;</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle DynamicArray in tuple array</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle DynamicStruct and StaticStruct of component</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle [Mock for type 'Type'] type list and [] components list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle [Mock for type 'Type'] type list and null components list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle [] type list and [] components list</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle nested tuple array in tuple array</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle null type list and null components list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">decodeTupleArray should handle regular array inside tuple array</td>
<td class="success">0.031s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getBlockTimestamp should handle IOException</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getBlockTimestamp should return correct timestamp</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should handle exceptions</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should handle exceptions during event processing</td>
<td class="success">0.050s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should handle forced outer error</td>
<td class="success">0.028s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should handle general log processing errors</td>
<td class="success">0.011s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should handle log processing errors</td>
<td class="success">0.011s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true</td>
<td class="success">0.012s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should process a log entry correctly</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should process logs and return transactions</td>
<td class="success">0.018s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getPendingTransactions should process logs with valid data</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDelayed should detect delayed blocks</td>
<td class="success">0.018s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should get block timestamp correctly</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should add transaction to queue when events are found</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should call convBlock2EventEntities when block has transactions</td>
<td class="success">0.510s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should cover remaining async callback paths with forced execution</td>
<td class="success">1.224s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should events is empty when processing with events</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should execute subscription callback and add transaction to queue</td>
<td class="success">1.019s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should execute subscription callback and process block</td>
<td class="success">1.018s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle NumberFormatException when parsing allowable timestamp difference</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle empty events in subscription callback</td>
<td class="success">1.034s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle error callback with proper cleanup</td>
<td class="success">0.504s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle exception during Web3j subscription creation</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle exceptions during block processing with events</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle null events in subscription callback</td>
<td class="success">1.016s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle subscription callback exception during block processing</td>
<td class="success">0.108s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle subscription callback with delayed block</td>
<td class="success">0.147s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle subscription callback with empty events</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle subscription callback with non-delayed block and events</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should handle subscription completion</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should log 'detect block includes events' when convBlock2EventEntities returns events</td>
<td class="success">1.019s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should log subscription error</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should not add transaction to queue when no events are found</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should process non-delayed blocks with events</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should skip processing for delayed blocks</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should subscribe to block events</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should subscribe to contract events</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should trigger InterruptedException in async callback</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should trigger main subscription callback lambda0</td>
<td class="success">0.021s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should trigger main subscription callback lambda0 with real execution</td>
<td class="success">0.122s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">subscribeAll should trigger subscription error callback lambda3</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">unsubscribe should dispose subscription when subscription is not null</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">unsubscribe should handle null subscription gracefully</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>AddProviderRole event signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b
Test successful - Indexed values: null
Test successful - Non-indexed values: null
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
