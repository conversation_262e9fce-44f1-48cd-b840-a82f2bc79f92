<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BlockHeightDao</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a> &gt; <span class="el_class">BlockHeightDao</span></div><h1>BlockHeightDao</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 191</td><td class="ctr2">100%</td><td class="bar">0 of 4</td><td class="ctr2">100%</td><td class="ctr1">0</td><td class="ctr2">8</td><td class="ctr1">0</td><td class="ctr2">53</td><td class="ctr1">0</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a3"><a href="BlockHeightDao.java.html#L48" class="el_method">lambda$get$0(BcmonitoringConfigurationProperties.Aws.Dynamodb, DynamoDbClient)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="45" alt="45"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="BlockHeightDao.java.html#L42" class="el_method">get()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="109" height="10" title="41" alt="41"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="BlockHeightDao.java.html#L89" class="el_method">buildBlockHeightsFromMapValue(QueryResponse)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="104" height="10" title="39" alt="39"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="BlockHeightDao.java.html#L109" class="el_method">save(BlockHeight)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="101" height="10" title="38" alt="38"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="BlockHeightDao.java.html#L115" class="el_method">lambda$save$1(BcmonitoringConfigurationProperties.Aws.Dynamodb, BlockHeight, DynamoDbClient)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="16" alt="16"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="BlockHeightDao.java.html#L28" class="el_method">BlockHeightDao(PooledDynamoDbService, BcmonitoringConfigurationProperties, LoggingService)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="12" alt="12"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>