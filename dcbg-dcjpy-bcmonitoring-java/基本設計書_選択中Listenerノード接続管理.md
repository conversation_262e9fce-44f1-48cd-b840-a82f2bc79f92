# 基本設計書：選択中Listenerノード接続管理

## 4. リスナーノード接続管理
BCMonitoringにおいて、Besuリスナーノードへの接続を管理し、安定したWebSocket接続を維持するためのListenerノード接続管理機能の設計する

### 4.1 Websocket接続インスタンス管理
以下の2つのWebsocket接続インスタンスを設計する
- リアルタイム監視専用のWebsocket接続
- Json-RPCメソッドコーラー専用のWebsocket接続

### 4.2 必要な接続数と理由

#### 4.2.1 接続数: 2接続
BCMonitoringでは、以下の理由により2つのWebSocket接続を使用する：

**1. リアルタイム監視専用接続 (web3j)**
- **用途**: `newHeadsNotifications()` による新ブロック購読
- **特徴**: 長時間の購読状態を維持
- **処理**: ブロック通知の受信とイベント処理

**2. JSON-RPC専用接続 (web3jCaller)**
- **用途**: `ethGetBlockByNumber()` などのRPCメソッド呼び出し
- **特徴**: リクエスト・レスポンス型の通信
- **処理**: ブロック詳細情報の取得

#### 4.2.2 分離する理由

**パフォーマンス面**
- 購読処理とRPC呼び出しの競合を回避
- 各接続が専用の処理に集中できる
- ブロック通知の遅延を最小化

**安定性面**
- 一方の接続でエラーが発生しても他方に影響しない
- 購読の長時間維持とRPC呼び出しの独立性を確保
- 接続障害時の影響範囲を限定

**実装面**
- Web3jライブラリの推奨パターンに準拠
- 接続管理の複雑さを軽減
- デバッグとトラブルシューティングが容易
