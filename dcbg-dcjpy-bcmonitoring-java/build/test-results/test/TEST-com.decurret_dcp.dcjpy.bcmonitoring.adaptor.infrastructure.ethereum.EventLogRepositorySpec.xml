<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:38.623Z" hostname="tungvt.local" time="0.019">
  <properties/>
  <testcase name="Subscribe should return transactions when successful" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec" time="0.008"/>
  <testcase name="Subscribe should throw BlockchainException when DAO error occurs" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec" time="0.006"/>
  <testcase name="GetFilterLogs should return transactions when successful" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec" time="0.002"/>
  <testcase name="GetFilterLog<PERSON> should throw BlockchainException when DAO error occurs" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositorySpec" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
