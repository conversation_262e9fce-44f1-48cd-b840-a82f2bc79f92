apiVersion: v1
kind: ConfigMap
metadata:
  name: invoke-core-tracker-envs
  namespace: dcbg-core
data:
  DB_MAXIMUM_POOL_SIZE: "50"
  DB_MINIMUM_IDLE: "10"
  DB_CONNECTION_TIMEOUT: "30000"
  DB_IDLE_TIMEOUT: "600000"
  PUSH_NOTIFICATION_SQS_QUEUE_NAME: "dcjpy_bctracker_queue_invoke-core.fifo"
  BASE_VISIBILITY_TIMEOUT: "5"
  BASE_WAIT_TIME_SECONDS: "1"
  LOCAL_STACK_ENDPOINT: ""
  BCCLIENT_APP_BASE_URL: "http://bcclient.dcbg-core.svc.cluster.local:8081"
  BCCLIENT_HTTP_CONNECTION_MAX_PER_ROUTE: "50"
  BCCLIENT_HTTP_CONNECTION_MAX_TOTAL: "50"
  BCCLIENT_READ_TIMEOUT_MILLISEC: "20000"
  CORE_APP_BASE_URL: "http://core.dcbg-core.svc.cluster.local:80"
  CORE_READ_TIMEOUT_MILLISEC: "500000"
  CORE_HTTP_CONNECTION_MAX_PER_ROUTE: "100"
  CORE_HTTP_CONNECTION_MAX_TOTAL: "100"
  CORE_SECRET_MANAGER_LOCAL_ENDPOINT : ""
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
