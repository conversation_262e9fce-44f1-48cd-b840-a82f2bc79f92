name: Check Manifest API Version

on:
  workflow_dispatch:
  pull_request:

env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
  KUBE_VERSION: v1.27

jobs:
  check_manifest:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout
        uses: actions/checkout@v3 # 公式 https://github.com/actions/checkout

      - name: Install Pluto
        uses: FairwindsOps/pluto/github-action@master # 公式 https://github.com/FairwindsOps/pluto

      - name: Check Manifest
        run: pluto detect-files -d . -o wide --target-versions k8s=${{ env.KUBE_VERSION }}

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2 # 公式 https://github.com/rtCamp/action-slack-notify
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "Manifest API Version Check Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The API version of kubernetes manifest is deprecated or removed.
