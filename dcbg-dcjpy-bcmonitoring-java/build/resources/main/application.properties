# Default Application Configuration
# This file contains default values that can be overridden by environment-specific profiles
# Use spring.profiles.active to activate specific environment configurations

spring.application.name=Bcmonitoring

# Default server configuration
server.port=2345

# Default AWS Configuration
# These values will be overridden by environment-specific profiles
aws.region=${AWS_REGION:ap-northeast-1}
aws.access-key-id=${AWS_ACCESS_KEY:dummy}
aws.secret-access-key=${AWS_SECRET_KEY:dummy}
aws.dynamodb.region=${DYNAMODB_REGION:ap-northeast-1}
aws.dynamodb.table-prefix=${DYNAMODB_TABLE_NAME_PREFIX:local}
aws.dynamodb.endpoint=${DYNAMODB_ENDPOINT:http://localstack:4566}
aws.s3.bucket-name=${S3_BUCKET_NAME:abijson-local-bucket}
aws.s3.region=${S3_REGION:ap-northeast-1}
aws.dynamodb.events-table-name=${EVENTS_TABLE_NAME:Events}
aws.dynamodb.block-height-table-name=${BLOCK_HEIGHT_TABLE_NAME:BlockHeight}

# Default WebSocket Configuration
websocket.uri.host=${WEBSOCKET_URI_HOST:localhost}
websocket.uri.port=${WEBSOCKET_URI_PORT:18541}

# Default Subscription Configuration
subscription.check-interval=${SUBSCRIPTION_CHECK_INTERVAL:3000}
subscription.allowable-block-timestamp-diff-sec=${ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC:2}

# Default Environment Configuration
env=${ENV:local}
abi-format=${ABI_FORMAT:hardhat}
eager-start=${EAGER_START:true}

# Default LocalStack Configuration
local-stack.end-point=${LOCALSTACK_ENDPOINT:http://localstack:4566}
local-stack.region=${LOCALSTACK_REGION:ap-northeast-1}
local-stack.access-key=${LOCALSTACK_ACCESS_KEY:access123}
local-stack.secret-key=${LOCALSTACK_SECRET_KEY:secret123}
