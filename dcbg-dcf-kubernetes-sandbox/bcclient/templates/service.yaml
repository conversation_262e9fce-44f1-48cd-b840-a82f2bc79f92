apiVersion: v1
kind: Service
metadata:
  name: bcclient
  namespace: dcbg-core
  labels:
    app: bcclient
spec:
  selector:
    app: bcclient
  type: ClusterIP
  ports:
    - protocol: TCP
      port: 8081
      targetPort: {{ .Values.application.ports.containerPort }}
      name: http
---
apiVersion: v1
kind: Service
metadata:
  name: bcclient-read-only
  namespace: dcbg-core
  labels:
    app: bcclient-read-only
spec:
  selector:
    app: bcclient-read-only
  type: ClusterIP
  ports:
    - protocol: TCP
      port: 8081
      targetPort: {{ .Values.application.ports.containerPort }}
      name: http
