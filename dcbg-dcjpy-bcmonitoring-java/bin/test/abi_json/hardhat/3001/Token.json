{"address": "0x57Fe7860bD66790b7E38021374Ae00B3d030f3C7", "abi": [{"type": "event", "anonymous": false, "name": "AddToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Approval", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "ownerId", "indexed": true}, {"type": "bytes32", "name": "spenderId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Burn", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "BurnCancel", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "uint256", "name": "blockTimestamp", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "CustomTransfer", "inputs": [{"type": "bytes32", "name": "sendAccountId", "indexed": false}, {"type": "bytes32", "name": "fromAccountId", "indexed": false}, {"type": "bytes32", "name": "toAccountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "miscValue1", "indexed": false}, {"type": "string", "name": "miscValue2", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Mint", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "symbol", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetEnabledToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Transfer", "inputs": [{"type": "tuple", "name": "transferData", "indexed": false, "components": [{"type": "bytes32", "name": "transferType"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "fromValidatorId"}, {"type": "bytes32", "name": "toValidatorId"}, {"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}, {"type": "uint256", "name": "businessZoneBalance"}, {"type": "uint16", "name": "bizZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "addToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addTotalSupply", "constant": false, "payable": false, "inputs": [{"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "approve", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "burn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "burnCancel", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "blockTimestamp"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkApprove", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "getAllowance", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}], "outputs": [{"type": "uint256", "name": "allowance"}, {"type": "uint256", "name": "approvedAt"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAllowanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerAccountId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}], "outputs": [{"type": "tuple[]", "name": "approvalData", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getBalanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint16[]", "name": "zoneIds"}, {"type": "string[]", "name": "zoneNames"}, {"type": "uint256[]", "name": "balances"}, {"type": "string[]", "name": "accountNames"}, {"type": "bytes32[]", "name": "accountStatus"}, {"type": "uint256", "name": "totalBalance"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getTokenAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}]}, {"type": "function", "name": "hasToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bool", "name": "chkEnabled"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasTokenState", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "mint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "modToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setTokenAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setTokenEnabled", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "tokenId"}, {"type": "bool", "name": "enabled"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "subTotalSupply", "constant": false, "payable": false, "inputs": [{"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "transferSingle", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xfe7e2a356c803372b7ade9c0d64e0feb1b9b74addae65ec5af9bdc03b185b874", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0x57Fe7860bD66790b7E38021374Ae00B3d030f3C7", "transactionIndex": 0, "gasUsed": "5355427", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xfcda7030e447052e2a20d0e1a778184554d853413d621e018d3effa6eb64a984", "blockNumber": 650, "cumulativeGasUsed": "5355427", "status": 1}}