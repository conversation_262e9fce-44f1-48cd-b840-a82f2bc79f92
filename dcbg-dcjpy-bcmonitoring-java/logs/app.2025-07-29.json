{"@timestamp":"2025-07-29T11:16:00.279101+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:16:00.360557+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:16:00.368613+07:00","@version":"1","message":"docker-machine executable was not found on PATH ([/Users/<USER>/.codeium/windsurf/bin, /opt/homebrew/opt/openjdk@21/bin, /opt/homebrew/opt/libpq/bin, /Users/<USER>/.jenv/shims, /Users/<USER>/.jenv/bin, /Users/<USER>/.rbenv/shims, /Users/<USER>/.rbenv/shims, /Users/<USER>/.nvm/versions/node/v22.12.0/bin, /opt/homebrew/bin, /opt/homebrew/sbin, /Library/Frameworks/Python.framework/Versions/3.13/bin, /usr/local/bin, /System/Cryptexes/App/usr/bin, /usr/bin, /bin, /usr/sbin, /sbin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin, /Library/Apple/usr/bin, /usr/local/go/bin, /Users/<USER>/.lmstudio/bin, /Users/<USER>/go/bin])","logger_name":"org.testcontainers.dockerclient.DockerMachineClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:16:00.369238+07:00","@version":"1","message":"Could not find a valid Docker environment. Please check configuration. Attempted configurations were:\n\tUnixSocketClientProviderStrategy: failed with exception InvalidConfigurationException (Could not find unix domain socket). Root cause NoSuchFileException (/var/run/docker.sock)As no valid configuration was found, execution cannot continue.\nSee https://www.testcontainers.org/on_failure.html for more details.","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:05.490867+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:05.571009+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:05.573852+07:00","@version":"1","message":"docker-machine executable was not found on PATH ([/Users/<USER>/.codeium/windsurf/bin, /opt/homebrew/opt/openjdk@21/bin, /opt/homebrew/opt/libpq/bin, /Users/<USER>/.jenv/shims, /Users/<USER>/.jenv/bin, /Users/<USER>/.rbenv/shims, /Users/<USER>/.rbenv/shims, /Users/<USER>/.nvm/versions/node/v22.12.0/bin, /opt/homebrew/bin, /opt/homebrew/sbin, /Library/Frameworks/Python.framework/Versions/3.13/bin, /usr/local/bin, /System/Cryptexes/App/usr/bin, /usr/bin, /bin, /usr/sbin, /sbin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin, /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin, /Library/Apple/usr/bin, /usr/local/go/bin, /Users/<USER>/.lmstudio/bin, /Users/<USER>/go/bin])","logger_name":"org.testcontainers.dockerclient.DockerMachineClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:05.574297+07:00","@version":"1","message":"Could not find a valid Docker environment. Please check configuration. Attempted configurations were:\n\tUnixSocketClientProviderStrategy: failed with exception InvalidConfigurationException (Could not find unix domain socket). Root cause NoSuchFileException (/var/run/docker.sock)As no valid configuration was found, execution cannot continue.\nSee https://www.testcontainers.org/on_failure.html for more details.","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:40.680198+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:40.756709+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.054804+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.056031+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.071515+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.071809+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.07195+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.072517+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.085483+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.223398+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.324571+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: 4210b43e70a14aa753ea3825a430ff64d17cc1e9a5c3cadcb92aa4bd17f4dba0","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.560949+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT0.487779S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.564664+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.56821+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:41.796218+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: 564dbda7b77e57e6f5758da3c99b35df65c8d87b7e8222a7a71316f5532e8146","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.110086+07:00","@version":"1","message":"Container docker/compose:1.29.2 started in PT1.541871S","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.11317+07:00","@version":"1","message":"Docker Compose container is running for command: up -d","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.1159+07:00","@version":"1","message":"STDERR: Creating network \"chxquz9l6zjx_default\" with the default driver","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-2060959857","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.116593+07:00","@version":"1","message":"STDERR: Creating chxquz9l6zjx_localstack_1 ... ","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-2060959857","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.116877+07:00","@version":"1","message":"STDERR: Creating chxquz9l6zjx_localstack_1 ... done","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-2060959857","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.116926+07:00","@version":"1","message":"Docker Compose has finished running","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.121719+07:00","@version":"1","message":"Creating container for image: alpine/socat:1.7.4.3-r0","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.161944+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 is starting: 35dfaf0fafa82f84900a37f2572ae39600ea1ce48281f9a542c7d8d3c44b3112","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:43.367944+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 started in PT0.246348S","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:54.740678+07:00","@version":"1","message":"Starting EventMonitoringITSpec using Java 21.0.7 with PID 54859 (started by thanhtungvu in /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:54.741724+07:00","@version":"1","message":"The following 1 profile is active: \"test\"","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.146068+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.251571+07:00","@version":"1","message":"Using LocalStack credentials for local environment","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.252882+07:00","@version":"1","message":"Configuring S3 client for local environment with endpoint: http://localhost:55949 and region: ap-northeast-1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.277269+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.335716+07:00","@version":"1","message":"Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)","logger_name":"org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration","thread_name":"Test worker","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:55.506832+07:00","@version":"1","message":"Started EventMonitoringITSpec in 0.931 seconds (process running for 16.402)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.37353+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.373829+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.396117+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/AccessCtrl.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.445259+07:00","@version":"1","message":"Added contract address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.501021+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16c04281], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.501303+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.501472+07:00","@version":"1","message":"Event: RoleAdminChanged, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@26d899a0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@bfc87a7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5afb7251], Signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.501592+07:00","@version":"1","message":"Parsed event: RoleAdminChanged with signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502022+07:00","@version":"1","message":"Event: RoleGranted, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c4c722b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@436219ba, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@420a827d], Signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502297+07:00","@version":"1","message":"Parsed event: RoleGranted with signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502435+07:00","@version":"1","message":"Event: RoleRevoked, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@539e5026, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c8cbd54, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f70d8b6], Signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502535+07:00","@version":"1","message":"Parsed event: RoleRevoked with signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502626+07:00","@version":"1","message":"Successfully parsed ABI with 4 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502717+07:00","@version":"1","message":"Registered events for contract: AccessCtrl at address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.502793+07:00","@version":"1","message":"ABI file processed: address=0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4, contract_name=AccessCtrl, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.503425+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.513152+07:00","@version":"1","message":"Added contract address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.516568+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3da6d854, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@159e6d3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd197, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2d12248c], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.516727+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.516872+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@267d2d02, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@685c2860, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3427fb95], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.51697+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.517125+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@82d8fd9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4777a6e3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4e619945], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.517227+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.517436+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@21a62463, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5b1cde0a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@19f5df9d], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.517945+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.530856+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.532965+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.534094+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@408664b5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@70b67f13, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fe5521d], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.534333+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.536161+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.536434+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@517eb52c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@12f306f7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6773cb0e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33f957ae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@170a4328, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d442b80], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.536569+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.536795+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6416b89d], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.53695+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.537647+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@58fdc418, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@64d9ecac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35da72fa], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.537888+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.538006+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.538089+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.538164+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=2","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.538323+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/FinancialZoneAccount.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.548432+07:00","@version":"1","message":"Added contract address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.550412+07:00","@version":"1","message":"Processing nested tuple with 6 components","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.554189+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.55434+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.554592+07:00","@version":"1","message":"Event: AddAccountLimit, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@149eb04a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28b07453, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c96d71f], Signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.554697+07:00","@version":"1","message":"Parsed event: AddAccountLimit with signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.554831+07:00","@version":"1","message":"Event: AddCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2f4e2e7b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4e33d738, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@658a2041, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1f5e3dc0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@413df5a6], Signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.554936+07:00","@version":"1","message":"Parsed event: AddCumulativeAmount with signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555057+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@116760ae], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555147+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555294+07:00","@version":"1","message":"Event: SubtractCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@255bd2eb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b055bbd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@653b17be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ff369a7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34c43638], Signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555393+07:00","@version":"1","message":"Parsed event: SubtractCumulativeAmount with signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555524+07:00","@version":"1","message":"Event: SyncBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55fd5bae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@68c62048, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c2cc2a6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@294c46ed, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4fceeea3], Signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.55563+07:00","@version":"1","message":"Parsed event: SyncBurn with signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555764+07:00","@version":"1","message":"Event: SyncCharge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15ad2c0e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5e9d7b78, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@520021aa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a8376bf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1632fabd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23c115b8], Signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555869+07:00","@version":"1","message":"Parsed event: SyncCharge with signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.555986+07:00","@version":"1","message":"Event: SyncCumulativeReset, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@701f6d1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d8cb29], Signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556082+07:00","@version":"1","message":"Parsed event: SyncCumulativeReset with signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556213+07:00","@version":"1","message":"Event: SyncDischarge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3e3ebd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b7987d7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@20054016, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@674346bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1679473f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d93d18], Signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556345+07:00","@version":"1","message":"Parsed event: SyncDischarge with signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.55647+07:00","@version":"1","message":"Event: SyncMint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bd6d6c5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65d19a5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ac1e6ee, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62c83153, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2fe5b331, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@419b0759], Signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556574+07:00","@version":"1","message":"Parsed event: SyncMint with signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556699+07:00","@version":"1","message":"Event: SyncTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@14b86f12, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3759967f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35a7d79e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c9fafbc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b0895c2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@191eb2], Signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556806+07:00","@version":"1","message":"Parsed event: SyncTransfer with signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.556886+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.55697+07:00","@version":"1","message":"Registered events for contract: FinancialZoneAccount at address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.557041+07:00","@version":"1","message":"ABI file processed: address=0xf908c90f27013e2e85eb6af516f7363c674bbec3, contract_name=FinancialZoneAccount, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.557236+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.565628+07:00","@version":"1","message":"Added contract address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567329+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47d736, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ca], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567453+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567588+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c55364, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37840cfa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9c4835b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@95acffa], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567687+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567817+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@202df3c6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3759d8e5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1aaa5b0e], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.567914+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568038+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45d062be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@aac1a01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73cb6541], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568129+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568244+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bf1321d], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568332+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568476+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7df2624f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@176413e2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fad9d22], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568586+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568819+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@d7e2110, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@63f0caea, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@543c2e75], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.568919+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569036+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72bfd5d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b213a09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ee3181d], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569132+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.56925+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b99c232, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73800309, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1838e02], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569359+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569436+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569511+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.569888+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.570365+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.577531+07:00","@version":"1","message":"Added contract address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.580021+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ecd2968, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ec0e732, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@54f1818c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37f1249e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1050dd61], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.580344+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.580656+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3980412e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@61254a73, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fce571d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49632250, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72518363], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.580895+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.581241+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@39ec2988, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1437f717, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44962663, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a22ef09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@625a21ac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@497efdbf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3cae6fcd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11d995f6], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.581584+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.581935+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd8120, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c9013cd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@530fd795, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ea49e27, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@40deaa52, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5cccc7ff, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d2108c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5baded37, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6e14502a], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582108+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582276+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@545d9128, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35eaad5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11a3a80, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d43af89, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75afb3d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@154f7867, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45519e74], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582392+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582513+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15be4eb0], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582607+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582739+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d071eac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7365d12b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a0e530d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5da25132, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7875d654, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2544bcf8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d9a0566, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22fef226], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582848+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.582969+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74ef575b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74cc8642, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49401c88, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c7aa844], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.583084+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.583203+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1221611f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24f1b8f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b22e312], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.583297+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.589285+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.590111+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@610a19e6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3dec5ca6], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.590484+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.590688+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.590842+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.591075+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.591902+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Validator.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.602215+07:00","@version":"1","message":"Added contract address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.604601+07:00","@version":"1","message":"Event: AddAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c1ceb4a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c878e01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ab90a7e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@83718d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b5e9c6c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b79ee80], Signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.604757+07:00","@version":"1","message":"Parsed event: AddAccount with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.604886+07:00","@version":"1","message":"Event: AddValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69b1e76b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7225ecc9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d77a31d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197e53dd], Signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.604976+07:00","@version":"1","message":"Parsed event: AddValidator with signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.605156+07:00","@version":"1","message":"Event: AddValidatorAccountId, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7f927526, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@228c8db9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c3bdbe], Signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.605244+07:00","@version":"1","message":"Parsed event: AddValidatorAccountId with signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.60546+07:00","@version":"1","message":"Event: AddValidatorRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6948f680, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51f95ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********], Signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.605989+07:00","@version":"1","message":"Parsed event: AddValidatorRole with signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.60638+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5c894a01], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.606481+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.606607+07:00","@version":"1","message":"Event: ModValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72db5be6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b1321b2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9a59dcc], Signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.606872+07:00","@version":"1","message":"Parsed event: ModValidator with signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.607354+07:00","@version":"1","message":"Event: SetBizZoneTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fea17bb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37467bcb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25cac220, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2470a8], Signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.607578+07:00","@version":"1","message":"Parsed event: SetBizZoneTerminated with signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.607708+07:00","@version":"1","message":"Event: SetTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f3cf8d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b205ad7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38884a51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b8adcd8], Signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.607812+07:00","@version":"1","message":"Parsed event: SetTerminated with signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.607955+07:00","@version":"1","message":"Event: SyncAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33580d49, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ae70f17, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4708d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4de66814, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ea716a4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7cfa39d0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4443ef5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a9b890b], Signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608061+07:00","@version":"1","message":"Parsed event: SyncAccount with signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608184+07:00","@version":"1","message":"Event: ValidatorEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c8faaca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4af40c48], Signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608274+07:00","@version":"1","message":"Parsed event: ValidatorEnabled with signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608344+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608406+07:00","@version":"1","message":"Registered events for contract: Validator at address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608511+07:00","@version":"1","message":"ABI file processed: address=0xfdc1a198656d23b75d15c3d37194ad5fabf17081, contract_name=Validator, last_modified=Tue Jul 29 11:17:55 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608657+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.608721+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.615105+07:00","@version":"1","message":"Created new DynamoDB connection","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.711811+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.712293+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.736449+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:56.737319+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:57.737087+07:00","@version":"1","message":"Block 1000 is delayed by more than 2 seconds","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:57.738345+07:00","@version":"1","message":"Event found tx_hash=0xabc222134","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:57.738496+07:00","@version":"1","message":"Looking for event with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac for address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:57.738589+07:00","@version":"1","message":"Available signatures for address 0xfdc1a198656d23b75d15c3d37194ad5fabf17081: [0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004, 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498, 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f, 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707, 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52, 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797, 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88, 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32, 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac, 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:17:57.738686+07:00","@version":"1","message":"Found matching event for signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:12.774182+07:00","@version":"1","message":"Event parsed tx_hash=0xabc222134, name=AddAccount","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:12.775394+07:00","@version":"1","message":"detect block includes events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.18373+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.263319+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.562554+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.568308+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.595087+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.595403+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.595551+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.5962+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.607799+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.752416+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:21.841001+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: 4d9274143933dbbd421504812be0ee344d9cc14b89dd6b35b7289337cae96698","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:22.060816+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT0.463876S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:22.064042+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:22.068048+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:22.307823+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: e80ca32be71c4d4076f6bef2742e8289acf94f98e8a114d7510a5d81ebd4e3ce","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:23.509079+07:00","@version":"1","message":"Could not start container","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"ERROR","level_value":40000,"stack_trace":"java.lang.IllegalStateException: Container did not start correctly.\n\tat org.testcontainers.containers.GenericContainer.tryStart(GenericContainer.java:480)\n\tat org.testcontainers.containers.GenericContainer.lambda$doStart$0(GenericContainer.java:344)\n\tat org.rnorth.ducttape.unreliables.Unreliables.retryUntilSuccess(Unreliables.java:81)\n\tat org.testcontainers.containers.GenericContainer.doStart(GenericContainer.java:334)\n\tat org.testcontainers.containers.GenericContainer.start(GenericContainer.java:322)\n\tat org.testcontainers.containers.ContainerisedDockerCompose.invoke(DockerComposeContainer.java:710)\n\tat org.testcontainers.containers.DockerComposeContainer.runWithCompose(DockerComposeContainer.java:337)\n\tat org.testcontainers.containers.DockerComposeContainer.createServices(DockerComposeContainer.java:258)\n\tat org.testcontainers.containers.DockerComposeContainer.start(DockerComposeContainer.java:189)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.helper.AdhocHelper.startContainer(AdhocHelper.groovy:33)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.helper.AdhocHelper.<clinit>(AdhocHelper.groovy:25)\n\tat java.base/jdk.internal.misc.Unsafe.ensureClassInitialized0(Native Method)\n\tat java.base/jdk.internal.misc.Unsafe.ensureClassInitialized(Unsafe.java:1160)\n\tat java.base/java.lang.invoke.DirectMethodHandle.checkInitialized(DirectMethodHandle.java:383)\n\tat java.base/java.lang.invoke.DirectMethodHandle.ensureInitialized(DirectMethodHandle.java:371)\n\tat java.base/java.lang.invoke.DirectMethodHandle.internalMemberNameEnsureInit(DirectMethodHandle.java:336)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.base.BaseAdhocITSpec.setupSpecCommon(BaseAdhocITSpec.groovy:100)\n\tat org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)\n\tat adhoc.event_monitoring.EventMonitoringITSpec.setupSpec(EventMonitoringITSpec.groovy:58)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:277)\n\tat org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.doRunSetupSpec(PlatformSpecRunner.java:152)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSetupSpec$2(PlatformSpecRunner.java:138)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:123)\n\tat org.spockframework.spring.SpringInterceptor.interceptSetupSpecMethod(SpringInterceptor.java:40)\n\tat org.spockframework.runtime.extension.AbstractMethodInterceptor.intercept(AbstractMethodInterceptor.java:36)\n\tat org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:122)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:433)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSetupSpec(PlatformSpecRunner.java:133)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSetupSpec(PlatformSpecRunner.java:128)\n\tat org.spockframework.runtime.SpecNode.before(SpecNode.java:54)\n\tat org.spockframework.runtime.SpecNode.before(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:153)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)\n\tat org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:72)\n\tat org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:66)\n\tat org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:157)\n\tat org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:442)\n\tat org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:425)\n\tat org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:59)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:72)\n\tat org.spockframework.runtime.SpecNode.around(SpecNode.java:12)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)\n\tat org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)\n\tat org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)\n\tat org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)\n\tat org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)\n\tat org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)\n\tat org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)\n\tat org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)\n\tat org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)\n\tat org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)\n\tat org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)\n\tat org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)\n\tat org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)\n\tat org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)\n\tat jdk.proxy2/jdk.proxy2.$Proxy6.stop(Unknown Source)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)\n\tat org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)\n\tat org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)\n\tat org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)\n\tat worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)\n","application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:23.540783+07:00","@version":"1","message":"Log output from the failed container:\nCreating network \"hdohs7w2fs47_default\" with the default driver\nCreating hdohs7w2fs47_localstack_1 ... \r\nHost is already in use by another container\nCreating hdohs7w2fs47_localstack_1 ... error\r\n\nERROR: for hdohs7w2fs47_localstack_1  Cannot start service localstack: driver failed programming external connectivity on endpoint hdohs7w2fs47_localstack_1 (ab30464b0d0d25edab555ad9c34312e0cae004edff298cd6a3494c381940e792): Bind for 0.0.0.0:4582 failed: port is already allocated\n\nERROR: for localstack  Cannot start service localstack: driver failed programming external connectivity on endpoint hdohs7w2fs47_localstack_1 (ab30464b0d0d25edab555ad9c34312e0cae004edff298cd6a3494c381940e792): Bind for 0.0.0.0:4582 failed: port is already allocated\nEncountered errors while bringing up the project.\n","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"ERROR","level_value":40000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.35544+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.435917+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.768836+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.769898+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.787121+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.787444+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.787592+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.788202+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.80146+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.919799+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:28.996347+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: dfe09fd960e53d5c5976c7d9164764f39157abf70f59f81bf2c2c6faed0a69ce","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:29.461898+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT0.672976S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:29.46599+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:29.470094+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:29.707778+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: c09ddcd5fa72bc902ca6c2664c9e79048834465771085f13f757b617cb1b8a57","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.968048+07:00","@version":"1","message":"Container docker/compose:1.29.2 started in PT1.497967S","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.972833+07:00","@version":"1","message":"Docker Compose container is running for command: up -d","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.978708+07:00","@version":"1","message":"Docker Compose has finished running","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.979137+07:00","@version":"1","message":"STDERR: Creating network \"tsrnlhyrvemz_default\" with the default driver","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--317284071","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.980099+07:00","@version":"1","message":"STDERR: Creating tsrnlhyrvemz_localstack_1 ... ","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--317284071","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.980543+07:00","@version":"1","message":"STDERR: Creating tsrnlhyrvemz_localstack_1 ... done","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream--317284071","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:30.986064+07:00","@version":"1","message":"Creating container for image: alpine/socat:1.7.4.3-r0","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:31.028527+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 is starting: 2333570d7ee14149a809a78f24aaaee4fe9ce24d605e7fc8213da0d67d27f31d","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:31.252256+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 started in PT0.266335S","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.264985+07:00","@version":"1","message":"Starting EventMonitoringITSpec using Java 21.0.7 with PID 55067 (started by thanhtungvu in /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.268724+07:00","@version":"1","message":"The following 1 profile is active: \"test\"","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.758576+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.868667+07:00","@version":"1","message":"Using LocalStack credentials for local environment","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.86963+07:00","@version":"1","message":"Configuring S3 client for local environment with endpoint: http://localhost:56254 and region: ap-northeast-1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.892816+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:47.937917+07:00","@version":"1","message":"Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)","logger_name":"org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration","thread_name":"Test worker","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:48.106919+07:00","@version":"1","message":"Started EventMonitoringITSpec in 1.173 seconds (process running for 21.176)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.119109+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.119376+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.404586+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/AccessCtrl.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.432492+07:00","@version":"1","message":"Added contract address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.479377+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16150bb7], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.47967+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.479816+07:00","@version":"1","message":"Event: RoleAdminChanged, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2018453c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4403ddef, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c3a580b], Signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.479913+07:00","@version":"1","message":"Parsed event: RoleAdminChanged with signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480096+07:00","@version":"1","message":"Event: RoleGranted, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@501f2048, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@a297301, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@548d884f], Signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480188+07:00","@version":"1","message":"Parsed event: RoleGranted with signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480312+07:00","@version":"1","message":"Event: RoleRevoked, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38228a4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ab655fc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6b469ad0], Signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480403+07:00","@version":"1","message":"Parsed event: RoleRevoked with signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480482+07:00","@version":"1","message":"Successfully parsed ABI with 4 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480567+07:00","@version":"1","message":"Registered events for contract: AccessCtrl at address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.480636+07:00","@version":"1","message":"ABI file processed: address=0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4, contract_name=AccessCtrl, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.481294+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.487553+07:00","@version":"1","message":"Added contract address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491013+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@625071dc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@da2f2fc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77c658f1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5e1f7c80], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491155+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491287+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3f20280e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@250f0804, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@60cbdd84], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491456+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491599+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b4b6695, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@26591ed0], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.491695+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.492146+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7789bb55, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@575604ff, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@39972f5b], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.492273+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.499213+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.500876+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.501416+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2fe5b331, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@419b0759, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@14b86f12], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.501532+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.502839+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.50304+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35a7d79e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c9fafbc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b0895c2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@191eb2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b893208, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5aba9e8a], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503173+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503323+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6523a69a], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503419+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503598+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6fdca6c8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@619c3b77, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197346a8], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503706+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503798+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503875+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.503954+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=2","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.5041+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/FinancialZoneAccount.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.50889+07:00","@version":"1","message":"Added contract address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.51072+07:00","@version":"1","message":"Processing nested tuple with 6 components","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.514958+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.515188+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.515474+07:00","@version":"1","message":"Event: AddAccountLimit, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1aaa5b0e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45d062be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@aac1a01], Signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.515591+07:00","@version":"1","message":"Parsed event: AddAccountLimit with signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.515879+07:00","@version":"1","message":"Event: AddCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73cb6541, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bf1321d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7df2624f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@176413e2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fad9d22], Signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516043+07:00","@version":"1","message":"Parsed event: AddCumulativeAmount with signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516175+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@d7e2110], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516279+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516427+07:00","@version":"1","message":"Event: SubtractCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@63f0caea, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@543c2e75, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72bfd5d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b213a09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ee3181d], Signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516529+07:00","@version":"1","message":"Parsed event: SubtractCumulativeAmount with signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516662+07:00","@version":"1","message":"Event: SyncBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b99c232, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73800309, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1838e02, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7264ddf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62cb975b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11204840], Signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.51677+07:00","@version":"1","message":"Parsed event: SyncBurn with signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.516906+07:00","@version":"1","message":"Event: SyncCharge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c4e27f8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3933cb1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77ce229, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@140dd8ce, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69815c50, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ecd2968], Signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517014+07:00","@version":"1","message":"Parsed event: SyncCharge with signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517129+07:00","@version":"1","message":"Event: SyncCumulativeReset, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ec0e732, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@54f1818c], Signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517223+07:00","@version":"1","message":"Parsed event: SyncCumulativeReset with signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517428+07:00","@version":"1","message":"Event: SyncDischarge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37f1249e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1050dd61, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3980412e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@61254a73, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fce571d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49632250], Signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517532+07:00","@version":"1","message":"Parsed event: SyncDischarge with signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517661+07:00","@version":"1","message":"Event: SyncMint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72518363, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@39ec2988, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1437f717, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44962663, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a22ef09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@625a21ac], Signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517765+07:00","@version":"1","message":"Parsed event: SyncMint with signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517895+07:00","@version":"1","message":"Event: SyncTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@497efdbf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3cae6fcd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11d995f6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd8120, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c9013cd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@530fd795], Signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.517999+07:00","@version":"1","message":"Parsed event: SyncTransfer with signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.518094+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.518173+07:00","@version":"1","message":"Registered events for contract: FinancialZoneAccount at address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.518241+07:00","@version":"1","message":"ABI file processed: address=0xf908c90f27013e2e85eb6af516f7363c674bbec3, contract_name=FinancialZoneAccount, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.518379+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.523459+07:00","@version":"1","message":"Added contract address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.52535+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11a3a80, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d43af89], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.52547+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.525591+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75afb3d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@154f7867, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45519e74, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15be4eb0], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.525685+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.525807+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d071eac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7365d12b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a0e530d], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.525894+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526006+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5da25132, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7875d654, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2544bcf8], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526098+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526208+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d9a0566], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526287+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526398+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22fef226, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74ef575b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74cc8642], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526492+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526605+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49401c88, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c7aa844, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1221611f], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526691+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.526802+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24f1b8f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b22e312, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2534aba], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.52689+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.527007+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1be3ea76, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@610a19e6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3dec5ca6], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.527111+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.527188+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.527259+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.527331+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.52747+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.5327+07:00","@version":"1","message":"Added contract address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.534672+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b5e9c6c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b79ee80, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69b1e76b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7225ecc9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d77a31d], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.534819+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.534975+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197e53dd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7f927526, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@228c8db9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c3bdbe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6948f680], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535072+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535224+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51f95ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5c894a01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72db5be6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b1321b2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9a59dcc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fea17bb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37467bcb], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535336+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535472+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25cac220, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2470a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f3cf8d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b205ad7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38884a51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b8adcd8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33580d49, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ae70f17, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4708d8], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535586+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535715+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4de66814, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ea716a4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7cfa39d0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4443ef5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a9b890b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c8faaca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535819+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.535927+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4af40c48], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.53601+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.536138+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2cc107cc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d593164, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d6e0b4d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2712d8e4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34954ad, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@27bf97b1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ab6c468, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b52ea22], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.536244+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.536362+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62f73fd9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@392e2e29, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@151728e0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@29a8d39a], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.536451+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.536561+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@727a3881, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3aed0f81, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16116014], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.53665+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540126+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540506+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@894591b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@151899dd], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540609+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540686+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540748+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540813+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.540937+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Validator.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.546764+07:00","@version":"1","message":"Added contract address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.548691+07:00","@version":"1","message":"Event: AddAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1f4f299, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d39677e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1da8f904, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@19f4aaf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@27d9103f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fd812d], Signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.548819+07:00","@version":"1","message":"Parsed event: AddAccount with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.548941+07:00","@version":"1","message":"Event: AddValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5bc738cf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@20f6cb2a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f416fe3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22587bcf], Signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549033+07:00","@version":"1","message":"Parsed event: AddValidator with signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.54915+07:00","@version":"1","message":"Event: AddValidatorAccountId, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@403b2e0b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@568202bd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@12f851f9], Signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549239+07:00","@version":"1","message":"Parsed event: AddValidatorAccountId with signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549356+07:00","@version":"1","message":"Event: AddValidatorRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@731fc66a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a4de574, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c1c856a], Signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549444+07:00","@version":"1","message":"Parsed event: AddValidatorRole with signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549778+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c3b6e89], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.549869+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550021+07:00","@version":"1","message":"Event: ModValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@711adbf2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74f9f6e0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c060ac0], Signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550227+07:00","@version":"1","message":"Parsed event: ModValidator with signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550402+07:00","@version":"1","message":"Event: SetBizZoneTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1bee64ef, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c8cf92b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7f946ac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ba51c99], Signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550514+07:00","@version":"1","message":"Parsed event: SetBizZoneTerminated with signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550641+07:00","@version":"1","message":"Event: SetTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6384c81e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@389a30e4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@759bcd7d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@56f95683], Signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550746+07:00","@version":"1","message":"Parsed event: SetTerminated with signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550884+07:00","@version":"1","message":"Event: SyncAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@13f8a07a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7a8cbff1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51d775f8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d067c0f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1babeea5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@447b916, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ed574d3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10cd8b02], Signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.550996+07:00","@version":"1","message":"Parsed event: SyncAccount with signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551114+07:00","@version":"1","message":"Event: ValidatorEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@54d79178, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d610dfd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28578f6e], Signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551205+07:00","@version":"1","message":"Parsed event: ValidatorEnabled with signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551278+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551406+07:00","@version":"1","message":"Registered events for contract: Validator at address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551471+07:00","@version":"1","message":"ABI file processed: address=0xfdc1a198656d23b75d15c3d37194ad5fabf17081, contract_name=Validator, last_modified=Tue Jul 29 11:19:48 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.55158+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.551638+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.55623+07:00","@version":"1","message":"Created new DynamoDB connection","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.611757+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.612181+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.640377+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:49.641055+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.646319+07:00","@version":"1","message":"Block 1000 is delayed by more than 2 seconds","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.649902+07:00","@version":"1","message":"Event found tx_hash=0xabc222134","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.650225+07:00","@version":"1","message":"Looking for event with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac for address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.6506+07:00","@version":"1","message":"Available signatures for address 0xfdc1a198656d23b75d15c3d37194ad5fabf17081: [0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004, 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498, 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f, 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707, 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52, 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797, 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88, 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32, 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac, 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.650779+07:00","@version":"1","message":"Found matching event for signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.688099+07:00","@version":"1","message":"Event parsed tx_hash=0xabc222134, name=AddAccount","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.688374+07:00","@version":"1","message":"detect block includes events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.694414+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.790857+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.791336+07:00","@version":"1","message":"Success to register event","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.791835+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.84338+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.843788+07:00","@version":"1","message":"Success to register block number","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:19:50.843868+07:00","@version":"1","message":"Success to process new transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.339249+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.42153+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.749112+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.750241+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.771961+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.772295+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.772486+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.773212+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.7869+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:40.927111+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:41.006693+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: 8bd408c2f471c52b989f5e3b17da2e4c9f792a9f192a19c953fd6bcdd4c5432c","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:41.206713+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT0.432743S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:41.210237+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:41.214466+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:41.44692+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: 15add7fc3adf2a85257562e6157496c4349736ce129dd68525d34fa26d34e8fa","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.754321+07:00","@version":"1","message":"Container docker/compose:1.29.2 started in PT1.539864S","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.755796+07:00","@version":"1","message":"Docker Compose container is running for command: up -d","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.758566+07:00","@version":"1","message":"STDERR: Creating network \"umhv4ghqzvyx_default\" with the default driver","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1918135046","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.758995+07:00","@version":"1","message":"Docker Compose has finished running","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.759068+07:00","@version":"1","message":"STDERR: Creating umhv4ghqzvyx_localstack_1 ... ","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1918135046","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.759464+07:00","@version":"1","message":"STDERR: Creating umhv4ghqzvyx_localstack_1 ... done","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1918135046","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.765273+07:00","@version":"1","message":"Creating container for image: alpine/socat:1.7.4.3-r0","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:42.800829+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 is starting: a6863dc6efa519b2bda56ba84a41a1ca38fccc63bfb6107a80e711ee861be61d","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:43.030167+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 started in PT0.265014S","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:52.535542+07:00","@version":"1","message":"Starting EventMonitoringITSpec using Java 21.0.7 with PID 55181 (started by thanhtungvu in /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:52.536565+07:00","@version":"1","message":"The following 1 profile is active: \"test\"","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:52.972046+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:53.079282+07:00","@version":"1","message":"Using LocalStack credentials for local environment","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:53.080633+07:00","@version":"1","message":"Configuring S3 client for local environment with endpoint: http://localhost:56501 and region: ap-northeast-1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:53.104098+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:53.149951+07:00","@version":"1","message":"Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)","logger_name":"org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration","thread_name":"Test worker","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:53.324556+07:00","@version":"1","message":"Started EventMonitoringITSpec in 0.943 seconds (process running for 14.549)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.356182+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.356652+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.393317+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/AccessCtrl.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.433426+07:00","@version":"1","message":"Added contract address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.496944+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7841d626], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.497329+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.497505+07:00","@version":"1","message":"Event: RoleAdminChanged, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15ea267, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@48227f92, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6cebd103], Signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.497622+07:00","@version":"1","message":"Parsed event: RoleAdminChanged with signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.497831+07:00","@version":"1","message":"Event: RoleGranted, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ed41eee, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35d13249, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7809e39], Signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.497939+07:00","@version":"1","message":"Parsed event: RoleGranted with signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.49807+07:00","@version":"1","message":"Event: RoleRevoked, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73cb9bad, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3da6d854, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@159e6d3], Signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.498173+07:00","@version":"1","message":"Parsed event: RoleRevoked with signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.498266+07:00","@version":"1","message":"Successfully parsed ABI with 4 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.498357+07:00","@version":"1","message":"Registered events for contract: AccessCtrl at address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.498445+07:00","@version":"1","message":"ABI file processed: address=0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4, contract_name=AccessCtrl, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.499033+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.508646+07:00","@version":"1","message":"Added contract address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.512034+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5b1cde0a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@19f5df9d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@556adfac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45ff6c5e], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.512168+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.5123+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@782e0665, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f1037ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c84163b], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.5124+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.51253+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72a71e69, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ab23376, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34d2e58d], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.512632+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.512836+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@121511cc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7957a97b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5a0ac551], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.512961+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.519757+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.521326+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.521848+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@58fdc418, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@64d9ecac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35da72fa], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.521976+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.523268+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.523469+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@40e5536d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6b726b51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@dd98967, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ff0b768, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65e4e3c0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d3434ce], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.523589+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.523735+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ce714be], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.523833+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.524013+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@149eb04a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28b07453, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c96d71f], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.524113+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.524201+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.524279+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.52435+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=2","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.524576+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/FinancialZoneAccount.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.532691+07:00","@version":"1","message":"Added contract address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.53469+07:00","@version":"1","message":"Processing nested tuple with 6 components","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538157+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538299+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538542+07:00","@version":"1","message":"Event: AddAccountLimit, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ff369a7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34c43638, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55fd5bae], Signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538644+07:00","@version":"1","message":"Parsed event: AddAccountLimit with signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538774+07:00","@version":"1","message":"Event: AddCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@68c62048, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c2cc2a6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@294c46ed, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4fceeea3], Signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.538877+07:00","@version":"1","message":"Parsed event: AddCumulativeAmount with signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539001+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15ad2c0e], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539089+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.53922+07:00","@version":"1","message":"Event: SubtractCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5e9d7b78, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@520021aa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a8376bf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1632fabd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23c115b8], Signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539322+07:00","@version":"1","message":"Parsed event: SubtractCumulativeAmount with signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539452+07:00","@version":"1","message":"Event: SyncBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@701f6d1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d8cb29, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3e3ebd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b7987d7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@20054016, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@674346bc], Signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539555+07:00","@version":"1","message":"Parsed event: SyncBurn with signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539686+07:00","@version":"1","message":"Event: SyncCharge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1679473f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d93d18, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bd6d6c5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65d19a5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ac1e6ee, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62c83153], Signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539786+07:00","@version":"1","message":"Parsed event: SyncCharge with signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539899+07:00","@version":"1","message":"Event: SyncCumulativeReset, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2fe5b331, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@419b0759], Signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.539987+07:00","@version":"1","message":"Parsed event: SyncCumulativeReset with signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540112+07:00","@version":"1","message":"Event: SyncDischarge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@14b86f12, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3759967f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35a7d79e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c9fafbc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b0895c2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@191eb2], Signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540214+07:00","@version":"1","message":"Parsed event: SyncDischarge with signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540336+07:00","@version":"1","message":"Event: SyncMint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b893208, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5aba9e8a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6523a69a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2d87d802, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6fdca6c8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@619c3b77], Signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540436+07:00","@version":"1","message":"Parsed event: SyncMint with signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540563+07:00","@version":"1","message":"Event: SyncTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197346a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@aa7ca4c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47d736, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c55364, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37840cfa], Signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540662+07:00","@version":"1","message":"Parsed event: SyncTransfer with signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.540987+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.541135+07:00","@version":"1","message":"Registered events for contract: FinancialZoneAccount at address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.541219+07:00","@version":"1","message":"ABI file processed: address=0xf908c90f27013e2e85eb6af516f7363c674bbec3, contract_name=FinancialZoneAccount, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.541372+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.550313+07:00","@version":"1","message":"Added contract address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552118+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bf1321d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7df2624f], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552239+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552366+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@176413e2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3fad9d22, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@d7e2110, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@63f0caea], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552466+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552585+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@543c2e75, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72bfd5d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b213a09], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552679+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552805+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ee3181d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b99c232, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73800309], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.552898+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553007+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1838e02], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553091+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.55321+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7264ddf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62cb975b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11204840], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553304+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553421+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c4e27f8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3933cb1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77ce229], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553511+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553628+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@140dd8ce, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69815c50, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ecd2968], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553721+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553841+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ec0e732, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@54f1818c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37f1249e], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.553959+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.55404+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.554122+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.554196+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.554332+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.561313+07:00","@version":"1","message":"Added contract address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.564708+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44962663, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a22ef09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@625a21ac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@497efdbf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3cae6fcd], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.564883+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565034+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11d995f6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd8120, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c9013cd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@530fd795, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ea49e27], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565134+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565291+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@40deaa52, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5cccc7ff, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d2108c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5baded37, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6e14502a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@545d9128, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35eaad5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11a3a80], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565409+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565817+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d43af89, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75afb3d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@154f7867, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45519e74, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15be4eb0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d071eac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7365d12b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a0e530d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5da25132], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.565952+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.5664+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7875d654, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2544bcf8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d9a0566, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22fef226, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74ef575b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74cc8642, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49401c88], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.566534+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.566663+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c7aa844], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.566754+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.566888+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1221611f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24f1b8f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b22e312, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2534aba, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1be3ea76, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@610a19e6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3dec5ca6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25f2d73b], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.566996+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.56712+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@214629a9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10b2d14e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f64a692, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c1ceb4a], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.567235+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.567361+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c878e01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ab90a7e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@83718d], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.56746+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.570721+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.571112+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69b1e76b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7225ecc9], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.571222+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.57151+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.571667+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.571772+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.571899+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Validator.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.581371+07:00","@version":"1","message":"Added contract address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.583352+07:00","@version":"1","message":"Event: AddAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c3bdbe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6948f680, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51f95ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5c894a01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72db5be6], Signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.583491+07:00","@version":"1","message":"Parsed event: AddAccount with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.583615+07:00","@version":"1","message":"Event: AddValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b1321b2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9a59dcc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fea17bb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37467bcb], Signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.58371+07:00","@version":"1","message":"Parsed event: AddValidator with signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.583827+07:00","@version":"1","message":"Event: AddValidatorAccountId, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25cac220, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2470a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f3cf8d8], Signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.58392+07:00","@version":"1","message":"Parsed event: AddValidatorAccountId with signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584041+07:00","@version":"1","message":"Event: AddValidatorRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b205ad7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38884a51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b8adcd8], Signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584146+07:00","@version":"1","message":"Parsed event: AddValidatorRole with signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584256+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33580d49], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584342+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584625+07:00","@version":"1","message":"Event: ModValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7ae70f17, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4708d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4de66814], Signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584739+07:00","@version":"1","message":"Parsed event: ModValidator with signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.584865+07:00","@version":"1","message":"Event: SetBizZoneTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ea716a4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7cfa39d0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4443ef5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a9b890b], Signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.58497+07:00","@version":"1","message":"Parsed event: SetBizZoneTerminated with signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585092+07:00","@version":"1","message":"Event: SetTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c8faaca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4af40c48, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2cc107cc], Signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585195+07:00","@version":"1","message":"Parsed event: SetTerminated with signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585324+07:00","@version":"1","message":"Event: SyncAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6d593164, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d6e0b4d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2712d8e4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34954ad, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@27bf97b1, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1ab6c468, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b52ea22, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62f73fd9], Signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585429+07:00","@version":"1","message":"Parsed event: SyncAccount with signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585543+07:00","@version":"1","message":"Event: ValidatorEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@392e2e29, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@151728e0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@29a8d39a], Signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585643+07:00","@version":"1","message":"Parsed event: ValidatorEnabled with signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585721+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585791+07:00","@version":"1","message":"Registered events for contract: Validator at address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585859+07:00","@version":"1","message":"ABI file processed: address=0xfdc1a198656d23b75d15c3d37194ad5fabf17081, contract_name=Validator, last_modified=Tue Jul 29 11:20:53 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.585978+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.586044+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.591948+07:00","@version":"1","message":"Created new DynamoDB connection","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.67784+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.678094+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.706236+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:54.707469+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.711014+07:00","@version":"1","message":"Block 1000 is delayed by more than 2 seconds","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.712288+07:00","@version":"1","message":"Event found tx_hash=0xabc222134","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.712413+07:00","@version":"1","message":"Looking for event with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac for address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.7125+07:00","@version":"1","message":"Available signatures for address 0xfdc1a198656d23b75d15c3d37194ad5fabf17081: [0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004, 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498, 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f, 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707, 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52, 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797, 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88, 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32, 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac, 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.712595+07:00","@version":"1","message":"Found matching event for signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.733849+07:00","@version":"1","message":"Event parsed tx_hash=0xabc222134, name=AddAccount","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.734061+07:00","@version":"1","message":"detect block includes events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.739498+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.816727+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.817009+07:00","@version":"1","message":"Success to register event","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.817255+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.866269+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.866592+07:00","@version":"1","message":"Success to register block number","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T11:20:55.866675+07:00","@version":"1","message":"Success to process new transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:42.986067+07:00","@version":"1","message":"Image name substitution will be performed by: DefaultImageNameSubstitutor (composite of 'ConfigurationFileImageNameSubstitutor' and 'PrefixingImageNameSubstitutor')","logger_name":"org.testcontainers.utility.ImageNameSubstitutor","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.071926+07:00","@version":"1","message":"Loaded org.testcontainers.dockerclient.UnixSocketClientProviderStrategy from ~/.testcontainers.properties, will try it first","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.359066+07:00","@version":"1","message":"Found Docker environment with local Unix socket (unix:///var/run/docker.sock)","logger_name":"org.testcontainers.dockerclient.DockerClientProviderStrategy","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.360083+07:00","@version":"1","message":"Docker host IP address is localhost","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.366943+07:00","@version":"1","message":"Connected to docker: \n  Server Version: 27.4.0\n  API Version: 1.47\n  Operating System: Docker Desktop\n  Total Memory: 7837 MB","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.367224+07:00","@version":"1","message":"Ryuk started - will monitor and terminate Testcontainers containers on JVM exit","logger_name":"org.testcontainers.utility.RyukResourceReaper","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.367369+07:00","@version":"1","message":"Checking the system...","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.367842+07:00","@version":"1","message":"✔︎ Docker server version should be at least 1.6.0","logger_name":"org.testcontainers.DockerClientFactory","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.376895+07:00","@version":"1","message":"Creating container for image: testcontainers/ryuk:0.3.4","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:43.569481+07:00","@version":"1","message":"Credential helper/store (docker-credential-desktop) does not have credentials for https://index.docker.io/v1/","logger_name":"org.testcontainers.utility.RegistryAuthLocator","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:45.15649+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 is starting: d4ed8411bc69af95a1212016a001baad512b54cd47c5308d8e83a94e033ad711","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:45.65896+07:00","@version":"1","message":"Container testcontainers/ryuk:0.3.4 started in PT2.290509S","logger_name":"\uD83D\uDC33 [testcontainers/ryuk:0.3.4]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:45.663562+07:00","@version":"1","message":"Preemptively checking local images for 'localstack/localstack:3.0.2', referenced via a compose file or transitive Dockerfile. If not available, it will be pulled.","logger_name":"org.testcontainers.containers.DockerComposeContainer","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:45.667942+07:00","@version":"1","message":"Creating container for image: docker/compose:1.29.2","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:45.891171+07:00","@version":"1","message":"Container docker/compose:1.29.2 is starting: cc9c4720086fdaae7b9ecda42b0b26a419be752bf7b61a9733253c8bd6730c3b","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.131163+07:00","@version":"1","message":"Container docker/compose:1.29.2 started in PT1.463212S","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.134487+07:00","@version":"1","message":"Docker Compose container is running for command: up -d","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.138026+07:00","@version":"1","message":"STDERR: Creating network \"smrvg5qmpzlu_default\" with the default driver","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1916500678","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.139073+07:00","@version":"1","message":"Docker Compose has finished running","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.139244+07:00","@version":"1","message":"STDERR: Creating smrvg5qmpzlu_localstack_1 ... ","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1916500678","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.139609+07:00","@version":"1","message":"STDERR: Creating smrvg5qmpzlu_localstack_1 ... done","logger_name":"\uD83D\uDC33 [docker/compose:1.29.2]","thread_name":"docker-java-stream-1916500678","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.154314+07:00","@version":"1","message":"Creating container for image: alpine/socat:1.7.4.3-r0","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.190939+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 is starting: 097891aa2cced60b83fffd0a635dc5afc5719d224c0813fcc33d5e7d95ac7332","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:47.38832+07:00","@version":"1","message":"Container alpine/socat:1.7.4.3-r0 started in PT0.234143S","logger_name":"\uD83D\uDC33 [alpine/socat:1.7.4.3-r0]","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:53.931179+07:00","@version":"1","message":"Starting EventMonitoringITSpec using Java 21.0.7 with PID 67857 (started by thanhtungvu in /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:53.932003+07:00","@version":"1","message":"The following 1 profile is active: \"test\"","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.351332+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.451552+07:00","@version":"1","message":"Using LocalStack credentials for local environment","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.452741+07:00","@version":"1","message":"Configuring S3 client for local environment with endpoint: http://localhost:60203 and region: ap-northeast-1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.477183+07:00","@version":"1","message":"Log level set to DEBUG based on environment: local","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.526712+07:00","@version":"1","message":"Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)","logger_name":"org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration","thread_name":"Test worker","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:54.701856+07:00","@version":"1","message":"Started EventMonitoringITSpec in 0.923 seconds (process running for 13.443)","logger_name":"adhoc.event_monitoring.EventMonitoringITSpec","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.573956+07:00","@version":"1","message":"Starting bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.574207+07:00","@version":"1","message":"downloading abi files... bucket_name=abijson-local-bucket","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.58871+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/AccessCtrl.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.623276+07:00","@version":"1","message":"Added contract address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.684843+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10c14d1e], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.685258+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.68544+07:00","@version":"1","message":"Event: RoleAdminChanged, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@36fe7b03, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@172ed66d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ac57130], Signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.685568+07:00","@version":"1","message":"Parsed event: RoleAdminChanged with signature: 0xbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.685776+07:00","@version":"1","message":"Event: RoleGranted, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@751523a5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@14691ed4, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15a4a198], Signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.685891+07:00","@version":"1","message":"Parsed event: RoleGranted with signature: 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.686112+07:00","@version":"1","message":"Event: RoleRevoked, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16576bcf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4a037aab, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@16c04281], Signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.686239+07:00","@version":"1","message":"Parsed event: RoleRevoked with signature: 0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.686338+07:00","@version":"1","message":"Successfully parsed ABI with 4 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.686442+07:00","@version":"1","message":"Registered events for contract: AccessCtrl at address: 0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.686525+07:00","@version":"1","message":"ABI file processed: address=0xf27289e45825f7e8f3eae5c3f52e05c8fb6fd3d4, contract_name=AccessCtrl, last_modified=Tue Jul 29 13:45:54 ICT 2025, events=1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.687157+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.694972+07:00","@version":"1","message":"Added contract address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.698516+07:00","@version":"1","message":"Event: AccountEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@597fa24f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7841d626, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15ea267, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@48227f92], Signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.69866+07:00","@version":"1","message":"Parsed event: AccountEnabled with signature: 0xe74d5ae3bfd2f3d16c625089cf683075de8355498359a4d2203a5c8c35273c72","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.698794+07:00","@version":"1","message":"Event: AccountTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6cebd103, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1a6dca5e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ed41eee], Signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.698888+07:00","@version":"1","message":"Parsed event: AccountTerminated with signature: 0x56069918d0bb596c54225b7d0bf24da7ba0563f28bd3b1eb7b4e30fe87b623d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.699139+07:00","@version":"1","message":"Event: AddAccountRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35d13249, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7809e39, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73cb9bad], Signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.699245+07:00","@version":"1","message":"Parsed event: AddAccountRole with signature: 0x001be3caf66ac15f7394fa7523e60730949d9ad02d0b5b5c3ba33184aa4909af","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.699605+07:00","@version":"1","message":"Event: AddZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@159e6d3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd197, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2d12248c], Signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.699731+07:00","@version":"1","message":"Parsed event: AddZone with signature: 0xab39ff20d5ec5f76ed779b309296a45e49f7d7fc5ce62a33195c8f79a15ea38f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.706472+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.708106+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.708618+07:00","@version":"1","message":"Event: AfterBalance, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@fffc91d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@61a6ac8a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4dd02f41], Signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.70874+07:00","@version":"1","message":"Parsed event: AfterBalance with signature: 0xf33c864590f74fd8a48c0b97b660bb38dd6dd49ea468e4f95b58717b8f4e64a5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710027+07:00","@version":"1","message":"Extracted 2 components from tuple type tuple[]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710227+07:00","@version":"1","message":"Event: ForceBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@651499d0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3102ab04, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7de43042, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@79d92b35, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38c2e7c7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c3c2150], Signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710349+07:00","@version":"1","message":"Parsed event: ForceBurn with signature: 0x2119d5ebb2182bb11efbe63aa029d6eaf9a22de76956dfefd5216f4e102d4836","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710492+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@408664b5], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710587+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710759+07:00","@version":"1","message":"Event: ModAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fe5521d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@222ede10, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@517eb52c], Signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710876+07:00","@version":"1","message":"Parsed event: ModAccount with signature: 0xf9295998204ca117845fc546f231b97d3f3db7fe59f05ed7aa5aa958573b445a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.710958+07:00","@version":"1","message":"Successfully parsed ABI with 8 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.711042+07:00","@version":"1","message":"Registered events for contract: Account at address: 0x993366a606a99129e56b4b99b27e428ba1cb672f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.711119+07:00","@version":"1","message":"ABI file processed: address=0x993366a606a99129e56b4b99b27e428ba1cb672f, contract_name=Account, last_modified=Tue Jul 29 13:45:54 ICT 2025, events=2","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.71126+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/FinancialZoneAccount.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.718848+07:00","@version":"1","message":"Added contract address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.720827+07:00","@version":"1","message":"Processing nested tuple with 6 components","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.724656+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.72481+07:00","@version":"1","message":"Extracted 6 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725074+07:00","@version":"1","message":"Event: AddAccountLimit, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35da72fa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@760e84b0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@40e5536d], Signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.72518+07:00","@version":"1","message":"Parsed event: AddAccountLimit with signature: 0xc6eb9b2da13d5af8dd4d727ec95f6a2b4d5c945cda2c69d8f2b5502870913b30","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725321+07:00","@version":"1","message":"Event: AddCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6b726b51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@dd98967, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ff0b768, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65e4e3c0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d3434ce], Signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725427+07:00","@version":"1","message":"Parsed event: AddCumulativeAmount with signature: 0x8e39dd2b827308cfd30abe15b37ee87bc60309eae1e920d120f7180fdff061fe","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725548+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5ce714be], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725636+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725767+07:00","@version":"1","message":"Event: SubtractCumulativeAmount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@56a6a3bd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@149eb04a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@28b07453, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4c96d71f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2f4e2e7b], Signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.725873+07:00","@version":"1","message":"Parsed event: SubtractCumulativeAmount with signature: 0xdc46725bd76575feef20eb7908815d8092aefa135cb45f6cd5cadfe65345e963","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726003+07:00","@version":"1","message":"Event: SyncBurn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4e33d738, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@658a2041, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1f5e3dc0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@413df5a6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@116760ae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@255bd2eb], Signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726105+07:00","@version":"1","message":"Parsed event: SyncBurn with signature: 0xb492364ef652b9038e2090e73f7e56d4f29c48116be128cf7a5dd9362bfe238d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726234+07:00","@version":"1","message":"Event: SyncCharge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b055bbd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@653b17be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ff369a7, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@34c43638, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@55fd5bae, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@68c62048], Signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726337+07:00","@version":"1","message":"Parsed event: SyncCharge with signature: 0x87f4784e8d6636f6a28d29d39db7ac6d677360cfaa03875f98ed5ea109f579ef","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726523+07:00","@version":"1","message":"Event: SyncCumulativeReset, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c2cc2a6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@294c46ed], Signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726613+07:00","@version":"1","message":"Parsed event: SyncCumulativeReset with signature: 0x4786011417e1c57409dd38f166a5cf10fd92ccf40affa4e066fddf0337a890e1","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726747+07:00","@version":"1","message":"Event: SyncDischarge, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4fceeea3, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15ad2c0e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5e9d7b78, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@520021aa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6a8376bf], Signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.726865+07:00","@version":"1","message":"Parsed event: SyncDischarge with signature: 0x042b000917e8859093fd96b57d9393554aa0ba02cbda9e1b8906ad415fed2f57","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727029+07:00","@version":"1","message":"Event: SyncMint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1632fabd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@23c115b8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@701f6d1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d8cb29, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3e3ebd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2b7987d7], Signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727182+07:00","@version":"1","message":"Parsed event: SyncMint with signature: 0x22693602dbd99f3110a174efd07b1bd0ec77c661a7e3910f76cb32bcbfd3c8e3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.72734+07:00","@version":"1","message":"Event: SyncTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@20054016, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@674346bc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1679473f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d93d18, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7bd6d6c5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@65d19a5], Signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727467+07:00","@version":"1","message":"Parsed event: SyncTransfer with signature: 0xbbdb83476a7a67d22ce1c7cefcfaa556a06d99334cfbe24f0e9aa0a69108836a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727557+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727639+07:00","@version":"1","message":"Registered events for contract: FinancialZoneAccount at address: 0xf908c90f27013e2e85eb6af516f7363c674bbec3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727714+07:00","@version":"1","message":"ABI file processed: address=0xf908c90f27013e2e85eb6af516f7363c674bbec3, contract_name=FinancialZoneAccount, last_modified=Tue Jul 29 13:45:55 ICT 2025, events=3","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.727861+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.73484+07:00","@version":"1","message":"Added contract address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.736476+07:00","@version":"1","message":"Event: AddBizZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7b0895c2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@191eb2], Signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.736589+07:00","@version":"1","message":"Parsed event: AddBizZone with signature: 0xe2b301fad6ecbeb8a9a7058c47c0079f0c7fe0aed5219e9eaaa89a57465ed20c","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.736713+07:00","@version":"1","message":"Event: AddProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b893208, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5aba9e8a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6523a69a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2d87d802], Signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.73681+07:00","@version":"1","message":"Parsed event: AddProvider with signature: 0xc532225a3cf08364cd2bd951477654600c1f86fa028eac72dbc9d2d2f283b3da","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.736934+07:00","@version":"1","message":"Event: AddProviderRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6fdca6c8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@619c3b77, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197346a8], Signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737027+07:00","@version":"1","message":"Parsed event: AddProviderRole with signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737138+07:00","@version":"1","message":"Event: AddTokenByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@aa7ca4c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@47d736, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3ca], Signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737233+07:00","@version":"1","message":"Parsed event: AddTokenByProvider with signature: 0x7435e834bdacb3f501fdae3d8d3cf47cc1c35109703aee37db666b2791265b49","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737348+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3c55364], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737431+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737548+07:00","@version":"1","message":"Event: ModProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37840cfa, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9c4835b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@95acffa], Signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737642+07:00","@version":"1","message":"Parsed event: ModProvider with signature: 0x473409b6a2cd89bfc2af59ec233a86cf77bf91a928eacc08653f90a1eeaf0086","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737758+07:00","@version":"1","message":"Event: ModZone, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@202df3c6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3759d8e5, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1aaa5b0e], Signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737849+07:00","@version":"1","message":"Parsed event: ModZone with signature: 0x64d970091a4c8c5acfd400dffce52e1d45dc29cda80aaff08e1bc7cab4a3d14e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.737965+07:00","@version":"1","message":"Event: ProviderEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45d062be, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@aac1a01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73cb6541], Signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738056+07:00","@version":"1","message":"Parsed event: ProviderEnabled with signature: 0xe0b85c1c3d6d9b5ff532d1af7833a03c696f30301ad57c53eeb988f1dbe4fd4b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738181+07:00","@version":"1","message":"Event: SetTokenIdByProvider, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3bf1321d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7df2624f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@176413e2], Signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.73829+07:00","@version":"1","message":"Parsed event: SetTokenIdByProvider with signature: 0x1ffbdd794cc8378ba10cc92817895e2d09814d0d5412b9458c10546d2d6a792e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738368+07:00","@version":"1","message":"Successfully parsed ABI with 9 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738437+07:00","@version":"1","message":"Registered events for contract: Provider at address: 0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738505+07:00","@version":"1","message":"ABI file processed: address=0xb0bdd71bdb22b3d0b3b6dd8c47df0f3c658ea22a, contract_name=Provider, last_modified=Tue Jul 29 13:45:55 ICT 2025, events=4","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.738643+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.745489+07:00","@version":"1","message":"Added contract address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.747515+07:00","@version":"1","message":"Event: AddToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@73800309, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1838e02, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7264ddf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@62cb975b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11204840], Signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.747673+07:00","@version":"1","message":"Parsed event: AddToken with signature: 0x2562f1b456390e0b383cfccf5a9873489a4de59bb4e8b11572864303d83b4d9b","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.747821+07:00","@version":"1","message":"Event: Approval, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@c4e27f8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3933cb1b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@77ce229, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@140dd8ce, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69815c50], Signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.747927+07:00","@version":"1","message":"Parsed event: Approval with signature: 0xb26ed3809e479f0f78d8d4eade6088e08c3c5f26cd079d185047e3330051804e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748091+07:00","@version":"1","message":"Event: Burn, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4ecd2968, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2ec0e732, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@54f1818c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37f1249e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1050dd61, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3980412e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@61254a73, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5fce571d], Signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748212+07:00","@version":"1","message":"Parsed event: Burn with signature: 0x5b9ed5e56901c5a11f230930840d8e91ec9b249cf3b711625be317af70f4623d","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748356+07:00","@version":"1","message":"Event: BurnCancel, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@49632250, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72518363, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@39ec2988, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1437f717, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@44962663, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a22ef09, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@625a21ac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@497efdbf, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3cae6fcd], Signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748472+07:00","@version":"1","message":"Parsed event: BurnCancel with signature: 0x1ef5ffb2607ba5927b4399b80dd60c0c29e0bfd1e8cd7f9241af29294611e8d9","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748604+07:00","@version":"1","message":"Event: CustomTransfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11d995f6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22bd8120, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1c9013cd, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@530fd795, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@ea49e27, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@40deaa52, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5cccc7ff], Signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748712+07:00","@version":"1","message":"Parsed event: CustomTransfer with signature: 0xee384c64fdbcd95cd4bd55344bb40fa42d26e8eb93310760bd423f4f2000cf33","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748821+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@71d2108c], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.748909+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749044+07:00","@version":"1","message":"Event: Mint, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5baded37, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6e14502a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@545d9128, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@35eaad5f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@11a3a80, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4d43af89, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@75afb3d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@154f7867], Signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749158+07:00","@version":"1","message":"Parsed event: Mint with signature: 0x76b9c7a3835735ef9dda1339db014a67709b7f8b9329b842512bbe0fc7a39b97","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749282+07:00","@version":"1","message":"Event: ModToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@45519e74, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@15be4eb0, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d071eac, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7365d12b], Signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749382+07:00","@version":"1","message":"Parsed event: ModToken with signature: 0x31b8275492fd03c8549f248f3ac91b83aeb776283ea6aa2dc5ddf8b6f11b9357","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749503+07:00","@version":"1","message":"Event: SetEnabledToken, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3a0e530d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5da25132, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7875d654], Signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.749596+07:00","@version":"1","message":"Parsed event: SetEnabledToken with signature: 0xc30c58b4605484f7c31c925e7821a43689bc6ebf0471a547a8425bc378b45698","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.752577+07:00","@version":"1","message":"Extracted 17 components from tuple type tuple","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.752873+07:00","@version":"1","message":"Event: Transfer, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@22fef226, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@74ef575b], Signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.752976+07:00","@version":"1","message":"Parsed event: Transfer with signature: 0xc8974cc09b477d38b5d6f836a0140699a0d886047d2ec9442b4510f6e0161120","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.753061+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.753134+07:00","@version":"1","message":"Registered events for contract: Token at address: 0x88eea3e4f0839b74a8de27951bc630126837d646","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.753215+07:00","@version":"1","message":"ABI file processed: address=0x88eea3e4f0839b74a8de27951bc630126837d646, contract_name=Token, last_modified=Tue Jul 29 13:45:54 ICT 2025, events=5","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.753354+07:00","@version":"1","message":"getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Validator.json","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.761135+07:00","@version":"1","message":"Added contract address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.763173+07:00","@version":"1","message":"Event: AddAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@24f1b8f, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b22e312, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2534aba, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1be3ea76, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@610a19e6, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@3dec5ca6], Signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.763314+07:00","@version":"1","message":"Parsed event: AddAccount with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.76344+07:00","@version":"1","message":"Event: AddValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25f2d73b, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@214629a9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@10b2d14e, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f64a692], Signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.76354+07:00","@version":"1","message":"Parsed event: AddValidator with signature: 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.763665+07:00","@version":"1","message":"Event: AddValidatorAccountId, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7c1ceb4a, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6c878e01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6ab90a7e], Signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.763764+07:00","@version":"1","message":"Parsed event: AddValidatorAccountId with signature: 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.76407+07:00","@version":"1","message":"Event: AddValidatorRole, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@83718d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b5e9c6c, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b79ee80], Signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.764442+07:00","@version":"1","message":"Parsed event: AddValidatorRole with signature: 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.764607+07:00","@version":"1","message":"Event: Initialized, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@69b1e76b], Signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.764722+07:00","@version":"1","message":"Parsed event: Initialized with signature: 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.764875+07:00","@version":"1","message":"Event: ModValidator, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7225ecc9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1d77a31d, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@197e53dd], Signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.764981+07:00","@version":"1","message":"Parsed event: ModValidator with signature: 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.765427+07:00","@version":"1","message":"Event: SetBizZoneTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@7f927526, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@228c8db9, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2c3bdbe, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@6948f680], Signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.765743+07:00","@version":"1","message":"Parsed event: SetBizZoneTerminated with signature: 0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.766088+07:00","@version":"1","message":"Event: SetTerminated, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@51f95ca, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@********, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@5c894a01, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@72db5be6], Signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.766424+07:00","@version":"1","message":"Parsed event: SetTerminated with signature: 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.766591+07:00","@version":"1","message":"Event: SyncAccount, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@b1321b2, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@9a59dcc, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1fea17bb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@37467bcb, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@25cac220, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@2470a8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4f3cf8d8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@4b205ad7], Signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.766858+07:00","@version":"1","message":"Parsed event: SyncAccount with signature: 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.76717+07:00","@version":"1","message":"Event: ValidatorEnabled, Parameters: [com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@38884a51, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@1b8adcd8, com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser$AbiEventInput@33580d49], Signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.767404+07:00","@version":"1","message":"Parsed event: ValidatorEnabled with signature: 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.767625+07:00","@version":"1","message":"Successfully parsed ABI with 10 events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.767698+07:00","@version":"1","message":"Registered events for contract: Validator at address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.76777+07:00","@version":"1","message":"ABI file processed: address=0xfdc1a198656d23b75d15c3d37194ad5fabf17081, contract_name=Validator, last_modified=Tue Jul 29 13:45:55 ICT 2025, events=6","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.768028+07:00","@version":"1","message":"Started bc monitoring","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.768098+07:00","@version":"1","message":"Monitoring events...","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.773148+07:00","@version":"1","message":"Created new DynamoDB connection","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.848729+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.848996+07:00","@version":"1","message":"Get blockheight: 0","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.88375+07:00","@version":"1","message":"Retrieved 0 logs from block height 1 to latest","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:55.886909+07:00","@version":"1","message":"Success to process pending transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.883145+07:00","@version":"1","message":"Block 1000 is delayed by more than 2 seconds","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"WARN","level_value":30000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.884362+07:00","@version":"1","message":"Event found tx_hash=0xabc222134","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.884486+07:00","@version":"1","message":"Looking for event with signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac for address: 0xfdc1a198656d23b75d15c3d37194ad5fabf17081","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.884568+07:00","@version":"1","message":"Available signatures for address 0xfdc1a198656d23b75d15c3d37194ad5fabf17081: [0x292bd4662cb403dae521dfa9be3afca62586286baf44a31370f327e7d6ad4004, 0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498, 0x572282c63ddb1faf44b9e289fa0d653bbd93807650da941fceb79431dc84028f, 0xb2cb81d13b4c557e241e59f881a5549c732d127a0ddfd89d4b2eb04d006ab707, 0x43bcfaa521d1f1a331db58aad718c19ed7b21b55b271f770dfb990c725d8ca52, 0x70eab42575c8fe4051e6545cb4c13c230cff8a4a9b9a8501092dd1e7ef1b8797, 0x9ecba8037b9304a32aa67c6e8b221528abe766633da2bfe288c587d0cbff3f88, 0x7399dbd1330031a120f6285e7369825f87f7d3a731eb5d3083abdd7621869c32, 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac, 0x44ad9d0e2eee6c28ee3dfb44c44957a09a674b083bc15b5539a9b4f60642e26e]","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.884654+07:00","@version":"1","message":"Found matching event for signature: 0x5133974344f36360c7792994012cce4d92c09edaac81309ff153fdddf161e2ac","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.906119+07:00","@version":"1","message":"Event parsed tx_hash=0xabc222134, name=AddAccount","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.906324+07:00","@version":"1","message":"detect block includes events","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"pool-1-thread-1","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:56.911568+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.287068+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.287381+07:00","@version":"1","message":"Success to register event","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.287748+07:00","@version":"1","message":"Reused existing DynamoDB connection from pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.342505+07:00","@version":"1","message":"Returned DynamoDB connection to pool","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.342821+07:00","@version":"1","message":"Success to register block number","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-29T13:45:57.342896+07:00","@version":"1","message":"Success to process new transactions","logger_name":"com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService","thread_name":"Test worker","level":"INFO","level_value":20000,"application":"bcmonitoring"}
