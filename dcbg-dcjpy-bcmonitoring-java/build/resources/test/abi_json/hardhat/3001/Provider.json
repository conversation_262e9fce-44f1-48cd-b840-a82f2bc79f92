{"address": "0x2cE44AB07487A4021FBea8c8C2F0354D6Bce08C7", "abi": [{"type": "event", "anonymous": false, "name": "AddBizZone", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddProviderRole", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "address", "name": "providerEoa", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddTokenByProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "tokenId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModZone", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ProviderEnabled", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetTokenIdByProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "tokenId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "ROLE_PREFIX_PROV", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addBizZone", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addProvider", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addProviderRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "address", "name": "providerEoa"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "providerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getProviderAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "tuple", "name": "provider", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}]}, {"type": "function", "name": "getProviderCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getTokenId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZoneName", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}], "outputs": [{"type": "string", "name": "zoneName"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "providerId"}, {"type": "bool", "name": "chkEnabled"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "mod<PERSON><PERSON>ider", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modZone", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setProviderAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "provider", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xe84249a249910f49fd18d8e0e0c56d4b2c7309738f8c2e4ee7d863d6374a0d22", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0x2cE44AB07487A4021FBea8c8C2F0354D6Bce08C7", "transactionIndex": 0, "gasUsed": "3472874", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x3fbd97d443b8d6758574af23c036747b5b8223c1e5e4fdbd0012a1750d38d965", "blockNumber": 623, "cumulativeGasUsed": "3472874", "status": 1}}