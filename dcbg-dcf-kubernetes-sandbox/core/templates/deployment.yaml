# newrelic-infra-agentとcoreのコンテナ定義をマージしてtemplateとして定義
# 環境ごとに変更がかかる可能性のある設定値はvaluesに外出しした
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core
  namespace: dcbg-core
  labels:
    app: core
spec:
  selector:
    matchLabels:
      app: core
  template:
    metadata:
      name: "core_pod"
      labels:
        app: core
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap-core-envs.yaml") . | sha256sum }}
    spec:
      shareProcessNamespace: true
      serviceAccountName: {{ .Values.serviceAccount.name }}
      containers:
        - name: core
          image: {{ .Values.application.image.name }}:{{ .Values.application.image.tag }}
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: core-envs
          env:
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: secrets-rds
                  key: username
            - name: DB_PASS
              valueFrom:
                secretKeyRef:
                  name: secrets-rds
                  key: password
          ports:
            - containerPort: {{ .Values.application.ports.containerPort }}
          resources:
            requests:
              cpu: {{ .Values.application.resources.requests.cpu }}
              memory: {{ .Values.application.resources.requests.memory }}
            limits:
              cpu: {{ .Values.application.resources.limits.cpu }}
              memory: {{ .Values.application.resources.limits.memory }}
        - name: newrelic-infrastructure
          image: {{ .Values.newrelic.imageName }}:{{ .Values.newrelic.imageTag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: NRIA_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  key: license-key
                  name: newrelic-license-key
            - name: NRIA_LOG_LEVEL
              value: "info"
            - name: NRIA_VERBOSE
              value: "0"
            - name: DISABLE_KUBE_STATE_METRICS
              value: "true"
            - name: CLUSTER_NAME
              value: {{ .Values.envName }}-eks-cluster
            - name: COMPUTE_TYPE
              value: serverless
            - name: NRK8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_DISPLAY_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_CUSTOM_ATTRIBUTES
              value: '{"clusterName":"$(CLUSTER_NAME)", "Environment":"{{ .Values.envName }}"}'
            - name: NRIA_PASSTHROUGH_ENVIRONMENT
              value: KUBERNETES_SERVICE_HOST,KUBERNETES_SERVICE_PORT,CLUSTER_NAME,CADVISOR_PORT,NRK8S_NODE_NAME,KUBE_STATE_METRICS_URL,KUBE_STATE_METRICS_POD_LABEL,TIMEOUT,ETCD_TLS_SECRET_NAME,ETCD_TLS_SECRET_NAMESPACE,API_SERVER_SECURE_PORT,KUBE_STATE_METRICS_SCHEME,KUBE_STATE_METRICS_PORT,SCHEDULER_ENDPOINT_URL,ETCD_ENDPOINT_URL,CONTROLLER_MANAGER_ENDPOINT_URL,API_SERVER_ENDPOINT_URL,DISABLE_KUBE_STATE_METRICS,DISCOVERY_CACHE_TTL
          resources:
            limits:
              cpu: {{ .Values.newrelic.resources.limits.cpu }}
              memory: {{ .Values.newrelic.resources.limits.memory }}
            requests:
              cpu: {{ .Values.newrelic.resources.requests.cpu }}
              memory: {{ .Values.newrelic.resources.requests.memory }}
