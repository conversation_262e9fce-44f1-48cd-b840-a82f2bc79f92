<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StructGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; <span class="el_source">StructGenerator.java</span></div><h1>StructGenerator.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.dynamic.loading.ClassLoadingStrategy;
import net.bytebuddy.implementation.MethodCall;
import org.web3j.abi.datatypes.*;

/** StructGenerator */
<span class="fc" id="L14">public class StructGenerator {</span>

  /**
   * Generate struct class from field types using ByteBuddy
   *
   * @param fieldTypes field types
   * @return the Dynamic Struct or Static Struct class
   * @throws Exception exception if any error occurs
   */
  public static Class&lt;? extends Type&lt;?&gt;&gt; generateStructClass(
      List&lt;Class&lt;? extends Type&lt;?&gt;&gt;&gt; fieldTypes) throws Exception {

<span class="fc" id="L26">    ByteBuddy byteBuddy = new ByteBuddy();</span>

    // Define constructor parameter types
<span class="fc" id="L29">    Class&lt;?&gt;[] paramTypes = fieldTypes.toArray(new Class&lt;?&gt;[0]);</span>
    Class&lt;? extends Type&lt;?&gt;&gt; structType =
<span class="fc bfc" id="L31" title="All 2 branches covered.">        fieldTypes.stream().anyMatch(StructGenerator::isDynamic)</span>
<span class="fc" id="L32">            ? DynamicStruct.class</span>
<span class="fc" id="L33">            : StaticStruct.class;</span>

<span class="fc" id="L35">    DynamicType.Builder&lt;? extends Type&lt;?&gt;&gt; structClass =</span>
        byteBuddy
<span class="fc" id="L37">            .subclass(structType)</span>
<span class="fc" id="L38">            .name(</span>
                &quot;com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.&quot;
<span class="fc" id="L40">                    + structType.getSimpleName()</span>
                    + &quot;_&quot;
<span class="fc" id="L42">                    + UUID.randomUUID().toString().replace(&quot;-&quot;, &quot;_&quot;));</span>

    // Define fields
<span class="fc bfc" id="L45" title="All 2 branches covered.">    for (int i = 0; i &lt; fieldTypes.size(); i++) {</span>
<span class="fc" id="L46">      structClass = structClass.defineField(&quot;field&quot; + i, fieldTypes.get(i), Modifier.PUBLIC);</span>
    }

<span class="fc" id="L49">    return structClass</span>
<span class="fc" id="L50">        .defineConstructor(Modifier.PUBLIC)</span>
<span class="fc" id="L51">        .withParameters(paramTypes)</span>
<span class="fc" id="L52">        .intercept(</span>
<span class="fc" id="L53">            MethodCall.invoke(structType.getConstructor(List.class))</span>
<span class="fc" id="L54">                .withMethodCall(</span>
<span class="fc" id="L55">                    MethodCall.invoke(Arrays.class.getMethod(&quot;asList&quot;, Object[].class))</span>
<span class="fc" id="L56">                        .withArgumentArray()))</span>
<span class="fc" id="L57">        .make()</span>
<span class="fc" id="L58">        .load(structType.getClassLoader(), ClassLoadingStrategy.Default.INJECTION)</span>
<span class="fc" id="L59">        .getLoaded();</span>
  }

  // reference from TypeDecoder#isDynamic
  public static boolean isDynamic(Class&lt;?&gt; parameter) {
<span class="fc bfc" id="L64" title="All 2 branches covered.">    return DynamicBytes.class.isAssignableFrom(parameter)</span>
<span class="fc bfc" id="L65" title="All 2 branches covered.">        || Utf8String.class.isAssignableFrom(parameter)</span>
<span class="fc bfc" id="L66" title="All 2 branches covered.">        || DynamicArray.class.isAssignableFrom(parameter)</span>
<span class="pc bpc" id="L67" title="1 of 2 branches missed.">        || DynamicStruct.class.isAssignableFrom(parameter);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>