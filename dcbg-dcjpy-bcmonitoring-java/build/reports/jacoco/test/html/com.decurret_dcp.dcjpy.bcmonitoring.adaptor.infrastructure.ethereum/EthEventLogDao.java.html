<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EthEventLogDao.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum</a> &gt; <span class="el_source">EthEventLogDao.java</span></div><h1>EthEventLogDao.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_ARRAY_TYPE;
import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_TYPE;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.disposables.Disposable;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.StaticStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterNumber;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.tx.Contract;

@Component
public class EthEventLogDao {
  private final LoggingService logger;
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;
  private Disposable subscription;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param log The logging service.
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      LoggingService log,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      AbiParser abiParser,
<span class="fc" id="L59">      ObjectMapper objectMapper) {</span>
<span class="fc" id="L60">    this.logger = log;</span>
<span class="fc" id="L61">    this.properties = properties;</span>
<span class="fc" id="L62">    this.web3jConfig = web3jConfig;</span>
<span class="fc" id="L63">    this.abiParser = abiParser;</span>
<span class="fc" id="L64">    this.objectMapper = objectMapper;</span>
<span class="fc" id="L65">  }</span>

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue&lt;Transaction&gt; subscribeAll() {
<span class="fc" id="L73">    BlockingQueue&lt;Transaction&gt; transactions = new LinkedBlockingQueue&lt;&gt;(Integer.MAX_VALUE);</span>

    // Check if the difference is valid
    int allowableDiff;
    try {
<span class="fc" id="L78">      allowableDiff =</span>
<span class="fc" id="L79">          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());</span>
<span class="fc" id="L80">    } catch (NumberFormatException e) {</span>
<span class="fc" id="L81">      logger.error(&quot;Failed to parse allowable timestamp difference&quot;, e);</span>
<span class="fc" id="L82">      return null;</span>
<span class="fc" id="L83">    }</span>

    try {
      // Create a new Web3j instance for this subscription
<span class="fc" id="L87">      Web3j web3j = web3jConfig.getWeb3j();</span>
      // Create a new Web3j instance for RPC API calls
<span class="fc" id="L89">      Web3j web3jCaller = web3jConfig.getWeb3jCaller();</span>
      // Subscribe to new blocks
<span class="fc" id="L91">      this.subscription =</span>
          web3j
<span class="fc" id="L93">              .newHeadsNotifications()</span>
<span class="fc" id="L94">              .subscribe(</span>
                  newHeadsNotification -&gt; {
                    try {
<span class="fc" id="L97">                      EthBlock.Block block =</span>
                          web3jCaller
<span class="fc" id="L99">                              .ethGetBlockByNumber(</span>
<span class="nc" id="L100">                                  () -&gt; newHeadsNotification.getParams().getResult().getNumber(),</span>
                                  true)
<span class="fc" id="L102">                              .send()</span>
<span class="fc" id="L103">                              .getBlock();</span>

<span class="fc" id="L105">                      BigInteger blockNumber = block.getNumber();</span>
<span class="fc bfc" id="L106" title="All 2 branches covered.">                      if (isDelayed(block, allowableDiff)) {</span>
<span class="fc" id="L107">                        logger.warn(</span>
                            &quot;Block {} is delayed by more than {} seconds&quot;,
                            blockNumber,
<span class="fc" id="L110">                            allowableDiff);</span>
                      }

                      // Check if block has no transactions
<span class="pc bpc" id="L114" title="1 of 4 branches missed.">                      if (block.getTransactions() == null || block.getTransactions().isEmpty()) {</span>
<span class="fc" id="L115">                        logger.info(&quot;Block {} has no transactions&quot;, blockNumber);</span>
<span class="fc" id="L116">                        return;</span>
                      }

                      // Process block transactions and events
<span class="fc" id="L120">                      List&lt;Event&gt; events = null;</span>
                      try {
<span class="fc" id="L122">                        events = convBlock2EventEntities(block);</span>
<span class="fc" id="L123">                      } catch (Exception e) {</span>
<span class="fc" id="L124">                        throw new RuntimeException(e);</span>
<span class="fc" id="L125">                      }</span>

                      // Always create transaction, even if events list is empty
<span class="pc bpc" id="L128" title="1 of 2 branches missed.">                      if (events != null) {</span>
<span class="fc bfc" id="L129" title="All 2 branches covered.">                        if (!events.isEmpty()) {</span>
<span class="fc" id="L130">                          logger.info(&quot;detect block includes events&quot;);</span>
                        }

                        BlockHeight blockHeight =
<span class="fc" id="L134">                            BlockHeight.builder().blockNumber(blockNumber.longValue()).build();</span>
                        Transaction transaction =
<span class="fc" id="L136">                            Transaction.builder().events(events).blockHeight(blockHeight).build();</span>
                        try {
<span class="fc" id="L138">                          transactions.put(transaction);</span>
<span class="nc" id="L139">                        } catch (InterruptedException e) {</span>
<span class="nc" id="L140">                          logger.error(&quot;Failed to put transaction&quot;, e);</span>
<span class="nc" id="L141">                          throw new RuntimeException(e);</span>
<span class="fc" id="L142">                        }</span>
                      }
<span class="fc" id="L144">                    } catch (Exception throwable) {</span>
<span class="fc" id="L145">                      logger.error(&quot;Error processing block&quot;, throwable);</span>
                      // Put error transaction to signal error condition
                      try {
<span class="fc" id="L148">                        transactions.put(</span>
<span class="fc" id="L149">                            Transaction.builder()</span>
<span class="fc" id="L150">                                .blockHeight(BlockHeight.builder().blockNumber(-1).build())</span>
<span class="fc" id="L151">                                .build());</span>
<span class="nc" id="L152">                      } catch (InterruptedException e) {</span>
<span class="nc" id="L153">                        logger.error(&quot;Failed to put error transaction&quot;, e);</span>
<span class="nc" id="L154">                        Thread.currentThread().interrupt();</span>
<span class="fc" id="L155">                      }</span>
<span class="fc" id="L156">                    }</span>
<span class="fc" id="L157">                  },</span>
                  error -&gt; {
<span class="fc" id="L159">                    logger.error(&quot;Subscription error&quot;, error);</span>
<span class="fc" id="L160">                    unsubscribe();</span>
<span class="fc" id="L161">                    web3jConfig.shutdownWeb3j();</span>
<span class="fc" id="L162">                    transactions.put(</span>
<span class="fc" id="L163">                        Transaction.builder()</span>
<span class="fc" id="L164">                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())</span>
<span class="fc" id="L165">                            .build());</span>
<span class="fc" id="L166">                  },</span>
<span class="fc" id="L167">                  () -&gt; logger.info(&quot;Subscription completed&quot;));</span>
<span class="fc" id="L168">      return transactions;</span>
<span class="fc" id="L169">    } catch (Exception e) {</span>
<span class="fc" id="L170">      logger.error(&quot;Failed to create Web3j subscription&quot;, e);</span>
<span class="fc" id="L171">      throw e;</span>
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
<span class="fc" id="L183">    long blockTimestamp = block.getTimestamp().longValue();</span>
<span class="fc" id="L184">    long currentTime = Instant.now().getEpochSecond();</span>
<span class="fc" id="L185">    long diff = currentTime - blockTimestamp;</span>

<span class="fc bfc" id="L187" title="All 2 branches covered.">    return diff &gt; allowableDiffSeconds;</span>
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List&lt;Event&gt; convBlock2EventEntities(EthBlock.Block block) throws Exception {
<span class="fc" id="L200">    List&lt;Event&gt; events = new ArrayList&lt;&gt;();</span>

    try {
      // Create a new Web3j instance for RPC API calls
<span class="fc" id="L204">      Web3j web3jCaller = web3jConfig.getWeb3jCaller();</span>

<span class="fc bfc" id="L206" title="All 2 branches covered.">      for (EthBlock.TransactionResult txResult : block.getTransactions()) {</span>
        // Return error if transaction is null
<span class="fc bfc" id="L208" title="All 2 branches covered.">        if (txResult.get() == null) {</span>
<span class="fc" id="L209">          throw new RuntimeException(&quot;Transaction is null&quot;);</span>
        }

        try {
<span class="fc" id="L213">          EthBlock.TransactionObject transaction = (EthBlock.TransactionObject) txResult.get();</span>
<span class="fc" id="L214">          String transactionHash = transaction.getHash();</span>

<span class="fc" id="L216">          EthGetTransactionReceipt receiptResponse =</span>
<span class="fc" id="L217">              web3jCaller.ethGetTransactionReceipt(transactionHash).send();</span>

<span class="fc" id="L219">          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);</span>
<span class="fc bfc" id="L220" title="All 2 branches covered.">          if (receipt == null) {</span>
<span class="fc" id="L221">            throw new RuntimeException(&quot;Transaction receipt is null&quot;);</span>
          }

<span class="fc bfc" id="L224" title="All 2 branches covered.">          for (Log log : receipt.getLogs()) {</span>
            try {
<span class="fc" id="L226">              logger.info(&quot;Event found tx_hash={}&quot;, log.getTransactionHash());</span>
<span class="fc" id="L227">              Event event =</span>
<span class="fc" id="L228">                  convertEthLogToEventEntity(log)</span>
<span class="fc" id="L229">                      .withBlockTimestamp(block.getTimestamp().longValue());</span>
<span class="fc" id="L230">              logger.info(&quot;Event parsed tx_hash={}, name={}&quot;, event.transactionHash, event.name);</span>
<span class="fc" id="L231">              events.add(event);</span>
<span class="fc" id="L232">            } catch (Exception e) {</span>
<span class="fc" id="L233">              logger.error(&quot;Error processing log for transaction {}&quot;, log.getTransactionHash());</span>
<span class="fc" id="L234">              throw e;</span>
<span class="fc" id="L235">            }</span>
<span class="fc" id="L236">          }</span>
<span class="fc" id="L237">        } catch (Exception e) {</span>
<span class="fc" id="L238">          logger.error(&quot;Error processing transaction&quot;, e.getMessage());</span>
<span class="fc" id="L239">          throw e;</span>
<span class="fc" id="L240">        }</span>
<span class="fc" id="L241">      }</span>
<span class="fc" id="L242">    } catch (Exception e) {</span>
<span class="fc" id="L243">      logger.error(&quot;Error converting block to events: {}&quot;, e.getMessage());</span>
<span class="fc" id="L244">      throw e;</span>
<span class="fc" id="L245">    }</span>

<span class="fc" id="L247">    return events;</span>
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    try {
      // Get ABI event definition for the log
<span class="fc" id="L260">      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);</span>
<span class="fc bfc" id="L261" title="All 2 branches covered.">      if (abiEvent == null) {</span>
<span class="fc" id="L262">        logger.info(&quot;Event definition not found in ABI&quot;);</span>
<span class="fc" id="L263">        throw new Exception(&quot;Event definition not found in ABI&quot;);</span>
      }

      // Get contract ABI event to access parameter names
<span class="fc" id="L267">      var contractAbiEvent = abiParser.getContractAbiEventByLog(ethLog);</span>

      // Extract event parameters using Web3j's utilities
<span class="fc" id="L270">      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);</span>
<span class="fc bfc" id="L271" title="All 2 branches covered.">      if (eventValues == null) {</span>
<span class="fc" id="L272">        logger.info(&quot;No event values found for log: {}&quot;, ethLog);</span>
<span class="fc" id="L273">        throw new Exception(&quot;No event values found for log&quot;);</span>
      }

<span class="fc" id="L276">      List&lt;Type&gt; indexedParameters = eventValues.getIndexedValues();</span>
<span class="fc" id="L277">      List&lt;Type&gt; nonIndexedParameters = eventValues.getNonIndexedValues();</span>
<span class="fc" id="L278">      Map&lt;Boolean, List&lt;AbiParser.AbiEventInput&gt;&gt; groupedInputs =</span>
<span class="fc" id="L279">          contractAbiEvent.getInputs().stream()</span>
<span class="fc" id="L280">              .collect(</span>
<span class="fc" id="L281">                  Collectors.groupingBy(</span>
<span class="fc" id="L282">                      AbiParser.AbiEventInput::isIndexed, LinkedHashMap::new, Collectors.toList()));</span>

<span class="fc" id="L284">      Map&lt;String, Object&gt; indexedValues =</span>
<span class="fc" id="L285">          decodeEventParameters(</span>
<span class="fc" id="L286">              indexedParameters, groupedInputs.getOrDefault(Boolean.TRUE, Collections.emptyList()));</span>
<span class="fc" id="L287">      Map&lt;String, Object&gt; nonIndexedValues =</span>
<span class="fc" id="L288">          decodeEventParameters(</span>
              nonIndexedParameters,
<span class="fc" id="L290">              groupedInputs.getOrDefault(Boolean.FALSE, Collections.emptyList()));</span>

<span class="fc" id="L292">      String indexedJson = objectMapper.writeValueAsString(indexedValues);</span>
<span class="fc" id="L293">      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);</span>

      // Serialize log to JSON
<span class="fc" id="L296">      String logJson = objectMapper.writeValueAsString(ethLog);</span>

      // Create and return new Event entity
<span class="fc" id="L299">      return Event.builder()</span>
<span class="fc" id="L300">          .name(abiEvent.getName())</span>
<span class="fc" id="L301">          .transactionHash(ethLog.getTransactionHash())</span>
<span class="fc" id="L302">          .logIndex((int) ethLog.getLogIndex().longValue())</span>
<span class="fc" id="L303">          .indexedValues(indexedJson)</span>
<span class="fc" id="L304">          .nonIndexedValues(nonIndexedJson)</span>
<span class="fc" id="L305">          .log(logJson)</span>
<span class="fc" id="L306">          .build();</span>
<span class="fc" id="L307">    } catch (Exception e) {</span>
<span class="fc" id="L308">      logger.error(&quot;Error converting log to event entity&quot;, e);</span>
<span class="fc" id="L309">      return null;</span>
    }
  }

  /**
   * Decodes a list of event parameter values based on their corresponding ABI event input
   * definitions.
   *
   * &lt;p&gt;This method maps the decoded values to their respective parameter names as defined in the
   * ABI, supporting complex types such as tuples, tuple arrays, and dynamic arrays.
   *
   * @param values the list of decoded {@link Type} values from the event log
   * @param inputs the list of expected ABI event input definitions
   * @return a map of parameter names to their decoded values, preserving the order of inputs
   */
  private Map&lt;String, Object&gt; decodeEventParameters(
      List&lt;Type&gt; values, List&lt;AbiParser.AbiEventInput&gt; inputs) {
<span class="fc" id="L326">    Map&lt;String, Object&gt; result = new LinkedHashMap&lt;&gt;();</span>
<span class="fc bfc" id="L327" title="All 8 branches covered.">    if (values == null || values.isEmpty() || inputs == null || inputs.isEmpty()) {</span>
<span class="fc" id="L328">      return result;</span>
    }
<span class="fc" id="L330">    int index = 0;</span>
<span class="fc bfc" id="L331" title="All 2 branches covered.">    for (var input : inputs) {</span>
<span class="fc bfc" id="L332" title="All 2 branches covered.">      if (index &gt;= values.size()) break;</span>

<span class="fc" id="L334">      String name = input.getName();</span>
<span class="fc" id="L335">      Object value = values.get(index).getValue();</span>

<span class="fc bfc" id="L337" title="All 4 branches covered.">      if (TUPLE_TYPE.equals(input.getType()) &amp;&amp; value instanceof List&lt;?&gt; list) {</span>
        // Handle tuple type: tuple
<span class="fc" id="L339">        result.put(name, decodeTuple(list, input.getComponents()));</span>
<span class="fc bfc" id="L340" title="All 4 branches covered.">      } else if (TUPLE_ARRAY_TYPE.equals(input.getType()) &amp;&amp; value instanceof List&lt;?&gt; list) {</span>
        // Handle tuple array type: tuple[]
<span class="fc" id="L342">        result.put(name, decodeTupleArray(list, input.getComponents()));</span>
<span class="fc bfc" id="L343" title="All 2 branches covered.">      } else if (input.getType() != null</span>
<span class="fc bfc" id="L344" title="All 2 branches covered.">          &amp;&amp; input.getType().endsWith(&quot;[]&quot;)</span>
<span class="fc bfc" id="L345" title="All 2 branches covered.">          &amp;&amp; values.get(index) instanceof DynamicArray&lt;?&gt;) {</span>
        // Handle dynamic array type: uint8[], bytes32[], etc.
<span class="fc" id="L347">        result.put(name, decodeDynamicArray(value));</span>
      } else {
        // Handle basic types: unit8, bytes32, etc.
<span class="fc" id="L350">        result.put(name, value);</span>
      }

<span class="fc" id="L353">      index++;</span>
<span class="fc" id="L354">    }</span>
<span class="fc" id="L355">    return result;</span>
  }

  /**
   * Decode Tuple
   *
   * @param list a list of Type objects
   * @param components a list of AbiEventInput objects
   * @return a map of strings to objects
   */
  private Map&lt;String, Object&gt; decodeTuple(List&lt;?&gt; list, List&lt;AbiParser.AbiEventInput&gt; components) {
<span class="fc" id="L366">    Map&lt;String, Object&gt; tupleResult = new LinkedHashMap&lt;&gt;();</span>
<span class="fc bfc" id="L367" title="All 8 branches covered.">    if (list == null || list.isEmpty() || components == null || components.isEmpty()) {</span>
<span class="fc" id="L368">      return tupleResult;</span>
    }
<span class="fc bfc" id="L370" title="All 2 branches covered.">    for (int i = 0; i &lt; components.size(); i++) {</span>
<span class="fc" id="L371">      String compName = components.get(i).getName();</span>
<span class="fc" id="L372">      Object compValue = ((Type&lt;?&gt;) list.get(i)).getValue();</span>

<span class="fc bfc" id="L374" title="All 4 branches covered.">      if (TUPLE_TYPE.equals(components.get(i).getType())</span>
<span class="fc" id="L375">          &amp;&amp; compValue instanceof List&lt;?&gt; nestedList) {</span>
<span class="fc" id="L376">        tupleResult.put(compName, decodeTuple(nestedList, components.get(i).getComponents()));</span>
      } else {
<span class="fc bfc" id="L378" title="All 2 branches covered.">        if (list.get(i) instanceof DynamicArray&lt;?&gt;) {</span>
<span class="fc" id="L379">          tupleResult.put(compName, decodeDynamicArray(compValue));</span>
        } else {
<span class="fc" id="L381">          tupleResult.put(compName, compValue);</span>
        }
      }
    }
<span class="fc" id="L385">    return tupleResult;</span>
  }

  /**
   * Decode Tuple Array
   *
   * @param list a list of Type objects representing an array of tuples
   * @param components a list of AbiEventInput objects defining the tuple structure
   * @return a list of maps, each representing a decoded tuple
   */
  private List&lt;Map&lt;String, Object&gt;&gt; decodeTupleArray(
      List&lt;?&gt; list, List&lt;AbiParser.AbiEventInput&gt; components) {
<span class="fc" id="L397">    List&lt;Map&lt;String, Object&gt;&gt; tupleArrayResult = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L398" title="All 8 branches covered.">    if (list == null || list.isEmpty() || components == null || components.isEmpty()) {</span>
<span class="fc" id="L399">      return tupleArrayResult;</span>
    }

    // For tuple arrays, the list should contain struct objects (DynamicStruct or StaticStruct)
<span class="fc bfc" id="L403" title="All 2 branches covered.">    for (Object item : list) {</span>
<span class="fc bfc" id="L404" title="All 2 branches covered.">      if (item instanceof DynamicStruct struct) {</span>
<span class="fc" id="L405">        tupleArrayResult.add(decodeTuple(struct.getValue(), components));</span>
<span class="fc bfc" id="L406" title="All 2 branches covered.">      } else if (item instanceof StaticStruct struct) {</span>
<span class="fc" id="L407">        tupleArrayResult.add(decodeTuple(struct.getValue(), components));</span>
      } else {
        // Fallback: treat as a single tuple if not a struct
<span class="fc" id="L410">        logger.warn(&quot;Unexpected tuple array element type: {}&quot;, item.getClass().getSimpleName());</span>
<span class="fc" id="L411">        Map&lt;String, Object&gt; singleTuple = new LinkedHashMap&lt;&gt;();</span>
<span class="fc bfc" id="L412" title="All 2 branches covered.">        if (item instanceof List&lt;?&gt; itemList) {</span>
<span class="fc" id="L413">          singleTuple = decodeTuple(itemList, components);</span>
        }
<span class="fc" id="L415">        tupleArrayResult.add(singleTuple);</span>
      }
<span class="fc" id="L417">    }</span>

<span class="fc" id="L419">    return tupleArrayResult;</span>
  }

  /**
   * Decode Dynamic Array
   *
   * @param value an ArrayList of Type objects
   * @return a list of objects
   */
  private List&lt;Object&gt; decodeDynamicArray(Object value) {
<span class="fc" id="L429">    ArrayList&lt;Type&gt; dynamicArray = (ArrayList&lt;Type&gt;) value;</span>
<span class="fc" id="L430">    return dynamicArray.stream().map(Type::getValue).toList();</span>
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
<span class="fc" id="L442">    Web3j web3j = web3jConfig.getWeb3j();</span>

    try {
<span class="fc" id="L445">      return web3j</span>
<span class="fc" id="L446">          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)</span>
<span class="fc" id="L447">          .send()</span>
<span class="fc" id="L448">          .getBlock()</span>
<span class="fc" id="L449">          .getTimestamp()</span>
<span class="fc" id="L450">          .longValue();</span>
    } finally {
      // Shutdown the Web3j instance to free resources
<span class="fc" id="L453">      web3j.shutdown();</span>
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public List&lt;Transaction&gt; getPendingTransactions(long blockHeight) {
<span class="fc" id="L464">    return getPendingTransactions(blockHeight, false);</span>
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public List&lt;Transaction&gt; getPendingTransactions(long blockHeight, boolean forceOuterError) {
    try {
      // Create a new Web3j instance for this operation
<span class="fc" id="L479">      Web3j web3j = web3jConfig.getWeb3j();</span>

      // Create filter to get logs from the specified block height
<span class="fc" id="L482">      EthFilter filter =</span>
          new EthFilter(
<span class="fc" id="L484">              DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),</span>
<span class="fc" id="L485">              DefaultBlockParameter.valueOf(&quot;latest&quot;),</span>
<span class="fc" id="L486">              Collections.emptyList());</span>

      // Get logs synchronously
<span class="fc" id="L489">      List&lt;EthLog.LogResult&gt; filterLogs = web3j.ethGetLogs(filter).send().getLogs();</span>

<span class="fc" id="L491">      logger.info(</span>
<span class="fc" id="L492">          &quot;Retrieved {} logs from block height {} to latest&quot;, filterLogs.size(), blockHeight);</span>

      // Collect block numbers
<span class="fc" id="L495">      List&lt;BigInteger&gt; blockNumbers =</span>
<span class="fc" id="L496">          filterLogs.stream().map(result -&gt; (Log) result.get()).map(Log::getBlockNumber).toList();</span>

      // Fetch timestamps per block
<span class="fc" id="L499">      Map&lt;BigInteger, BigInteger&gt; blockTimestamps = new HashMap&lt;&gt;();</span>
<span class="fc bfc" id="L500" title="All 2 branches covered.">      for (BigInteger blockNumber : blockNumbers) {</span>
<span class="fc" id="L501">        EthBlock block =</span>
<span class="fc" id="L502">            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();</span>
<span class="fc" id="L503">        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());</span>
<span class="fc" id="L504">      }</span>

<span class="fc bfc" id="L506" title="All 2 branches covered.">      if (forceOuterError) {</span>
<span class="fc" id="L507">        throw new RuntimeException(&quot;Forced error in outer catch block for testing&quot;);</span>
      }

<span class="fc" id="L510">      return filterLogs.stream()</span>
<span class="fc" id="L511">          .map(</span>
              logResult -&gt; {
                try {
<span class="fc" id="L514">                  Log ethLog = (Log) logResult.get();</span>
<span class="fc" id="L515">                  logger.info(&quot;Event found tx_hash={}&quot;, ethLog.getTransactionHash());</span>

<span class="fc" id="L517">                  Event event =</span>
<span class="fc" id="L518">                      convertEthLogToEventEntity(ethLog)</span>
<span class="fc" id="L519">                          .withBlockTimestamp(</span>
<span class="fc" id="L520">                              blockTimestamps.get(ethLog.getBlockNumber()).longValue());</span>
<span class="fc" id="L521">                  logger.info(</span>
                      &quot;Event parsed tx_hash={}, name={}&quot;, event.transactionHash, event.name);

                  BlockHeight height =
<span class="fc" id="L525">                      BlockHeight.builder()</span>
<span class="fc" id="L526">                          .blockNumber(ethLog.getBlockNumber().longValue())</span>
<span class="fc" id="L527">                          .build();</span>

<span class="fc" id="L529">                  return Transaction.builder()</span>
<span class="fc" id="L530">                      .events(Collections.singletonList(event))</span>
<span class="fc" id="L531">                      .blockHeight(height)</span>
<span class="fc" id="L532">                      .build();</span>
<span class="fc" id="L533">                } catch (Exception e) {</span>
<span class="fc" id="L534">                  logger.error(&quot;Error processing individual log&quot;, e);</span>
<span class="fc" id="L535">                  return null;</span>
                }
              })
<span class="fc" id="L538">          .filter(Objects::nonNull)</span>
<span class="fc" id="L539">          .toList();</span>

<span class="fc" id="L541">    } catch (Exception e) {</span>
<span class="fc" id="L542">      logger.error(&quot;Error getting filtered logs&quot;, e);</span>
<span class="fc" id="L543">      throw new RuntimeException(&quot;Error getting filtered logs&quot;, e);</span>
    }
  }

  public void unsubscribe() {
<span class="fc bfc" id="L548" title="All 2 branches covered.">    if (subscription != null) {</span>
<span class="fc" id="L549">      subscription.dispose();</span>
    }
<span class="fc" id="L551">  }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>