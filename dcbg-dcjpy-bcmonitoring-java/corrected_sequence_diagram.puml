@startuml

!theme cerulean

autonumber
participant BESU         as besu
participant BCMonitoring as bcm
database S3 as s3
database DynamoDB as Dynamo
activate bcm

group 初期化処理
    bcm -> s3 : バケット配下の全ゾーン情報分の ABI JSON を全て取得
    note right
      ダウンロードした ABI JSON は 
      コントラクトアドレス をキーとし、
      ContractEvents（コントラクト名とイベント情報）
      をValueとしたKey-Value型で保持
    end note
    
    group Monitoring Loop
        bcm -> Dynamo : 検知済イベントの最終 Block 高を取得
        
        create control NewBlockSubscription as subscription
        bcm -> subscription : 新しいブロック通知を受け取るためにSubscribeする
        note right
          Web3j WebSocketでnewHeadsNotificationsを購読
          新しいブロックは非同期でキューに蓄積される
        end note
        
        bcm -> besu : 過去ブロック（最終Block高+1〜現在）のログを一括取得
        besu -> bcm : 過去ブロックのイベントログを返却
        
        group 過去ブロック処理（順次処理）
            loop 過去ブロックのTransactionごと
                alt S3 から取得した ABI JSON に含まれるイベントである場合
                    bcm -> Dynamo : Event レコードを Put
                end
                alt Block高が変わった場合
                    bcm -> Dynamo : Block 高レコードを Put
                end
            end
            bcm -> Dynamo : 最終 Block 高レコードを Put
            note right
              過去ブロック処理が完全に終了してから
              新しいブロック処理を開始
            end note
        end
        
        group 新しいブロック処理（継続的処理）
            loop ContextConfig.isServiceRunning()がtrueの間
                subscription -> bcm : キューから新しいTransactionを取得（5秒間隔でポーリング）
                alt Transactionが存在する場合
                    alt S3 から取得した ABI JSON に含まれるイベントである場合
                        bcm -> Dynamo : Event レコードを Put
                        bcm -> Dynamo : Block 高レコードを Put
                    end
                end
            end
        end
        
        alt エラー発生
            bcm -> subscription : unsubscribe()を実行
            bcm -> bcm : checkInterval分sleep後、BCMonitoringを再起動する
        end
    end
end

besu -> besu : event を emit
note right
  新しいイベントは非同期で
  subscriptionのキューに蓄積される
end note

deactivate bcm
@enduml
