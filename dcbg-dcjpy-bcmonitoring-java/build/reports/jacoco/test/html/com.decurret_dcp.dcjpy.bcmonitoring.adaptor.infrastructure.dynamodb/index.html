<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <span class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</span></div><h1>com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 317</td><td class="ctr2">100%</td><td class="bar">0 of 6</td><td class="ctr2">100%</td><td class="ctr1">0</td><td class="ctr2">14</td><td class="ctr1">0</td><td class="ctr2">93</td><td class="ctr1">0</td><td class="ctr2">11</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a0"><a href="BlockHeightDao.html" class="el_class">BlockHeightDao</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="191" alt="191"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">53</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">6</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="EventDao.html" class="el_class">EventDao</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="70" alt="70"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">3</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="PooledDynamoDbService.html" class="el_class">PooledDynamoDbService</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="56" alt="56"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">2</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>