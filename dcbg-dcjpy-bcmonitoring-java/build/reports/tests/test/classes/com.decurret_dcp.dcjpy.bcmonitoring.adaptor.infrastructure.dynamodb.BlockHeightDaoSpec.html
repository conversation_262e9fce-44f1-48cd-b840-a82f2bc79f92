<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - BlockHeightDaoSpec</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>BlockHeightDaoSpec</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a> &gt; BlockHeightDaoSpec</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.296s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should handle exception when saving block height fails</td>
<td class="success">0.101s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle generic Exception during get operation</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle generic Exception during save operation</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should return 0 when no block heights found</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should return true when saving null block height</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should successfully get block height</td>
<td class="success">1.117s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should successfully save block height</td>
<td class="success">0.039s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should throw DataAccessException when get fails</td>
<td class="success">0.019s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
