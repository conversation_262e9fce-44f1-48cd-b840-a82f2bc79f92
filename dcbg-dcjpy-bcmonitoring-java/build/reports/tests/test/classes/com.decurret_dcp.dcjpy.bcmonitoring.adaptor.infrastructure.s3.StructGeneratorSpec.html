<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - StructGeneratorSpec</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>StructGeneratorSpec</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.html">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; StructGeneratorSpec</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">17</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.021s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">constructor should be callable for completeness</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create DynamicStruct for dynamic types</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create DynamicStruct when DynamicArray is present</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create DynamicStruct when DynamicBytes is present</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create DynamicStruct when DynamicStruct is present</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create StaticStruct for static types</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create constructor with correct parameters</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should create unique class names</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should handle empty field types list</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should handle mixed static and dynamic types</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateStructClass should handle single field type</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should handle null parameter</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should return false for static types</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should return true for DynamicArray</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should return true for DynamicBytes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should return true for DynamicStruct</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isDynamic should return true for Utf8String</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at 2025/07/30 17:39:40</p>
</div>
</div>
</body>
</html>
