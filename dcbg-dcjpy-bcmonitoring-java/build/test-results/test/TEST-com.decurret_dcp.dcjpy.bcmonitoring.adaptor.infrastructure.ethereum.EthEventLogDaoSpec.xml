<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" tests="92" skipped="0" failures="0" errors="0" timestamp="2025-07-30T10:39:29.530Z" hostname="tungvt.local" time="9.09">
  <properties/>
  <testcase name="convBlock2EventEntities should handle empty transaction lists" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.544"/>
  <testcase name="convBlock2EventEntities should handle missing transaction receipts" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.085"/>
  <testcase name="convBlock2EventEntities should process transactions with valid logs" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.098"/>
  <testcase name="convBlock2EventEntities should include events with null transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.005"/>
  <testcase name="convBlock2EventEntities should handle exceptions during log processing" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.01"/>
  <testcase name="convBlock2EventEntities should handle exceptions during transaction processing" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.004"/>
  <testcase name="convBlock2EventEntities should include events with empty transaction hash" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.004"/>
  <testcase name="getPendingTransactions should handle exceptions during event processing" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.05"/>
  <testcase name="isDelayed should detect delayed blocks" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.018"/>
  <testcase name="decodeTupleArray should handle regular array inside tuple array" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.031"/>
  <testcase name="getPendingTransactions should process logs and return transactions" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.018"/>
  <testcase name="getPendingTransactions should process logs with valid data" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.007"/>
  <testcase name="getPendingTransactions should handle log processing errors" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.011"/>
  <testcase name="getPendingTransactions should handle general log processing errors" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.011"/>
  <testcase name="getPendingTransactions should handle exceptions" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.005"/>
  <testcase name="getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.012"/>
  <testcase name="getPendingTransactions should process a log entry correctly" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.009"/>
  <testcase name="should get block timestamp correctly" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.004"/>
  <testcase name="subscribeAll should subscribe to contract events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.005"/>
  <testcase name="subscribeAll should subscribe to block events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.003"/>
  <testcase name="subscribeAll should skip processing for delayed blocks" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should process non-delayed blocks with events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="subscribeAll should handle exceptions during block processing with events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should events is empty when processing with events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="subscribeAll should add transaction to queue when events are found" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should not add transaction to queue when no events are found" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="convertEthLogToEventEntity should successfully convert a log to an event with ABI event" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.193"/>
  <testcase name="convertEthLogToEventEntity should failed convert a log with EventValues is null " classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.004"/>
  <testcase name="convertEthLogToEventEntity should handle null ABI event" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="convertEthLogToEventEntity should handle block retrieval exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="convertEthLogToEventEntity should handle ABI parser exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="convertEthLogToEventEntity should handle empty topics list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="convBlock2EventEntities should process events from a block with logs" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.003"/>
  <testcase name="subscribeAll should handle NumberFormatException when parsing allowable timestamp difference" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="subscribeAll should log subscription error" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.005"/>
  <testcase name="subscribeAll should handle exception during Web3j subscription creation" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="unsubscribe should dispose subscription when subscription is not null" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="unsubscribe should handle null subscription gracefully" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="getBlockTimestamp should return correct timestamp" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="getBlockTimestamp should handle IOException" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.003"/>
  <testcase name="convBlock2EventEntities should handle Web3j creation exception" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="convertEthLogToEventEntity should handle general exceptions during processing" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should handle subscription callback with delayed block" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.147"/>
  <testcase name="subscribeAll should handle subscription callback with non-delayed block and events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should handle subscription callback with empty events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should handle subscription callback exception during block processing" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.108"/>
  <testcase name="subscribeAll should handle subscription completion" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.004"/>
  <testcase name="subscribeAll should execute subscription callback and process block" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.018"/>
  <testcase name="subscribeAll should execute subscription callback and add transaction to queue" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.019"/>
  <testcase name="subscribeAll should handle empty events in subscription callback" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.034"/>
  <testcase name="subscribeAll should handle null events in subscription callback" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.016"/>
  <testcase name="subscribeAll should trigger main subscription callback lambda0" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.021"/>
  <testcase name="subscribeAll should trigger InterruptedException in async callback" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.006"/>
  <testcase name="subscribeAll should trigger subscription error callback lambda3" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="subscribeAll should handle error callback with proper cleanup" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.504"/>
  <testcase name="subscribeAll should trigger main subscription callback lambda0 with real execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.122"/>
  <testcase name="subscribeAll should cover remaining async callback paths with forced execution" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.224"/>
  <testcase name="convBlock2EventEntities should handle null transaction" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.005"/>
  <testcase name="getPendingTransactions should handle forced outer error" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.028"/>
  <testcase name="convertEthLogToEventEntity should handle real AddProviderRole event with bytes32 parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.023"/>
  <testcase name="decodeTuple should decode simple tuple with basic types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTuple should handle nested tuple types" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTuple should handle DynamicArray in tuple" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.007"/>
  <testcase name="decodeTuple should handle when tuple types but value of Type is not a list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="decodeTuple should handle null type and null components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTuple should handle [] type and [] components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTuple should handle [Mock for type 'Type'] type and null components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeTuple should handle [Mock for type 'Type'] type and [] components" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTupleArray should handle DynamicStruct and StaticStruct of component" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.008"/>
  <testcase name="decodeTupleArray should fallback decode if item is List&lt;Type&gt;" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="decodeTupleArray should handle null type list and null components list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTupleArray should handle [] type list and [] components list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeTupleArray should handle [Mock for type 'Type'] type list and null components list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeTupleArray should handle [Mock for type 'Type'] type list and [] components list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeDynamicArray should decode array of Type objects" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeEventParameters should handle tuple type parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle tuple type parameters but value type is not a list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle tuple array type parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.002"/>
  <testcase name="decodeEventParameters should handle tuple array type parameters but value type is not a list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeEventParameters should handle null type list and null component list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle [] type list and [] component list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle [Mock for type 'Type'] type list and null component list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle [Mock for type 'Type'] type list and [] component list" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.0"/>
  <testcase name="decodeEventParameters should handle dynamic array type parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.003"/>
  <testcase name="decodeEventParameters should handle basic type parameters" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTupleArray should handle DynamicArray in tuple array" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="decodeTupleArray should handle nested tuple array in tuple array" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.009"/>
  <testcase name="decodeEventParameters should handle more inputs than values" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.001"/>
  <testcase name="convertEthLogToEventEntity should test ObjectMapper serialization functionality" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.034"/>
  <testcase name="convertEthLogToEventEntity should successfully create Event object with real AddProviderRole event" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.008"/>
  <testcase name="subscribeAll should call convBlock2EventEntities when block has transactions" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="0.51"/>
  <testcase name="subscribeAll should log 'detect block includes events' when convBlock2EventEntities returns events" classname="com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDaoSpec" time="1.019"/>
  <system-out><![CDATA[AddProviderRole event signature: 0xc00b7969abad8afb874b50ac102795cc3b944b19c764c2768308474fb772aa5b
Test successful - Indexed values: null
Test successful - Non-indexed values: null
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
