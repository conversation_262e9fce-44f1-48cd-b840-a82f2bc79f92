envName: stage-fin-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/stage-fin-a-core
    tag: "9f81c4e"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-a-osaka-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-3:************:targetgroup/stage-fin-a-osaka-core-alb-tg/368818de05468e41

configMap:
  DB_BASE: "stage-fin-a-osaka-rds-cluster.cluster-cl8426u8w4ql.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_TBJQ09O5B/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_TBJQ09O5B"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-3"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"