apiVersion: v1
kind: ConfigMap
metadata:
  name: bctracker-balance-envs
  namespace: dcbg-core
data:
  DB_MAXIMUM_POOL_SIZE: "50"
  DB_MINIMUM_IDLE: "10"
  DB_CONNECTION_TIMEOUT: "30000"
  DB_IDLE_TIMEOUT: "600000"
  PUSH_NOTIFICATION_SQS_QUEUE_NAME: "dcjpy_bctracker_queue_balance.fifo"
  BASE_VISIBILITY_TIMEOUT: "5"
  BASE_WAIT_TIME_SECONDS: "1"
  AWS_BALANCE_CACHE_TABLE: "balance_cache"
  BCCLIENT_APP_BASE_URL: "http://bcclient.dcbg-core.svc.cluster.local:8081"
  BCCLIENT_HTTP_CONNECTION_MAX_PER_ROUTE: "50"
  BCCLIENT_HTTP_CONNECTION_MAX_TOTAL: "50"
  BCCLIENT_READ_TIMEOUT_MILLISEC: "20000"
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
