#!/bin/bash -e

# EKS Cluster https://github.com/decurret-lab/dcbg-dcf-terraform-sandbox/blob/b94293e19ba04ee81d4f29c0fe5a7bde035acf09/Modules/EKS/main.tf#L85
CLUSTER_NAME=$1
OPERATOR_VERSION=$2
REGION=$3

if [[ -z "${CLUSTER_NAME}" ]]; then
  echo "error: please set cluster name (e.g. prod-biz-a-eks-cluster)" 1>&2
  exit 1
fi

if [[ -z "${OPERATOR_VERSION}" ]]; then
  echo "error: please set eso version (e.g. 0.9.2)" 1>&2
  exit 1
fi

if [[ -z "${REGION}" ]]; then
  echo "error: please set region (e.g. ap-northeast-1 or ap-northeast-3)" 1>&2
  exit 1
fi

if [ ! -e "$(which helm)" ]; then
  echo "error: helm is not installed or not in the PATH" 1>&2
  exit 1
fi

# chart install
helm repo add external-secrets https://charts.external-secrets.io
helm repo update external-secrets

# chart apply
aws eks update-kubeconfig --name "${CLUSTER_NAME}" --region ${REGION}

echo -e "\nCLUSTER_NAME:\t${CLUSTER_NAME}"
echo -e "OPERATOR_VERSION:\t${OPERATOR_VERSION}\n"

# check the variable to determine 'install' or 'upgrade'
HELM_COMMAND="install"
if [[ ${OPERATOR_UPDATE} -eq 1 ]]; then
  HELM_COMMAND="upgrade"
fi

# Configration https://github.com/external-secrets/external-secrets/tree/main/deploy/charts/external-secrets
COMMAND=$(cat<<-EOF
helm ${HELM_COMMAND} external-secrets external-secrets/external-secrets \
  --version ${OPERATOR_VERSION} \
  --set installCRDs=true \
  --set webhook.port=9443 \
  -n kube-system
EOF)

eval ${COMMAND}
