envName: dev-cl-fin

application:
  image:
    name: nginx
    tag: "1.27.4"
  ports:
    containerPort: 80

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/dev-cl-fin-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/dev-cl-fin-tokyo-core-alb-tg/0463fbf319bcbbed

configMap:
  DB_BASE: "dev-cl-fin-tokyo-rds-cluster.cluster-cvommo6osvll.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_7Wsr96Tri/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_7Wsr96Tri"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
