# core コンポーネント helm chart

ディレクトリ構造とそれぞれのファイルの役割は以下の通り。

```
├── Chart.yaml # チャートの概要が記述されたYAMLファイル(ファイル名は予約)
├── README.md
├── templates  # チャートの本体ともいえる、KubernetesオブジェクトのリソースYAMLのテンプレート群を配置するディレクトリ(ディレクトリ名は予約)
│    ├── _helpers.tpl # テンプレートのヘルパー関数が定義されたファイル
│    ├── configmap-core-envs.yaml
│    ├── deployment.yaml
│    ├── secrets-rds.enc.yaml
│    ├── service-account.yaml
│    ├── service.yaml
│    └── target-group-binding.yaml
├── <環境名1>
│    ├── values-<環境名>-fin.yaml # 環境1の共通領域のパラメータを書いたvaluesファイル
│    └── values-<環境名>-ind.yaml # 環境1の付加領域のパラメータを書いたvaluesファイル
├── <環境名2>
│    ├── values-<環境名>-fin.yaml
│    └── values-<環境名>-ind.yaml
└── values.yaml # ベースとなるパラメータを書いたvaluesファイル(ファイル名は予約)
```
