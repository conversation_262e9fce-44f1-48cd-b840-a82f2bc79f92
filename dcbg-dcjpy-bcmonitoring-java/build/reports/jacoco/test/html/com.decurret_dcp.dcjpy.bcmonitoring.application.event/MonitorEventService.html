<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MonitorEventService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.application.event</a> &gt; <span class="el_class">MonitorEventService</span></div><h1>MonitorEventService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 557</td><td class="ctr2">100%</td><td class="bar">2 of 58</td><td class="ctr2">96%</td><td class="ctr1">2</td><td class="ctr2">39</td><td class="ctr1">0</td><td class="ctr2">128</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a5"><a href="MonitorEventService.java.html#L117" class="el_method">processPendingTransactions(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="96" alt="96"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="MonitorEventService.java.html#L190" class="el_method">saveTransaction(Transaction)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="83" alt="83"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="82" height="10" title="11" alt="11"/></td><td class="ctr2" id="e4">91%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">18</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="MonitorEventService.java.html#L229" class="el_method">savePendingTransaction(Transaction)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="83" height="10" title="67" alt="67"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="9" alt="9"/></td><td class="ctr2" id="e5">90%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="MonitorEventService.java.html#L89" class="el_method">monitorEvents()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="82" height="10" title="66" alt="66"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="MonitorEventService.java.html#L159" class="el_method">processNewTransactions(BlockingQueue)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="64" alt="64"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">15</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="MonitorEventService.java.html#L278" class="el_method">fetchTraceId(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="61" alt="61"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i6">11</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="MonitorEventService.java.html#L65" class="el_method">execute()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="57" height="10" title="46" alt="46"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="MonitorEventService.java.html#L32" class="el_method">MonitorEventService(LoggingService, EventLogRepository, EventRepository, BlockHeightRepository, BcmonitoringConfigurationProperties, Web3jConfig, EthEventLogDao, ObjectMapper)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="33" alt="33"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">11</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="MonitorEventService.java.html#L262" class="el_method">savePendingTransactionBlockNumber(BlockHeight)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="33" alt="33"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="MonitorEventService.java.html#L303" class="el_method">sleep(int)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>