apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "bcmonitoring-envs"
  namespace: "dcbg-core"
data:
  ENV: "prod"
  WEBSOCKET_URI_PORT: "8541"
  EVENTS_TABLE_NAME: "Events"
  BLOCK_HEIGHT_TABLE_NAME: "BlockHeight"
  SUBSCRIPTION_CHECK_INTERVAL: "3000"
  ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC: "4"
  ABI_FORMAT: "hardhat"
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
