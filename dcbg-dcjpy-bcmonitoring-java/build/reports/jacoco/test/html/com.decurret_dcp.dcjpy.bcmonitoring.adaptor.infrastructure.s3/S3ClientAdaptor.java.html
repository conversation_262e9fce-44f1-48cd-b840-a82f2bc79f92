<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>S3ClientAdaptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3</a> &gt; <span class="el_source">S3ClientAdaptor.java</span></div><h1>S3ClientAdaptor.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3CommonPrefixesListingException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

@Component
public class S3ClientAdaptor implements S3AbiRepository {
  private final S3Client s3Client;
  private final LoggingService logger;

<span class="fc" id="L18">  public S3ClientAdaptor(S3Client s3Client, LoggingService logger) {</span>
<span class="fc" id="L19">    this.s3Client = s3Client;</span>
<span class="fc" id="L20">    this.logger = logger;</span>
<span class="fc" id="L21">  }</span>

  /**
   * List common prefixes of objects in a bucket.
   *
   * @param bucketName The name of the S3 bucket.
   * @param delimiter The delimiter to use for grouping objects.
   * @return A list of common prefixes.
   * @throws S3CommonPrefixesListingException if an error occurs while listing objects.
   */
  @Override
  public List&lt;CommonPrefix&gt; listCommonPrefixesObjects(String bucketName, String delimiter) {
    try {
      ListObjectsV2Request request =
<span class="fc" id="L35">          ListObjectsV2Request.builder().bucket(bucketName).delimiter(delimiter).build();</span>

<span class="fc" id="L37">      ListObjectsV2Response response = s3Client.listObjectsV2(request);</span>
<span class="fc" id="L38">      return response.commonPrefixes();</span>
<span class="fc" id="L39">    } catch (software.amazon.awssdk.services.s3.model.S3Exception e) {</span>
<span class="fc" id="L40">      String errorMessage = &quot;Error listing common prefixes in bucket: &quot; + bucketName;</span>
<span class="fc" id="L41">      logger.error(errorMessage, e);</span>
<span class="fc" id="L42">      throw new S3CommonPrefixesListingException(errorMessage, e);</span>
    }
  }

  /**
   * List objects in a bucket with a specific prefix.
   *
   * @param bucketName The name of the S3 bucket.
   * @param prefix The prefix to filter objects.
   * @return A ListObjectsV2Response containing the objects.
   * @throws S3Exception if an error occurs while listing objects.
   */
  @Override
  public ListObjectsV2Response listObjects(String bucketName, String prefix) {
    try {
      ListObjectsV2Request request =
<span class="fc" id="L58">          ListObjectsV2Request.builder().bucket(bucketName).prefix(prefix).build();</span>

<span class="fc" id="L60">      return s3Client.listObjectsV2(request);</span>
<span class="fc" id="L61">    } catch (software.amazon.awssdk.services.s3.model.S3Exception e) {</span>
<span class="fc" id="L62">      String errorMessage =</span>
          &quot;Error listing objects with prefix '&quot; + prefix + &quot;' in bucket: &quot; + bucketName;
<span class="fc" id="L64">      logger.error(errorMessage, e);</span>
<span class="fc" id="L65">      throw new S3Exception(errorMessage, e);</span>
    }
  }

  /**
   * Get an object from S3.
   *
   * @param bucketName The name of the S3 bucket.
   * @param key The key of the object to retrieve.
   * @return An InputStream for the object.
   * @throws S3Exception if an error occurs while getting the object.
   */
  @Override
  public InputStream getObject(String bucketName, String key) {
    try {
<span class="fc" id="L80">      GetObjectRequest request = GetObjectRequest.builder().bucket(bucketName).key(key).build();</span>
<span class="fc" id="L81">      return new ByteArrayInputStream(s3Client.getObject(request).readAllBytes());</span>
<span class="fc" id="L82">    } catch (software.amazon.awssdk.services.s3.model.S3Exception e) {</span>
<span class="fc" id="L83">      String errorMessage =</span>
          &quot;Error getting object with key '&quot; + key + &quot;' from bucket: &quot; + bucketName;
<span class="fc" id="L85">      logger.error(errorMessage, e);</span>
<span class="fc" id="L86">      throw new S3Exception(errorMessage, e);</span>
<span class="fc" id="L87">    } catch (Exception e) {</span>
<span class="fc" id="L88">      String errorMessage =</span>
          &quot;Error reading object data for key '&quot; + key + &quot;' from bucket: &quot; + bucketName;
<span class="fc" id="L90">      logger.error(errorMessage, e);</span>
<span class="fc" id="L91">      throw new S3Exception(errorMessage, e);</span>
    }
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>