<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="ja"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PooledDynamoDbService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dcbg-dcjpy-bcmonitoring</a> &gt; <a href="index.source.html" class="el_package">com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb</a> &gt; <span class="el_source">PooledDynamoDbService.java</span></div><h1>PooledDynamoDbService.java</h1><pre class="source lang-java linenums">package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.DynamoDBConnectionPool;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.function.Function;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;

/**
 * Service that provides pooled DynamoDB operations This service manages connection acquisition and
 * release automatically
 */
@Service
public class PooledDynamoDbService {

  private final DynamoDBConnectionPool connectionPool;
  private final LoggingService logger;

<span class="fc" id="L20">  public PooledDynamoDbService(DynamoDBConnectionPool connectionPool, LoggingService logger) {</span>
<span class="fc" id="L21">    this.connectionPool = connectionPool;</span>
<span class="fc" id="L22">    this.logger = logger;</span>
<span class="fc" id="L23">  }</span>

  /**
   * Execute a DynamoDB operation with automatic connection management
   *
   * @param operation Function that takes DynamoDbClient and returns result
   * @param &lt;T&gt; Return type of the operation
   * @return Result of the operation
   * @throws DynamoDbException if DynamoDB operation fails
   * @throws RuntimeException if connection management fails
   */
  public &lt;T&gt; T executeWithConnection(Function&lt;DynamoDbClient, T&gt; operation) {
<span class="fc" id="L35">    DynamoDbClient client = null;</span>
    try {
      // Acquire connection from pool
<span class="fc" id="L38">      client = connectionPool.acquireConnection();</span>

      // Execute the operation
<span class="fc" id="L41">      return operation.apply(client);</span>

<span class="fc" id="L43">    } catch (InterruptedException e) {</span>
<span class="fc" id="L44">      Thread.currentThread().interrupt();</span>
<span class="fc" id="L45">      throw new RuntimeException(&quot;Thread interrupted while acquiring DynamoDB connection&quot;, e);</span>
<span class="fc" id="L46">    } catch (DynamoDbException e) {</span>
<span class="fc" id="L47">      logger.error(&quot;DynamoDB operation failed&quot;, e);</span>
<span class="fc" id="L48">      throw e;</span>
<span class="fc" id="L49">    } catch (Exception e) {</span>
<span class="fc" id="L50">      logger.error(&quot;Unexpected error during DynamoDB operation&quot;, e);</span>
<span class="fc" id="L51">      throw new RuntimeException(&quot;DynamoDB operation failed&quot;, e);</span>
    } finally {
      // Always release connection back to pool
<span class="fc bfc" id="L54" title="All 2 branches covered.">      if (client != null) {</span>
<span class="fc" id="L55">        connectionPool.releaseConnection(client);</span>
      }
    }
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>