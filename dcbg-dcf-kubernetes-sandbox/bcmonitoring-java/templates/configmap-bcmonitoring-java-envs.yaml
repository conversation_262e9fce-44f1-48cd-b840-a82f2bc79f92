apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "bcmonitoring-java-envs"
  namespace: "dcbg-core"
data:
  ENV: "prod"
  SPRING_PROFILES_ACTIVE: "prod"
  LOGGING_LEVEL_ROOT: "INFO"
  LOGGING_LEVEL_COM_DECURRET: "DEBUG"
  WEBSOCKET_URI_PORT: "8541"
  EVENTS_TABLE_NAME: "Events"
  BLOCK_HEIGHT_TABLE_NAME: "BlockHeight"
  SUBSCRIPTION_CHECK_INTERVAL: "3000"
  ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC: "4"
  ABI_FORMAT: "hardhat"
  JAVA_OPTS: "-Xms1g -Xmx2g -XX:+UseG1GC"
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
