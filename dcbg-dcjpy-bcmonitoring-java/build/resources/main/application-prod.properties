# Production Environment Configuration
spring.application.name=Bcmonitoring

server.port=2345

# AWS Configuration for Production Environment
aws.region=${PROD_AWS_REGION:ap-northeast-1}
aws.access-key-id=${PROD_AWS_ACCESS_KEY:prod-access-key}
aws.secret-access-key=${PROD_AWS_SECRET_KEY:prod-secret-key}
aws.dynamodb.region=${PROD_DYNAMODB_REGION:ap-northeast-1}
aws.dynamodb.table-prefix=${PROD_DYNAMODB_TABLE_PREFIX:prod}
aws.dynamodb.endpoint=${PROD_DYNAMODB_ENDPOINT:https://dynamodb.ap-northeast-1.amazonaws.com}
aws.s3.bucket-name=${PROD_S3_BUCKET_NAME:abijson-prod-bucket}
aws.s3.region=${PROD_S3_REGION:ap-northeast-1}
aws.dynamodb.events-table-name=${PROD_EVENTS_TABLE_NAME:Events}
aws.dynamodb.block-height-table-name=${PROD_BLOCK_HEIGHT_TABLE_NAME:BlockHeight}

# WebSocket Configuration for Production
websocket.uri.host=${PROD_WEBSOCKET_URI_HOST:mainnet-websocket.example.com}
websocket.uri.port=${PROD_WEBSOCKET_URI_PORT:443}

# Subscription Configuration for Production
subscription.check-interval=${PROD_SUBSCRIPTION_CHECK_INTERVAL:3000}
subscription.allowable-block-timestamp-diff-sec=${PROD_ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC:2}

# Environment Configuration
env=prod
abi-format=${PROD_ABI_FORMAT:hardhat}
eager-start=${PROD_EAGER_START:true}

# LocalStack Configuration (not used in prod, but kept for compatibility)
local-stack.end-point=${PROD_LOCALSTACK_ENDPOINT:http://localhost:4566}
local-stack.region=${PROD_LOCALSTACK_REGION:ap-northeast-1}
local-stack.access-key=${PROD_LOCALSTACK_ACCESS_KEY:access123}
local-stack.secret-key=${PROD_LOCALSTACK_SECRET_KEY:secret123}
